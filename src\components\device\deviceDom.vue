<template>
  <div
    class="cabinet"
    ref="cabinet"
    @click.stop="clickMagnifiedImage($event, 'cabinet')"
  >
    <div class="cabinetList">
      <div class="cabinetName">
        <div class="name">设备名称</div>
        <div>设备状态</div>
      </div>
      <div
        :class="[
          {
            cabinetItem: true,
            cabinetBoxHover: props.mouseoverData.id == item.id,
            cabinetBoxClick: props.selectedData.id == item.id,
          },
        ]"
        v-for="item in cabinetTab.resources"
        :key="item.id"
        @mouseover="emit('setMagnified', item)"
        @mouseout="emit('setMagnified')"
        @click.stop="clickMagnifiedImage(item, 'cabinetBox')"
      >
        <div class="name">{{ item.name }}</div>
        <div class="status">
          <div :class="`status${props.statusAllData[item.code].class}`">
            {{ props.statusAllData[item.code]?.title }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="cabinetImg"
      :style="{
        height: `${backHeight}px`,
        backgroundImage: `url(${Cabinet})`,
      }"
    >
      <img
        v-for="item in cabinetTab.newResources"
        :key="item.id"
        :src="item.img"
        :class="{
          cabinetImgItem: item.info,
          ImgItem: true,
          ['classID' + item.id]: true,
          mouseoverId: props.mouseoverData.id == item.id,
          cabinetBoxClick: props.selectedData.id == item.id,
        }"
        :style="{
          height: `${imgHeight * item.size}px`,
        }"
        :ref="item.info ? 'magnifiedImg' : ''"
        @mouseover="showMagnifiedImage(item, $event)"
        @mouseout="hideMagnifiedImage"
        @click.stop="clickMagnifiedImage(item, 'cabinetImgItem')"
      />
      <div
        v-if="magnifiedImage"
        class="magnifiedImage animate__zoomIn animate__animated"
        :style="magnifiedImageStyle"
      >
        <img :src="magnifiedImage" alt="Magnified Image" />
        <div class="describe">
          <div>资产名称：{{ magnifiedImageInfo.name }}</div>
          <div>资产编码：{{ magnifiedImageInfo.code }}</div>
          <!-- <div>资产类型：{{ magnifiedImageInfo.resourceTypeName }}</div> -->
          <div>
            资产状态：{{ props.statusAllData[magnifiedImageInfo.code].title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import Cabinet from "@/assets/device/机柜一.png";
import panel from "@/assets/device/面板.png";
import HAA from "@/assets/device/增强新罗兰检测接收机.png";
import HAB from "@/assets/device/频率比对测量设备.png";
import HAC from "@/assets/device/多通道时间间隔计数器.png";
import HAD from "@/assets/device/时间综合测量仪.png";
import HAE from "@/assets/device/网络时间服务器.png";
import HAF from "@/assets/device/频谱干扰测试系统.png";
import HAG from "@/assets/device/脉冲分配放大器.png";
import HAH from "@/assets/device/频率分配放大器.png";
import HAJ from "@/assets/device/数字记录仪.png";
import HAM from "@/assets/device/标准时间复现设备.png";
import HAN from "@/assets/device/网络连接服务器.png";
import HAO from "@/assets/device/网络交换机.png";
import HAP from "@/assets/device/工控机.png";
import HAQ from "@/assets/device/时间间隔计数器.png";
import HAR from "@/assets/device/PDU.png";
import HAS from "@/assets/device/低噪声电源.png";
import HAT from "@/assets/device/显示器.png";
import HAU from "@/assets/device/鼠标键盘.png";
import HAZ from "@/assets/device/硬盘录像机.png";
import HAY from "@/assets/device/kvm.png";
import HAX from "@/assets/device/鼠标键盘.png";
import HBU from "@/assets/device/脉冲分配放大器.png";
import HBT from "@/assets/device/频率分配放大器.png";
const imgMap = {
  Cabinet: Cabinet,
  panel: panel,
  HAA: HAA,
  HAB: HAB,
  HAC: HAC,
  HAD: HAD,
  HAE: HAE,
  HAF: HAF,
  HAG: HAG,
  HAH: HAH,
  HAJ: HAJ,
  HAM: HAM,
  HAN: HAN,
  HAO: HAO,
  HAP: HAP,
  HAQ: HAQ,
  HAR: HAR,
  HAS: HAS,
  HAT: HAT,
  HAU: HAU,
  HAZ: HAZ,
  HAY: HAY,
  HAX: HAX,
  HBU: HBU,
  HBT: HBT,
};
// 黑名单  有写点击不让他去请求数据 问题是没东西
const banList = ["HAU", "HAT"];

const magnifiedImg = ref();
const cabinet = ref(); //图片的位置
const imgHeight = ref(0); // 每一个U 的高度
const backHeight = ref(0); // 背景的高度
const cabinetTab = ref({}); // 当前机柜的数据

const emit = defineEmits(["setMagnified", "setSelectedData"]);
const hideMagnifiedImage = () => {
  emit("setMagnified");
  magnifiedImage.value = null;
};
const props = defineProps({
  cabinetData: {
    type: Object,
    required: true,
    default: () => {
      return {};
    },
  },
  // 设备状态在这里取
  statusAllData: {
    type: Object,
    required: true,
    default: () => {
      return {};
    },
  },
  selectedData: {
    type: Object,
    required: false,
    default: () => {
      return { id: undefined };
    },
  },
  mouseoverData: {
    type: Object,
    required: false,
    default: () => {
      return { id: undefined };
    },
  },
});
const magnifiedImage = ref(null); // 放大的图片
const magnifiedImageInfo = ref({}); // 放大的图片信息
const magnifiedImageStyle = ref({}); // 放大图片的样式
const clickMagnifiedImage = async (event, type) => {
  const objType = {
    // 最外面的点击
    cabinet: async (event) => {
      const targetId = event.target.dataset.id;
      if (targetId && targetId !== "9999") {
        const targetItem = cabinetTab.value.resources.find(
          (item) => item.id == targetId,
        );
        if (targetItem.infoApi) {
          emit("setSelectedData", targetItem);
        }
        // await store.handleSelect(event, "cabinetItem");
      } else {
        emit("setSelectedData");
        emit("setMagnified");
      }
    },
    // 左面板点击
    cabinetBox: async (event) => {
      if (event.infoApi) {
        const selectData = cabinetTab.value.resources.find(
          (item) => item.id == event.id,
        );
        emit("setSelectedData", selectData);
        // await store.handleSelect(event, "cabinetItem");
      }
    },
    // 图片点击
    cabinetImgItem: async (event) => {
      if (event.id !== "9999" && event.infoApi) {
        const selectData = cabinetTab.value.resources.find(
          (item) => item.id == event.id,
        );
        emit("setSelectedData", selectData);
        // await store.handleSelect(event, "cabinetItem");
      }
    },
  };
  await objType[type](event);
};
// 排序设备数据
function sortCabinetsByUsStart(cabinets) {
  return cabinets.sort((a, b) => a.usStart - b.usStart);
}
function fillBlanks(newItems) {
  const totalSlots = 38; // 总共的格子数
  let currentIndex = 0; // 当前处理到的格子索引
  const filledData = []; // 填充后的数据数组
  // 排序机柜数据
  const cabinet = sortCabinetsByUsStart(newItems);
  // 遍历机柜中的设备数据
  for (const item of cabinet) {
    // 填充img
    item.img = imgMap[item.resourceTypeCode];
    item.infoApi = !banList.includes(item.resourceTypeCode); // 设备是否有点击不让他去请求数据
    // 在当前设备前填充盲板
    while (currentIndex < item.usStart) {
      filledData.push({
        name: "盲板",
        img: panel, // 盲板图片路径
        desc: "盲板",
        usHeight: 1,
        usStart: currentIndex,
        id: "9999", // 盲板ID，可以根据需要生成唯一ID
        info: false,
      });
      currentIndex++; // 移动到下一个格子
    }

    // 添加当前设备到填充后的数据数组
    filledData.push({
      ...item,
      info: true, // 表示这是一个设备，不是盲板
    });

    // 跳过当前设备占据的格子数
    currentIndex += item.usHeight;
  }

  // 在最后一个设备后填充盲板，直到填满所有格子
  while (currentIndex < totalSlots) {
    filledData.push({
      name: "panel",
      img: panel, // 盲板图片路径
      desc: "盲板",
      usHeight: 1,
      usStart: currentIndex,
      id: "9999", // 盲板ID，可以根据需要生成唯一ID
      info: false,
    });
    currentIndex++;
  }

  // 返回填充后的机柜数据
  let newfilledData = [...filledData].reverse();
  return [...newfilledData];
}
onMounted(() => {
  cabinetTab.value = {
    ...props.cabinetData,
    resources: [...props.cabinetData.resources].reverse(),
    newResources: fillBlanks(props.cabinetData.resources),
  };
  // 获取图片的高度
  backHeight.value = cabinet.value.offsetHeight;
  imgHeight.value = (backHeight.value - 100) / 38;
});

const showMagnifiedImage = (item, event) => {
  if (!item.info) return;
  emit("setMagnified", item);
  if (!item.infoApi) return;
  let url = item.img;
  const rect = event.target.getBoundingClientRect();
  let top = rect.top - 80; // 垂直居中
  magnifiedImage.value = url;
  magnifiedImageInfo.value = item;
  magnifiedImageStyle.value = {
    position: "absolute",
    right: item.positionright + "px", // 放大图片显示在左侧
    top: `${top}px`,
    // transform: "translateY(-50%)",
    border: "1px solid #ccc",
    borderRadius: "5px",
    boxShadow: "0 0 10px rgba(0, 0, 0, 0.5)",
    zIndex: 99,
    transform: "scale(2)",
  };
};
</script>

<style lang="scss" scoped>
.cabinet {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #fff;
  position: relative;
  padding: 0 12px;
  .cabinetList {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-top: 12px;
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    .cabinetName {
      display: flex;
      justify-content: space-around;
      border-bottom: 1px solid #00000080;
      padding-left: 5px;
      div {
        flex: 1;
        margin-left: -8px;
      }
      .name {
        flex: 2;
      }
    }
    .cabinetItem {
      display: flex;
      justify-content: space-around;
      align-items: center;
      transition: all 25ms ease-in-out;
      div {
        flex: 1;
      }
      .name {
        width: 100px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
    }
  }
  .cabinetImg {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    position: relative;
    width: 138px;
    padding-top: 46px;
    padding-bottom: 46px;
    .cabinetImgItem {
      width: 90px;
      transition: all 25ms ease-in-out;
    }
    .ImgItem {
      width: 90px;
    }
    .mouseoverId {
      transform: scale(1.1);
      border: 2px solid #1daf4e;
    }
    .cabinetBoxClick {
      transform: scale(1.3);
      border: 2px solid #266fe8;
    }

    .magnifiedImage {
      position: absolute;
      width: 200px;
      height: auto;
      border: 1px solid #ccc;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      padding: 6px;
      z-index: 10;
      background-color: rgba(237, 241, 213, 0.89);
    }
    .describe {
      font-size: 8px;
      padding: 5px;
      div {
        width: 100%;
      }
    }
  }
}

.cabinetBoxHover {
  background-color: #94d5aa;
}
.cabinetBoxClick {
  color: #fff;
  background-color: #266fe8;
}
.statusnormal {
  width: 40px;
  height: 24px;
  border: 1px solid #00C851;
  color: #00C851;
  text-align: center;
  margin: 0 auto;
  border-radius: 6px;
  line-height: 24px;
  margin-top: 5px;
  background: linear-gradient(135deg, #E8F5E8 0%, #F0FFF0 100%);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 200, 81, 0.15);
  transition: all 0.3s ease;
}

.statusnormal:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 200, 81, 0.25);
}

.statusabnormal {
  width: 40px;
  height: 24px;
  border: 1px solid #FF4444;
  color: #FF4444;
  text-align: center;
  margin: 0 auto;
  border-radius: 6px;
  line-height: 24px;
  margin-top: 5px;
  background: linear-gradient(135deg, #FFE8E8 0%, #FFF0F0 100%);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(255, 68, 68, 0.15);
  transition: all 0.3s ease;
}

.statusabnormal:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 68, 68, 0.25);
}

.statusfault {
  width: 40px;
  height: 24px;
  border: 1px solid #9E9E9E;
  color: #9E9E9E;
  text-align: center;
  margin: 0 auto;
  border-radius: 6px;
  line-height: 24px;
  margin-top: 5px;
  background: linear-gradient(135deg, #F5F5F5 0%, #FAFAFA 100%);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(158, 158, 158, 0.15);
  transition: all 0.3s ease;
}

.statusfault:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(158, 158, 158, 0.25);
}
// // 媒体查询更具不同的高度来适配不同分辨率的屏幕
// @media only screen and (max-height: 1080px) {
//   .cabinet {
//     .cabinetImg {
//       width: 160px;
//       padding-top: 40px;
//       padding-bottom: 40px;
//     }
//     .cabinetImgItem,
//     .ImgItem {
//       width: 105px;
//     }
//   }
// }

// @media only screen and (max-height: 919px) {
//   .cabinet {
//     .cabinetImg {
//       width: 138px;
//       padding-top: 33px;
//       padding-bottom: 33px;
//     }
//     .cabinetImgItem,
//     .ImgItem {
//       width: 90px;
//     }
//   }
// }
</style>
