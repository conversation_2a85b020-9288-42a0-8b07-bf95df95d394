<template>
  <div class="con">
    <div class="top-con">
      <el-tabs
        v-model="timeDiffData.rawData.activeName"
        @tab-change="timeDiffData.handleRawClick"
      >
        <el-tab-pane label="场强" name="fieldStrength" :lazy="true">
        </el-tab-pane>
        <el-tab-pane label="信噪比" name="snr" :lazy="true"> </el-tab-pane>
        <el-tab-pane label="包周差" name="packageTD" :lazy="true">
        </el-tab-pane>
        <el-tab-pane label="频率偏差" name="frequencyTD" :lazy="true">
        </el-tab-pane>
      </el-tabs>
      <div class="tabContent">
        <lineChart
          ref="lineChartRef"
          class="chartArea"
          :data="rawData"
        ></lineChart>
      </div>
    </div>
    <el-table
      :data="rawData.tableData || []"
      border
      max-height="2000"
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column align="center" label="设备">
        <template #default="{ row }">
          <div>
            {{ "接收机" + row.receiverId.slice(-1) }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column property="avg" align="center" label="当前值" /> -->
      <el-table-column property="min" align="center" label="最小值" />
      <el-table-column property="max" align="center" label="最大值" />
      <el-table-column property="avg" align="center" label="平均值" />
      <el-table-column property="stdDev" align="center" label="标准差" />
      <el-table-column property="rms" align="center" label="RMS" />
      <el-table-column
        property="percentile95"
        align="center"
        label="95%分位数"
      />
      <el-table-column property="count" align="center" label="总数" />
    </el-table>
  </div>
</template>

<script setup>
import {
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  watch,
  onUnmounted,
  nextTick,
} from "vue";
import { useTimeDiffData } from "@/store/modules/row-data/time-diff-data";
import lineChart from "@/components/lineChart/lineChart.vue";
const activeName = ref("fieldStrength");
const lineChartRef = ref(null);
const timeDiffData = useTimeDiffData();
onMounted(async () => {
  // 初始化
  await timeDiffData.initRawData(lineChartRef);
});
//全部的数据
const rawData = reactive({
  title: {
    text: `单位：`,
    left: "left",
    top: "50",
    textStyle: {
      fontSize: 14,
      fontWeight: "bold",
    },
  },
  xAxis: {
    type: "time",
    axisLabel: {
      interval: 0, // 显示所有标签
    },
  },
  yAxis: {
    type: "value",
    axisLine: {
      show: false, // 隐藏 y 轴轴线
    },
    splitLine: {
      show: false, // 隐藏 y 轴网格线
    },
  },
  legend: {
    bottom: -4,
    itemHeight: 4,
    icon: "rect",
    textStyle: {
      fontWeight: 400,
      fontSize: 12,
      color: "#2B2E3F",
      lineHeight: 20,
      fontStyle: "normal",
    },
  },
  grid: {
    left: "60px",
    right: "30px",
    top: "100px",
    bottom: "50px",
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      label: {
        backgroundColor: "#6a7985",
      },
    },
  },
  series: [
    {
      type: "line",
      showSymbol: false,
      data: [],
    },
  ],
});
</script>

<style lang="scss" scoped>
.top-con {
  background: #fff;
  border-radius: 2px;
  height: 440px;
  padding: 8px 0;

  :deep(.el-tabs__header) {
    margin: 0 0 0 2px;
  }

  .tabContent {
    padding: 0 10px;
  }

  .chartArea {
    width: 100%;
    height: 370px;
  }

  .select {
    width: 162px;
    position: absolute;
    top: 8px;
    left: 30px;
    z-index: 1;
  }

  .select-label {
    position: absolute;
    top: 14px;
    left: 170px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    text-align: center;
    font-style: normal;
    z-index: 1;
  }

  .hide-select-label {
    display: none;
  }

  :deep(.el-tabs) {
    .el-tabs__item {
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      text-align: right;
      font-style: normal;
      padding: 12px 12px 14px 12px;
      height: 48px;
      margin-right: 16px;
    }

    .el-tabs__nav {
      padding: 0 10px;
    }
  }
}

.con {
  :deep(.el-table) {
    --el-table-border-color: #e7e7e7;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }
}
</style>
