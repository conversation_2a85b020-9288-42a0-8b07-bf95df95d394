<template>
  <div class="aggregateData">
    <div class="topCards">
      <el-card class="cards">
        <template #header>
          <div class="card-header">
            <span>仪表检测服务响应数据</span>
          </div>
        </template>
        <div class="neibusd">
          <div
            class="topCards_box"
            id="topCards_box"
            ref="scrollableComponent"
            @scroll="handleScroll"
          >
            <div
              :class="{
                name: true,
                infos: detectionInstrumentData?.id == item.id,
              }"
              v-for="item in aggregateData.detectionInstrument"
              :key="item.id"
              @click="setInstrument(item)"
            >
              <div
                class="tag"
                v-if="detectionInstrumentData?.id == item.id"
              ></div>
              <div class="title">{{ item.title }}</div>
            </div>
          </div>
          <div class="topCards_body">
            <div class="topCards_body_item">
              <div class="navs">实时报文</div>
              <div class="boxs">{{ detectionInstrumentData.packet }}</div>
            </div>
            <div
              class="topCards_body_item topCards_body_item_food"
              v-if="detectionInstrumentData"
            >
              <div class="navs">数据</div>
              <div
                class="boxs"
                v-for="i in Object.keys(aggregateData.typeList)"
                :key="i"
              >
                <div>{{ aggregateData.typeList[i] }}：</div>
                <div>{{ detectionInstrumentData.data[i] }}</div>
              </div>
            </div>
          </div>
          <img
            v-if="aggregateData.infoTops"
            @click="handleClickOutside"
            src="../../../assets/imgs/top.png"
            class="top"
          />
        </div>
      </el-card>
      <el-card class="cards tongji">
        <template #header>
          <div class="card-header">
            <span>统计</span>
          </div>
        </template>
        <div class="tongji_box">
          <div
            class="tongji_item"
            v-for="item in Object.keys(aggregateData.statistics)"
            :key="item"
          >
            <div class="tongji_left">
              {{ aggregateData.statistics[item].name }}
            </div>
            <div class="tongji_right">
              {{ aggregateData.statistics[item].data }}
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <el-card class="bottomCard">
      <template #header>
        <div class="card-header">
          <span>统计</span>
        </div>
      </template>
      <lineChart
        class="chartArea"
        ref="chartAreas"
        :data="{ series: [{ data: [] }] }"
      />
      <div class="prompt">
        <div class="prompt_item normal">
          <div class="xian"></div>
          实际值
        </div>
        <div class="prompt_item Abnormal">
          <div class="xian"></div>
          异常
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, nextTick, onUnmounted } from "vue";
import { statistics, statisticsChart } from "./data.js";
import lineChart from "../../../components/lineChart/lineChart.vue";
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
import dayjs from "dayjs";
import { isIdExist } from "@/utils/common";
let activeName = ref("first");
let scrollableComponent = ref();
let chartAreas = ref();
let chartAreasDme = ref();
//总的数据
let aggregateData = reactive({
  detectionInstrument: [], //仪表检测服务响应数据数据
  statisticsChart: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
    },
    title: {
      text: `单位：ns`,
      left: "left",
      top: "0",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value}",
      },
      axisPointer: {
        snap: true,
      },
      min: 0,
      max: 100,
    },
    visualMap: {
      show: false,
      dimension: 0,
      pieces: [],
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "12%",
      top: "10%",
    },
    series: [
      {
        type: "line",
        symbol: "", // 设置为空值，不显示符号
        symbolSize: 0, // 设置为0，不显示符号
        smooth: false,
        data: [],
        markArea: {
          itemStyle: {
            color: "rgba(39, 128, 255,0.1)",
          },
          data: [],
        },
      },
    ],
  },
  statistics: {
    frmHdr: { name: "样本个数", data: "" },
    curOfset: { name: "当前偏差", data: "" },
    ntpFw: { name: "偏差范围", data: "" }, //ntpTiMin  // ntpTiMax
    ntpTiAvg: { name: "平均偏差", data: "" },
    ntpTiStd: { name: "标准偏差", data: "" },
    ntpSenCur: { name: "秒偏差", data: "" },
    ntpSenOfsetCount: { name: "秒偏差告警量", data: "" },
    ntpSgnLossCount: { name: "型号丢失告警量", data: "" },
  }, //表的数据
  chartList: [], //需要处理 的数据
  typeList: {
    T1: "Client Tx (T1)",
    T2: "Setver Rx (T2)",
    T3: "Setver Tx (T3)",
    T4: "Client Rx (T4)",
  },
  infoInstrument: true, //  自动显示第一条数据
  infoTops: false,
  hoveredData: 0, //显示的地几个
});
let detectionInstrumentData = ref(false);
onMounted(async () => {
  await initScocket();
  aggregateData.detectionInstrument = [];
  subscribe("/topic/jsInfo/udptmc/UDP-DTZ-TMC", setDetectionInstrument);
  // chartAreasDme.value = chartAreas.value.getIns();
  // chartAreasDme.value.on("mouseover", (params) => {
  //   onMouseover(params.dataIndex);
  // });
});
//移动的鼠标
// let onMouseover = (dataIndex) => {
//   aggregateData.hoveredData = dataIndex;
// };
//处理ws 仪表检测服务响应数据数据
const setDetectionInstrument = (data) => {
  if (!data) {
    return;
  }
  let newData = JSON.parse(data);
  console.log("newData", newData);
  newData.id = newData.date;
  newData.title = dayjs(newData.date).format("YYYY年MM月DD日 HH:mm:ss");
  aggregateData.detectionInstrument.unshift(newData);
  if (aggregateData.detectionInstrument.length > 120) {
    aggregateData.detectionInstrument = [
      ...aggregateData.detectionInstrument,
    ].splice(0, 120);
  }

  // 自动的跳转到第一个数据
  if (aggregateData.infoInstrument) {
    detectionInstrumentData.value = aggregateData.detectionInstrument[0];
  }
  // 如果之前选中的数据划过了，就让他跳转到第一个数据
  if (
    !isIdExist(
      detectionInstrumentData.value.id,
      aggregateData.detectionInstrument,
    )
  ) {
    handleClickOutside();
  }
  //下面的图表
  nextTick(() => {
    try {
      setStatisticsChart(newData);
    } catch {
      console.log("重新来");
    }
  });

  //旁边表的数据
  let tabelData = {
    ...newData.stats,
    frmHdr: newData.stats.idx,
    ntpFw: newData.stats.ntpTiMin + " - " + newData.stats.ntpTiMax,
  };
  Object.keys(aggregateData.statistics).forEach((i) => {
    aggregateData.statistics[i].data = tabelData[i];
  });
};
//选中的一条数据
const setInstrument = (data) => {
  aggregateData.infoInstrument = false;
  detectionInstrumentData.value = data;
};
//返回最上面那条数据
const handleClickOutside = () => {
  scrollableComponent.value.scrollTop = 0;
  aggregateData.infoInstrument = true;
  aggregateData.infoTops = false;
  detectionInstrumentData.value = aggregateData.detectionInstrument[0];
};
// 滑动到最上面 显示的东西
const handleScroll = () => {
  if (scrollableComponent.value.scrollTop > 100) {
    aggregateData.infoTops = true;
  }
};

//设置图标的数据
const setStatisticsChart = (item) => {
  console.log("item", item);
  // 超过数据后
  if (aggregateData.chartList.length >= 121) {
    aggregateData.chartList = [...aggregateData.chartList].splice(1, 121);
  }
  let newData = {
    date: dayjs(item.date).format("HH:mm:ss"),
    data: {
      info: item.data.redLine == 0 && item.data.ntpStatus == 0,
      // info: Math.random() < 0.5,
      // curOfset: Math.random() < 0.5 ? item.data.curOfset : -item.data.curOfset,
      curOfset: item.data.curOfset == "*" ? 0 : item.data.curOfset,
    },
  };
  aggregateData.chartList.push(newData);
  let obj = { ...aggregateData.statisticsChart };
  let { x, y } = setXYData(aggregateData.chartList);
  obj.xAxis.data = x;
  obj.series[0].data = y;
  let max = Math.max(...y.map((i) => i - 0));
  let min = Math.min(...y.map((i) => i - 0));
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) * 0.1;
    min = min - (max - min) * 0.1;
  }
  obj.yAxis = {
    max: Math.ceil(max * 1000) / 1000,
    min: Math.ceil(min * 1000) / 1000,
  };
  obj.visualMap.pieces = newsetVisualMappieces(aggregateData.chartList);
  obj.series[0].markArea.data = setMarkArea(aggregateData.chartList);
  chartAreas.value.setInfos();
  chartAreas.value.getIns().setOption(obj);
};

//处理 markArea 数据
const setMarkArea = (arr) => {
  // 初始化结果数组
  let result = [];
  arr.forEach((val, index) => {
    if (index > 1) {
      result.push([
        {
          xAxis: arr[index - 1].date,
        },
        {
          xAxis: val.date,
          itemStyle: {
            color: val.data.info
              ? "rgba(39, 128, 255,0)"
              : "rgba(39, 128, 255,0.1)",
          },
        },
      ]);
    }
  });

  return result;
};

const newsetVisualMappieces = (arr) => {
  let result = [];
  arr.forEach((val, index) => {
    result.push({
      color: val.data.info ? "#1e7bff" : "red",
      lte: index,
    });
  });
  return result;
};
//处理x轴和y轴的数据
const setXYData = (arr) => {
  let x = [];
  let y = [];
  arr.forEach((i) => {
    x.push(i.date);
    y.push(i.data.curOfset);
  });
  return { x, y };
};
onUnmounted(() => {
  unsubscribe("/topic/jsInfo/udptmc/UDP-DTZ-TMC");
});
</script>

<style lang="scss" scoped>
.aggregateData {
  display: flex;
  flex-direction: column;
  height: 95%;
  .card-header {
    font-weight: 600;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
  }
  :deep(.el-card__header) {
    padding: 14px 16px;
  }
  .topCards {
    width: 100%;
    height: 350px;
    display: flex;
    margin-bottom: 16px;
    .cards {
      flex: 1;
      .neibusd {
        display: flex;
        height: 250px;
        position: relative;
      }
      .top {
        position: absolute;
        width: 18px;
        right: 10px;
        bottom: 10px;
        cursor: pointer;
      }
      .topCards_box {
        width: 193px;
        overflow-y: scroll;
        overflow-x: hidden;
        scrollbar-color: rgba(38, 112, 232, 0.384) #ecf2fe;
        scrollbar-width: thin;
        position: relative;
      }
      .name {
        font-size: 13px;
        color: #6d6e78;
        padding: 16px;
        background: #ecf2fe;
        cursor: pointer;
      }
      .infos {
        display: flex;
        align-items: center;
        background: #fff;
      }
      .tag {
        width: 4px;
        height: 52px;
        background: #266fe8;
        position: absolute;
        left: 0;
      }
      .topCards_body {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #ffffff;
        border: 1px solid #dcdcdc;
        padding: 16px;
        margin-left: 2px;
        .topCards_body_item {
          .navs {
            font-weight: 600;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 8px;
          }
          .boxs {
            display: flex;
            width: 560px;
            word-break: break-all;
          }
        }
        .topCards_body_item_food {
          margin-top: 16px;
        }
      }
    }
    .tongji {
      margin-left: 16px;
    }
  }
  .tongji {
    .tongji_box {
      width: 100%;
      height: 250px;
      display: flex;
      flex-direction: column;
      border-radius: 4px 4px 0px 0px;
      border-left: 1px solid #e8e8e8;
      border-right: 1px solid #e8e8e8;
      border-bottom: 1px solid #e8e8e8;
      .tongji_item {
        display: flex;
        border-top: 1px solid #e8e8e8;
        .tongji_left {
          width: 233px;
          height: 30.1px;
          line-height: 30.1px;
          background: #fafafa;
          border-radius: 4px 4px 0px 0px;
          padding-left: 70px;
        }
        .tongji_right {
          padding-left: 150px;
          line-height: 30.1px;
        }
      }
    }
  }
  .bottomCard {
    flex: 1;
    position: relative;
    .chartArea {
      width: 100%;
      height: 340px;
    }
    .prompt {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      .xian {
        width: 20px;
        height: 5px;
        margin-right: 15px;
        background: #266fe8;
      }
      .prompt_item {
        display: flex;
        align-items: center;
      }
      .Abnormal {
        margin-left: 15px;
        .xian {
          background: #e34d59;
        }
      }
    }
  }
}
</style>
