_jsload2&&_jsload2('canvablepath', 'function og(a){a=a.replace(/,/gm," ");a=a.replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2");a=a.replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2");a=a.replace(/([MmZzLlHhVvCcSsQqTtAa])([^\\s])/gm,"$1 $2");a=a.replace(/([^\\s])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2");a=a.replace(/([0-9])([+\\-])/gm,"$1 $2");a=a.replace(/(\\.[0-9]*)(\\.)/gm,"$1 $2");a=a.replace(/([Aa](\\s+[0-9]+){3})\\s+([01])\\s*([01])/gm,"$1 $3 $4 ");a=og.jW(a);a=og.trim(a);this.RP=new function(a){this.ZF= a.split(" ");this.reset=function(){this.ih=-1;this.Rt=this.Ww="";this.start=new og.cg(0,0);this.Xw=new og.cg(0,0);this.kb=new og.cg(0,0);this.ja=[];this.Do=[]};this.vM=function(){return this.ih>=this.ZF.length-1};this.sj=function(){return this.vM()?q:this.ZF[this.ih+1].match(/^[A-Za-z]$/)!=s};this.CE=function(){switch(this.Ww){case "m":case "l":case "h":case "v":case "c":case "s":case "q":case "t":case "a":case "z":return q}return t};this.$L=function(){this.ih++;return this.ZF[this.ih]};this.mj=function(){return parseFloat(this.$L())}; this.yZ=function(){this.Rt=this.Ww;this.Ww=this.$L()};this.vm=function(){return this.kZ(new og.cg(this.mj(),this.mj()))};this.JD=function(){var a=this.vm();return this.Xw=a};this.vk=function(){var a=this.vm();return this.kb=a};this.SL=function(){return"c"!=this.Rt.toLowerCase()&&"s"!=this.Rt.toLowerCase()&&"q"!=this.Rt.toLowerCase()&&"t"!=this.Rt.toLowerCase()?this.kb:new og.cg(2*this.kb.x-this.Xw.x,2*this.kb.y-this.Xw.y)};this.kZ=function(a){this.CE()&&(a.x+=this.kb.x,a.y+=this.kb.y);return a};this.Xi= function(a,b,f){f!=s&&(0<this.Do.length&&this.Do[this.Do.length-1]==s)&&(this.Do[this.Do.length-1]=Math.atan2(f.y-this.ja[this.ja.length-1].y,f.x-this.ja[this.ja.length-1].x));this.$B(a,b==s?s:Math.atan2(a.y-b.y,a.x-b.x))};this.$B=function(a,b){this.ja.push(a);this.Do.push(b)}}(a);this.bb=function(){return this.di(s)};this.di=function(a){var c=this.RP;c.reset();var e=new og.xP;for(a!=s&&a.beginPath();!c.vM();)switch(c.yZ(),c.Ww){case "M":case "m":var f=c.vk();c.Xi(f);e.qg(f.x,f.y);a!=s&&a.moveTo(f.x, f.y);for(c.start=c.kb;!c.sj();)f=c.vk(),c.Xi(f,c.start),e.qg(f.x,f.y),a!=s&&a.lineTo(f.x,f.y);break;case "L":case "l":for(;!c.sj();){var g=c.kb,f=c.vk();c.Xi(f,g);e.qg(f.x,f.y);a!=s&&a.lineTo(f.x,f.y)}break;case "H":case "h":for(;!c.sj();)f=new og.cg((c.CE()?c.kb.x:0)+c.mj(),c.kb.y),c.Xi(f,c.kb),c.kb=f,e.qg(c.kb.x,c.kb.y),a!=s&&a.lineTo(c.kb.x,c.kb.y);break;case "V":case "v":for(;!c.sj();)f=new og.cg(c.kb.x,(c.CE()?c.kb.y:0)+c.mj()),c.Xi(f,c.kb),c.kb=f,e.qg(c.kb.x,c.kb.y),a!=s&&a.lineTo(c.kb.x,c.kb.y); break;case "C":case "c":for(;!c.sj();){var i=c.kb,g=c.vm(),k=c.JD(),f=c.vk();c.Xi(f,k,g);e.ZB(i.x,i.y,g.x,g.y,k.x,k.y,f.x,f.y);a!=s&&a.bezierCurveTo(g.x,g.y,k.x,k.y,f.x,f.y)}break;case "S":case "s":for(;!c.sj();)i=c.kb,g=c.SL(),k=c.JD(),f=c.vk(),c.Xi(f,k,g),e.ZB(i.x,i.y,g.x,g.y,k.x,k.y,f.x,f.y),a!=s&&a.bezierCurveTo(g.x,g.y,k.x,k.y,f.x,f.y);break;case "Q":case "q":for(;!c.sj();)i=c.kb,k=c.JD(),f=c.vk(),c.Xi(f,k,k),e.XJ(i.x,i.y,k.x,k.y,f.x,f.y),a!=s&&a.quadraticCurveTo(k.x,k.y,f.x,f.y);break;case "T":case "t":for(;!c.sj();)i= c.kb,k=c.SL(),c.Xw=k,f=c.vk(),c.Xi(f,k,k),e.XJ(i.x,i.y,k.x,k.y,f.x,f.y),a!=s&&a.quadraticCurveTo(k.x,k.y,f.x,f.y);break;case "A":case "a":for(;!c.sj();){var i=c.kb,m=c.mj(),n=c.mj(),g=c.mj()*(Math.PI/180),o=c.mj(),k=c.mj(),f=c.vk(),p=new og.cg(Math.cos(g)*(i.x-f.x)/2+Math.sin(g)*(i.y-f.y)/2,-Math.sin(g)*(i.x-f.x)/2+Math.cos(g)*(i.y-f.y)/2),v=Math.pow(p.x,2)/Math.pow(m,2)+Math.pow(p.y,2)/Math.pow(n,2);1<v&&(m*=Math.sqrt(v),n*=Math.sqrt(v));o=(o==k?-1:1)*Math.sqrt((Math.pow(m,2)*Math.pow(n,2)-Math.pow(m, 2)*Math.pow(p.y,2)-Math.pow(n,2)*Math.pow(p.x,2))/(Math.pow(m,2)*Math.pow(p.y,2)+Math.pow(n,2)*Math.pow(p.x,2)));isNaN(o)&&(o=0);var x=new og.cg(o*m*p.y/n,o*-n*p.x/m),i=new og.cg((i.x+f.x)/2+Math.cos(g)*x.x-Math.sin(g)*x.y,(i.y+f.y)/2+Math.sin(g)*x.x+Math.cos(g)*x.y),y=function(a,b){return(a[0]*b[0]+a[1]*b[1])/(Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2))*Math.sqrt(Math.pow(b[0],2)+Math.pow(b[1],2)))},A=function(a,b){return(a[0]*b[1]<a[1]*b[0]?-1:1)*Math.acos(y(a,b))},o=A([1,0],[(p.x-x.x)/m,(p.y- x.y)/n]),v=[(p.x-x.x)/m,(p.y-x.y)/n],x=[(-p.x-x.x)/m,(-p.y-x.y)/n],p=A(v,x);-1>=y(v,x)&&(p=Math.PI);1<=y(v,x)&&(p=0);v=1-k?1:-1;x=o+v*(p/2);c.$B(new og.cg(i.x+m*Math.cos(x),i.y+n*Math.sin(x)),x-v*Math.PI/2);c.$B(f,x-v*Math.PI);e.qg(f.x,f.y);a!=s&&(y=m>n?m:n,f=m>n?1:m/n,m=m>n?n/m:1,a.translate(i.x,i.y),a.rotate(g),a.scale(f,m),a.arc(0,0,y,o,o+p,1-k),a.scale(1/f,1/m),a.rotate(-g),a.translate(-i.x,-i.y))}break;case "Z":case "z":a!=s&&a.closePath(),c.kb=c.start}return e}} og.trim=function(a){return a.replace(/^\\s+|\\s+$/g,"")};og.jW=function(a){return a.replace(/[\\s\\r\\t\\n]+/gm," ")};og.cg=function(a,b){this.x=a;this.y=b}; og.xP=function(){this.Cn=this.Bn=this.Aj=this.zj=Number.NaN;this.x=w("zj");this.y=w("Aj");this.width=function(){return this.Bn-this.zj};this.height=function(){return this.Cn-this.Aj};this.qg=function(a,b){if(a!=s){if(isNaN(this.zj)||isNaN(this.Bn))this.Bn=this.zj=a;a<this.zj&&(this.zj=a);a>this.Bn&&(this.Bn=a)}if(b!=s){if(isNaN(this.Aj)||isNaN(this.Cn))this.Cn=this.Aj=b;b<this.Aj&&(this.Aj=b);b>this.Cn&&(this.Cn=b)}};this.dC=function(a){this.qg(a,s)};this.eC=function(a){this.qg(s,a)};this.XJ=function(a, b,c,e,f,g){c=a+2/3*(c-a);e=b+2/3*(e-b);this.ZB(a,b,c,c+1/3*(f-a),e,e+1/3*(g-b),f,g)};this.ZB=function(a,b,c,e,f,g,i,k){var m=[a,b],n=[c,e],o=[f,g],p=[i,k];this.qg(m[0],m[1]);this.qg(p[0],p[1]);for(pg=0;1>=pg;pg++)if(a=function(a){return Math.pow(1-a,3)*m[pg]+3*Math.pow(1-a,2)*a*n[pg]+3*(1-a)*Math.pow(a,2)*o[pg]+Math.pow(a,3)*p[pg]},b=6*m[pg]-12*n[pg]+6*o[pg],c=-3*m[pg]+9*n[pg]-9*o[pg]+3*p[pg],e=3*n[pg]-3*m[pg],0==c)0!=b&&(b=-e/b,0<b&&1>b&&(0==pg&&this.dC(a(b)),1==pg&&this.eC(a(b))));else if(e=Math.pow(b, 2)-4*e*c,!(0>e)&&(f=(-b+Math.sqrt(e))/(2*c),0<f&&1>f&&(0==pg&&this.dC(a(f)),1==pg&&this.eC(a(f))),b=(-b-Math.sqrt(e))/(2*c),0<b&&1>b))0==pg&&this.dC(a(b)),1==pg&&this.eC(a(b))};this.qg(l,l);this.qg(l,l)};B.yP=og; ');