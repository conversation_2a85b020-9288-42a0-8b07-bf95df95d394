<template>
  <div class="con">
    <div class="top-con">
      <div class="tabContent">
        <el-select
          v-model="preferredObj.typeIndex"
          placeholder="请选择"
          class="item select"
        >
          <el-option key="tser" label="授时偏差" value="tser"></el-option>
          <el-option key="toa" label="TOA" value="toa"></el-option>
          <el-option key="toc" label="TOC" value="toc"></el-option>
        </el-select>
        <el-select
          v-model="preferredObj.optionIndex"
          placeholder="请选择"
          class="select"
        >
          <el-option
            v-for="item in preferredObj.optionList"
            :key="item.key"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <lineChart
          ref="lineChartRef"
          class="chartArea"
          :data="preferredObj.chartData"
        ></lineChart>
      </div>
    </div>
    <el-table
      :data="preferredObj.tableData || []"
      border
      max-height="2000"
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column align="center" label="设备">
        <template #default="{ row }">
          <div>
            {{ proxy.configure.thoroughfare(row.transmitter) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column property="min" align="center" label="最小值" />
      <el-table-column property="max" align="center" label="最大值" />
      <el-table-column property="avg" align="center" label="平均值" />
      <el-table-column property="stdDev" align="center" label="标准差" />
      <el-table-column property="rms" align="center" label="RMS" />
      <el-table-column
        property="percentile95"
        align="center"
        label="95%分位数"
      />
      <el-table-column property="count" align="center" label="总数" />
    </el-table>
  </div>
</template>

<script setup>
import apiAjax from "@/api/index";
import {
  reactive,
  ref,
  watch,
  onMounted,
  onUnmounted,
  getCurrentInstance,
  nextTick,
} from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
const { proxy } = getCurrentInstance();
const preferredObj = reactive({
  optionList: [], //下拉框的选的值 动态传入
  optionIndex: "", // 默认选中的值，默认选中的是第一个值
  typeIndex: "tser",
  chartData: {
    title: {
      text: `单位：ns`,
      left: "left",
      top: "50",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "time",
      axisLabel: {
        interval: 0, // 显示所有标签
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false, // 隐藏 y 轴轴线
      },
      splitLine: {
        show: false, // 隐藏 y 轴网格线
      },
      min: 20.0,
      max: 20.2,
    },
    legend: {
      bottom: -4,
      itemHeight: 4,
      icon: "rect",
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: "#2B2E3F",
        lineHeight: 20,
        fontStyle: "normal",
      },
    },
    grid: {
      left: "60px",
      right: "30px",
      top: "100px",
      bottom: "50px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [
      {
        type: "line",
        data: [],
        showSymbol: false,
        symbolSize: 10, // 标记的大小
        itemStyle: {
          color: "#1d7bff",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(25, 120, 255, 0.2)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255, 255, 255, 0)", // 100% 处的颜色
              },
            ],
          },
          origin: "start", // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: "solid",
        },
      },
    ],
  },
  chartDataObj: {},
  tableData: [],
});
const lineChartRef = ref(null);

//假数据
function getRandomNumber(min, max) {
  return (Math.random() * (max - min) + min).toFixed(3);
}

function addSeconds(date, seconds) {
  const newDate = new Date(date.getTime() + seconds * 1000);
  return newDate;
}

function formatDate(date) {
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0");
  const day = String(date.getUTCDate()).padStart(2, "0");
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const sec = String(date.getUTCSeconds()).padStart(2, "0");
  return `${year}${month}${day}${hours}${minutes}${sec}`;
}

async function setLOde() {
  const data = await apiAjax.get(
    `/api/jnx/data/findHistory?startTime=${proxy.configure.getTimestampTwoMinutesAgo(
      3
    )}&endTime=${proxy.configure.getUtcDate()}&dataType=fbtyxd_real&value=1&twSize=3600&page=1&pageNum=300`
  );
  let newList = [];
  if (!data) return [];
  if (data[0]?.fbtyxd_real.length > 0) {
    newList = data[0].fbtyxd_real;
  }
  return newList;
}
onMounted(async () => {
  await initScocket();
  initMode();
});
onUnmounted(() => {
  unsubscribe("/topic/jsInfo/syrealtimedata/DTD-DTZ-FBTYXD");
  unsubscribe("/topic/jsInfo/24hrealscd/24h-REAL-FBTYXD");
});

const initMode = async () => {
  let list = await setLOde();
  if (list.length > 361) {
    list = list.slice(-360);
  }
  list.forEach((i) => {
    let newData = i;
    const dateTime = proxy.configure.setDate(newData.Date); // 预计算时间戳
    const date = new Date(dateTime);
    setPreferredObj(newData.transmitters, date, dateTime);
  });
  subscribe("/topic/jsInfo/syrealtimedata/DTD-DTZ-FBTYXD", setPreferred);
  subscribe("/topic/jsInfo/24hrealscd/24h-REAL-FBTYXD", setDataFromFBTYXD); // 下面表格的实时数据
};

const setDataFromFBTYXD = (data) => {
  let newData = JSON.parse(data);
  preferredObj.tableData = [];
  newData.forEach((i) => {
    if (
      i.dataTp == preferredObj.typeIndex &&
      preferredObj.optionIndex == i.transmitter
    ) {
      preferredObj.tableData.push(i);
    }
  });
};

const setPreferred = (data) => {
  let newData = JSON.parse(data);
  const dateTime = proxy.configure.setDate(newData.Date); // 预计算时间戳
  const date = new Date(dateTime);
  if(newData.transmitters){
    setPreferredObj(newData.transmitters, date, dateTime);
  }
};

const setPreferredObj = (valuelist, date, dateTime) => {
  valuelist.forEach((transmitter) => {
    const { transmitter: id, tser, toa, toc } = transmitter;
    const optionExists = preferredObj.optionList.some((i) => i.value === id);
    if (!optionExists) {
      preferredObj.optionList.push({
        key: id,
        label: proxy.configure.thoroughfare(id),
        value: id,
      });
    }
    preferredObj.optionIndex = preferredObj.optionIndex || id;
    // 确保目标对象存在
    if (!preferredObj.chartDataObj[id]) {
      preferredObj.chartDataObj[id] = {
        tser: [], //授时偏差
        toa: [], //toa
        toc: [], //toc
      };
    }
    // 更新数据
    preferredObj.chartDataObj[id].tser.push({
      name: date.toString(),
      value: [date, tser],
    });

    preferredObj.chartDataObj[id].toa.push({
      name: date.toString(),
      value: [date, toa],
    });
    preferredObj.chartDataObj[id].toc.push({
      name: date.toString(),
      value: [date, toc],
    });
    const MAX_LENGTH = 360;
    if (preferredObj.chartDataObj[id].tser.length > MAX_LENGTH) {
      preferredObj.chartDataObj[id].tser.shift();
      preferredObj.chartDataObj[id].toa.shift();
      preferredObj.chartDataObj[id].toc.shift();
    }
  });
  nextTick(() => {
    getChartAreaData();
  });
};

const getChartAreaData = () => {
  let objType = {
    tser: "授时偏差",
    toa: "TOA",
    toc: "TOC",
  };
  const list =
    preferredObj.chartDataObj[preferredObj.optionIndex][preferredObj.typeIndex];
  if (proxy.configure.isEmpty(list)) {
    return;
  }
  let max = Math.max(...list.map((i) => i.value[1]));
  let min = Math.min(...list.map((i) => i.value[1]));
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) * 0.1;
    min = min - (max - min) * 0.1;
  }
  lineChartRef.value.setInfos();
  lineChartRef.value.getIns().setOption({
    yAxis: {
      max: Math.ceil(max * 1000) / 1000,
      min: Math.ceil(min * 1000) / 1000,
    },
    grid: { left: (Math.ceil(max * 1000) / 1000 + "").length * 7 + 25 + "px" },
    series: [
      {
        data: list,
        name: objType[preferredObj.typeIndex],
      },
    ],
  });
};
watch(
  () => [preferredObj.optionIndex, preferredObj.typeIndex],
  () => {
    getChartAreaData();
  }
);
</script>

<style lang="scss" scoped>
.top-con {
  background: #fff;
  border-radius: 2px;
  height: 440px;

  .tabContent {
    padding: 0 10px;
  }

  .chartArea {
    width: 100%;
    height: 400px;
  }

  .select {
    width: 182px;
    position: relative;
    top: 6px;
    left: 10px;
    z-index: 1;
    height: 0;
  }

  .item {
    width: 122px;
    margin-right: 16px;
  }

  .select-label {
    position: relative;
    width: 200px;
    top: 0px;
    left: 280px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    text-align: left;
    font-style: normal;
    z-index: 1;
    height: 0;
  }

  .hide-select-label {
    display: none;
  }
}

.con {
  :deep(.el-table) {
    --el-table-border-color: #e7e7e7;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }
}
</style>
