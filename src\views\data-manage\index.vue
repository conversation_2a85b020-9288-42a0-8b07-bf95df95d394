<template>
  <div class="con">
    <div class="top-bar-con">
      <div class="top-bar" v-if="hasChildTab">
        <el-button-group class="btn-group">
          <el-button
            :color="themeStore.themeColor"
            :plain="childTabIndex === 0 ? false : true"
            @click="() => onChildTabClick(0)"
            >原始数据</el-button
          >
          <el-button
            :color="themeStore.themeColor"
            :plain="childTabIndex === 1 ? false : true"
            @click="() => onChildTabClick(1)"
            >分析数据</el-button
          >
        </el-button-group>
        <el-button type="primary" @click="onFormHow">预处理</el-button>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.name"
        :name="item.name"
        :label="item.label"
      >
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Files /></el-icon>
            <span>{{ item.label }}</span>
          </span>
        </template>
        <KeepAlive>
          <rowlandDome v-if="item.name === 'tab4'" />
          <Interfere v-else-if="item.name === 'tab6'" />
          <weatherStation v-else-if="item.name === 'tab7'" />
          <Child
            v-else
            :key="`${item.name}_${childTabIndex}`"
            v-bind="(item.filterConfig || [])[childTabIndex] || {}"
            @onSearch="onSearch"
            ref="childDom"
          />
        </KeepAlive>
      </el-tab-pane>
    </el-tabs>
    <FormDom ref="formdome" />
  </div>
</template>

<script setup>
import {
  computed,
  ref,
  watch,
  onMounted,
  reactive,
  getCurrentInstance,
} from "vue";
import { useDataManage } from "@/store/modules/data-manage";
import Child from "./child.vue";
import Interfere from "./interfere.vue";
import { useThemeStore } from "@/store/modules/theme";
import dayjs from "dayjs";
import FormDom from "./FormDom.vue";
import apiAjax from "@/api/index";
import { ElMessage } from "element-plus";
import weatherStation from "./weatherStation.vue";
import rowlandDome from "./rowlandDome.vue";
import {
  ApiObj,
  tabList,
  setTabData,
  setUrl,
  setfrequencyTabe,
} from "./data.js";
const { proxy } = getCurrentInstance();
const store = useDataManage();
const themeStore = useThemeStore();
let configObj = reactive({});
const formdome = ref(); // 页面元素
const childDom = ref(); // 页面元素
onMounted(async () => {
  await store.init();
  tabList.value[0].filterConfig[0].devList.data = store.configObj.Receiver;
  // 设置多通道的默认值 设备ID
  tabList.value[1].filterConfig[0].devList.data = store.configObj.Configuration;
});

// 搜索的函数
const onSearch = async ({ newType, newVal, loopType }) => {
  let proms = "";
  if (!ApiObj[newType]) {
    return;
  }
  let fromObj = { ...ApiObj[newType].from };
  //不是点击点击再次搜索 这里是在设置from
  if (loopType !== "onSearchEchart") {
    if (newVal.date.length == 2) {
      fromObj.startTime = Math.floor(newVal.date[0].getTime() / 1000);
      fromObj.endTime = Math.floor(newVal.date[1].getTime() / 1000);
    }
    Object.keys(fromObj).forEach((key) => {
      Object.keys(newVal).forEach((k) => {
        if (fromObj[key] === k) {
          fromObj[key] = newVal[k];
        }
      });
    });
    proms = ApiObj[newType].setForm(fromObj, loopType);
  } else {
    proms = ApiObj[newType].setForm(newVal, loopType);
  }

  if (!proms) {
    return;
  }

  // 现在接口还出完就先对部分接口
  if (
    newType == "rolandAOne" ||
    newType == "rolandATwo" ||
    newType == "channelTwo" ||
    newType == "channelOne" ||
    newType == "timeSynthesis" ||
    newType == "digitalRecorder" ||
    newType == "frequencyComparison"
  ) {
    //调用 获取图形的接口
    let chart = {};
    let tlabt = [];
    if (ApiObj[newType].url.chart) {
      // 更具接口特殊处理
      let typeName = newType + "chart";
      proms = setUrl(typeName, proms);
      chart = await apiAjax.get(ApiObj[newType].url.chart + proms);
    }
    if (ApiObj[newType].url.tlabt) {
      // 更具接口特殊处理
      let typeName = newType + "tlabt";
      proms = setUrl(typeName, proms);
      tlabt = await apiAjax.get(ApiObj[newType].url.tlabt + proms);
    }
    tlabt = setTabData(newType, tlabt);
    ApiObj[newType][loopType](chart, tlabt, fromObj, childDom.value);
    if (newType == "frequencyComparison") {
      // 有第三个请求需要
      let data = await apiAjax.get(ApiObj[newType].url.tlabt_two + proms);
      // let data = await apiAjax.get(
      //   ApiObj[newType].url.tlabt_two + "1777595000/1777595000/8750M,6000M"
      // );
      childDom.value[4].setTableName(setfrequencyTabe(data));
    }
  }
};

// 打开预处理
const onFormHow = () => {
  formdome.value.showDialogTable();
};

const activeName = ref("tab1");
const childTabSelected = ref({
  tab1: 0,
  tab2: 0,
  tab3: 0,
  tab4: 0,
  tab5: 0,
});
const childTabIndex = computed(() => {
  return childTabSelected.value[activeName.value];
});

const onChildTabClick = (index) => {
  childTabSelected.value[activeName.value] = index;
};

const hasChildTab = ref(false);

watch(
  activeName,
  (nv) => {
    hasChildTab.value = tabList.value.find(
      (item) => item.name === nv
    ).hasChildTab;
  },
  {
    immediate: true,
  }
);
</script>

<style lang="scss" scoped>
.con {
  :deep(.el-tabs) {
    .el-tabs__header {
      height: 56px;
      padding: 12px 16px;
      background: #fff;
      border-radius: 2px;
    }

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__item.is-top.is-active {
      background: #ecf2fe;
      border-radius: 3px;
    }

    .el-tabs__item {
      height: 32px;
      padding: 0 12px;
      margin-right: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  .top-bar-con {
    position: relative;
  }

  .top-bar {
    position: absolute;
    top: 12px;
    right: 16px;
    z-index: 1;

    .btn-group {
      margin-right: 16px;
    }
  }

  .card-header {
    font-weight: 600;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }

  .custom-tabs-label {
    display: flex;
    align-items: center;
  }

  .custom-tabs-label span {
    margin-left: 4px;
  }
}
</style>
