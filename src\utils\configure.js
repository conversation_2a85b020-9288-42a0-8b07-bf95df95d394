
import axios from 'axios';
import { localStg } from "@/utils/storage";
let typeObj = {
  "6780M": '6780M 南海-贺州',
  "6780X": '6780X 南海-饶平',
  "6780Y": '6780Y 南海-崇左',
  "7430M": '7430M 北海-荣成',
  "7430X": '7430X 北海-宣城',
  "7430Y": '7430Y 北海-和龙',
  "8390M": '8390M 东海-宣城',
  "8390X": '8390X 东海-饶平',
  "8390Y": '8390Y 东海-荣成',
  "6000M": '6000M 蒲城',
  "8750M": '8750M 敦煌',
  "7600M": "7600M 库尔勒",
  "6250M": "6250M 那曲"
}
const thoroughfare = (type) => {
  if (!typeObj[type]) {
    return "未知类型"
  } else {
    return typeObj[type]
  }
}
const stationAll = {
  "J01": "西安授时监测站",
  "J02": "敦煌授时监测站",
  "J03": "库尔勒授时监测站",
  "J04": "拉萨授时监测站",
  "J05": "西安监测中心站",
}
const stationAllLoginLong = stationAll[import.meta.env.VITE_SERVICE_BASE_TYPECODE]

// 创建柱状图
const createBarChart = (data) => {
  let dom = 800;
  let barWidth = dom / 20;
  let colors = []
  for (let i = 0; i < 4; i++) {
    colors.push({
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#73fcff' // 最左边
        }, {
          offset: 0.5,
          color: '#86eef1' // 左边的右边 颜色
        }, {
          offset: 0.5,
          color: '#5ad6d9' // 右边的左边 颜色
        }, {
          offset: 1,
          color: '#3dc8ca'
        }]
    })
  }
  return {
    //提示框
    tooltip: {
      trigger: 'axis',
      formatter: "{b} : {c}" + data.unit,
      axisPointer: { // 坐标轴指示器，坐标轴触发有效
        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
      }

    },
    /**区域位置*/
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '80',
    },
    //X轴
    xAxis: {
      data: data.xData,
      type: 'category',
      axisLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255,255,255,1)',
          shadowColor: 'rgba(255,255,255,1)',
          shadowOffsetX: '20',
        },
        symbol: ['none', 'arrow'],
        symbolOffset: [0, 25]
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        margin: 30,
        fontSize: 15,
      },
    },
    yAxis: {
      show: true,
      splitNumber: 4,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#075858'
        },
      },
      axisLabel: {
        color: '#FFFFFF',
        margin: 30,
        fontSize: 15
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: barWidth,
        itemStyle: {
          normal: {
            color: function (params) {
              return colors[params.dataIndex % 7];
            }
          }
        },
        label: {
          show: false,
          position: [barWidth / 2, -(barWidth + 20)],
          color: '#ffffff',
          fontSize: 14,
          fontStyle: 'bold',
          align: 'center',
        },
        data: data.yList
      },
      {
        z: 2,
        type: 'pictorialBar',
        data: data.yList,
        symbol: 'diamond',
        symbolOffset: [0, '50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          normal: {
            color: function (params) {
              return colors[params.dataIndex % 7];
            },
          }
        },
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        data: data.yList,
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          normal: {
            borderWidth: 0,
            color: function (params) {
              return colors[params.dataIndex % 7].colorStops[0].color;
            },

          }
        },
      },
    ],
  };
}
function isEmpty(value) {
  // 判断是否为对象或数组
  if (value === null || typeof value !== 'object') {
    return true
  }

  // 如果是数组或对象，进行空判断
  if (Array.isArray(value)) {
    // 如果是数组，检查其长度
    return value.length === 0;
  } else {
    // 如果是对象，检查其属性个数
    return Object.keys(value).length === 0;
  }
}
//格式化时间的
const setDate = (timeString, info) => {
  const year = parseInt(timeString.substring(0, 4), 10);
  const month = parseInt(timeString.substring(4, 6), 10) - 1; // 月份从0开始
  const day = parseInt(timeString.substring(6, 8), 10);
  const hours = parseInt(timeString.substring(8, 10), 10);
  const minutes = parseInt(timeString.substring(10, 12), 10);
  const seconds = parseInt(timeString.substring(12, 14), 10);
  // 创建 Date 对象
  const date = new Date(year, month, day, hours, minutes, seconds);
  // 获取时间戳（毫秒）
  const timestampMs = date.getTime();
  return timestampMs;
};

// 得到当前utc 时间或者是更具时间搓来计算utc 时间
const getUtcDate = (date = false) => {
  let utcDate = date ? new Date(date) : new Date();
  let utc = utcDate.toISOString().slice(0, -5) + utcDate.toISOString().slice(23, 24);
  return utc;
}
//当前得到N分钟前的时间戳
const getTimestampTwoMinutesAgo = (n = 2) => {
  // 获取当前时间戳
  const now = Date.now();

  // 计算N分钟的毫秒数
  const twoMinutesInMillis = n * 60 * 1000;

  // 计算两分钟之前的时间戳
  return getUtcDate(now - twoMinutesInMillis);
}

//去重函数
const duplicate = (list, key) => {
  return list.reduce((accumulator, current) => {
    const existing = accumulator.find(item => item[key] === current[key]);
    if (!existing) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);
}

// 更具字段长度 返回 对应的px
const setPx = (value) => {
  return value.length * 7
}

const download = async (newUrl) => {
  try {
    const token = localStg.get("token") || false;
    // 发送带有验证头的请求
    const response = await axios({
      url: newUrl,
      method: 'GET',
      responseType: 'blob', // 关键设置，表示响应类型为 blob
      headers: {
        'Authorization': `${token}` // 这里的 token 是你的验证头
      }
    });

    // 获取文件的 MIME 类型
    const contentType = response.headers['content-type'];
    const contentDisposition = response.headers['content-disposition'];
    const filenameMatch = contentDisposition && contentDisposition.match(/filename="(.+)"/);
    const filename = Date.now() + '.zip';

    // 创建一个 a 标签用于下载文件
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载文件时出错:', error);
  }
};

/**
 * 格式化时间戳
 * @param {number} timestamp - 秒级时间戳
 * @param {string} [timeZone='UTC'] - 时区 (当前实现固定为UTC, 此参数暂时无效)
 * @param {string} [format] - 输出格式 ('YYYY-MM-DD HH:mm:ss' 或 'HH:mm:ss')
 * @returns {string|null} 格式化后的时间字符串或null
 */
const formatTimestamps = (timestamp, timeZone = 'UTC', format = 'YYYY-MM-DD HH:mm:ss') => {
  if (timestamp === null || timestamp === undefined || isNaN(timestamp)) return null;
  try {
    const date = new Date(timestamp * 1000); // Convert seconds to milliseconds

    // 总是提取所有部分，因为可能需要它们
    const year = date.getUTCFullYear();
    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = date.getUTCDate().toString().padStart(2, '0');
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    const seconds = date.getUTCSeconds().toString().padStart(2, '0');

    // 根据 format 参数决定输出
    if (format === 'HH:mm:ss') {
      return `${hours}:${minutes}:${seconds}`;
    }

    // 默认返回完整格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error("Error formatting timestamp:", error);
    return null; // 出错时返回 null
  }
}

function numberToChinese(newnum) {
  const units = ["", "十", "百", "千", "万", "亿", "兆"];
  const digits = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  var num = newnum - 0;
  if (num === 0) return digits[0]; // 处理零

  let result = '';

  let unitPos = 0; // 当前单位位置
  let zeroFlag = false; // 是否需要加零

  // 从后往前处理数字
  while (num > 0) {
    const digit = num % 10; // 取出当前位的数字
    if (digit > 0) {
      result = digits[digit] + units[unitPos] + result; // 添加数字和单位
      zeroFlag = false; // 不需要加零
    } else if (!zeroFlag) {
      result = digits[0] + result; // 需要加零
      zeroFlag = true; // 下一次再遇到零时不再添加
    }

    num = Math.floor(num / 10); // 去掉当前位
    unitPos++; // 移动到下一位
  }

  // 处理万和亿之间的零，如 10101 应为“一万零一百零一”
  result = result.replace(/零+/g, "零"); // 替换多个零为一个
  result = result.replace(/零+$/, ""); // 去掉末尾的零

  return result;
}
// 默认选中前一天的状态
const setDefaultDates = (n = 1) => {
  const now = new Date();
  const yesterday = new Date();
  yesterday.setDate(now.getDate() - n); // 设置为前一天
  return [yesterday, now]; // 返回前一天到现在的时间范围
}

// 格式化时间  // 传入的是时间搓
function formatTimestamp(timestamp) {
  // 创建一个 Date 对象
  const date = new Date(timestamp);

  // 获取年、月、日、时、分、秒
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
  const day = String(date.getUTCDate()).padStart(2, '0');
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');

  // 拼接成指定格式的字符串
  const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

  return formatted;
}
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function addMinutes(date, minutes) {
  const newDate = new Date(date);
  const newMinutes = newDate.getMinutes() + minutes;
  newDate.setMinutes(newMinutes);

  return formatDate(newDate);
}
const newGetYMinMax = (newData, oldData) => {
  let absYmax = Math.abs(newData);
  let absYmin = Math.abs(oldData);
  let range = absYmax > absYmin ? absYmax : absYmin;
  // 看这个数 在那个最近的五的倍速中
  let rate = Math.ceil(range / 5) * 5;
  return {
    Ymax: rate,
    Ymin: -rate
  }
}




export default {
  thoroughfare,
  isEmpty,
  setDate,
  getUtcDate,
  getTimestampTwoMinutesAgo,
  duplicate,
  setPx,
  download,
  numberToChinese,
  setDefaultDates,
  typeObj,
  formatTimestamp,
  formatTimestamps,
  addMinutes,
  newGetYMinMax,
  stationAllLoginLong,
  createBarChart
}

