import { ref, reactive } from "vue";
import TopoIcon1 from "../../assets/topo/receiver.png";
import TopoIcon2 from "../../assets/topo/eq.png";

const chart2Data = reactive({
  tooltip: {},
  animationDurationUpdate: 1500,
  animationEasingUpdate: "quinticInOut",
  series: [
    {
      type: "graph",
      layout: "force",
      force: {
        // initLayout: 'circular',
        gravity: 0.2,
        animation: false,
        repulsion: 400,
        edgeLength: 200,
      },
      symbolSize: 50,
      roam: true,
      label: {
        show: false,
        // position: 'TOP',
      },
      edgeSymbol: ["circle", "arrow"],
      edgeSymbolSize: [4, 10],
      edgeLabel: {
        fontSize: 20,
      },
      data: [
        {
          id: "0",
          name: "单台站",
          symbol: `image://${TopoIcon1}`,
          x: 300,
          y: 300,
          label: {
            show: true,
          }
        },
        {
          id: "1",
          name: "网络时间服务器",
          symbol: `image://${TopoIcon2}`,
          x: 800,
          y: 300,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "2",
          name: "脉冲分配放大器",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 100,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "3",
          name: "频率分配放大器",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "4",
          name: "时间综合测量仪",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "5",
          name: "数字记录仪",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "6",
          name: "多通道时差测量软件",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "7",
          name: "频率比对测量系统",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "8",
          name: "频谱干扰测试系统",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "9",
          name: "自动气象站",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "10",
          name: "信号检测反馈平台",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "11",
          name: "天波信号分析平台",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'bottom',
          }
        },
        {
          id: "12",
          name: "授时发播分系统",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "13",
          name: "时频分系统",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "14",
          name: "eLORAN参数测量软件",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
        {
          id: "15",
          name: "监测运控子系统",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "16",
          name: "天波信号分析平台",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'top',
          }
        },
        {
          id: "17",
          name: "数据管理系统",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "18",
          name: "信号检测反馈平台",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'right',
          }
        },
        {
          id: "19",
          name: "eLORAN监测站总控平台",
          symbol: `image://${TopoIcon2}`,
          x: 550,
          y: 500,
          label: {
            show: true,
            position: 'left',
          }
        },
      ],
      links: [
        // {
        //   source: "1", target: "0", lineStyle: { color: "green" }, label: {
        //     show: true,
        //     position: 'left',
        //   }
        // },
        // {
        //   source: "2", target: "0", lineStyle: { color: "green" }, label: {
        //     show: true,
        //     position: 'right',
        //   }
        // },
        { source: "1", target: "0", lineStyle: { color: "#00C851" } }, // 正常连接-绿色
        { source: "2", target: "0", lineStyle: { color: "#00C851" } },
        { source: "3", target: "0", lineStyle: { color: "#00C851" } },
        { source: "4", target: "0", lineStyle: { color: "#00C851" } },
        { source: "5", target: "0", lineStyle: { color: "#00C851" } },
        { source: "6", target: "0", lineStyle: { color: "#00C851" } },
        { source: "7", target: "0", lineStyle: { color: "#00C851" } },
        { source: "8", target: "0", lineStyle: { color: "#00C851" } },
        { source: "9", target: "0", lineStyle: { color: "#00C851" } },
        { source: "10", target: "0", lineStyle: { color: "#00C851" } },
        { source: "11", target: "0", lineStyle: { color: "#00C851" } },
        { source: "12", target: "0", lineStyle: { color: "#00C851" } },
        { source: "13", target: "0", lineStyle: { color: "#00C851" } },
        { source: "14", target: "0", lineStyle: { color: "#00C851" } },
        { source: "14", target: "0", lineStyle: { color: "#00C851" } },
        { source: "15", target: "0", lineStyle: { color: "#00C851" } },
        // {
        //   source: "15",
        //   target: "0",
        //   lineStyle: { curveness: 0.2, color: "gray" },
        // },
        // {
        //   source: "0",
        //   target: "15",
        //   lineStyle: { curveness: 0.2, color: "green" },
        // },
        { source: "16", target: "0", lineStyle: { color: "#00C851" } },
        { source: "17", target: "0", lineStyle: { color: "#00C851" } },
        { source: "18", target: "0", lineStyle: { color: "#00C851" } },
        { source: "19", target: "0", lineStyle: { color: "#00C851" } },
        // {
        //   source: "19",
        //   target: "0",
        //   lineStyle: { curveness: 0.2, color: "green" },
        // },
        // {
        //   source: "0",
        //   target: "19",
        //   lineStyle: { curveness: 0.2, color: "green" },
        // },
      ],
      lineStyle: {
        opacity: 0.9,
        width: 2,
        curveness: 0,
      },
    },
  ],
});
export let deviceData = reactive({
  // 图表
  deviceState: {
    name: "设备状态",
    data: {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        orient: 'vertical',
        top: '80',
        right: "130",
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '60%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
          },
          emphasis: {
            label: {
              show: true
            }
          },
          labelLine: {
            show: true,
            length: 12,
          },
          center: ['30%', '50%'],
          data: [
            { value: 0, name: '正常', itemStyle: { color: '#00C851' } }, // 正常-绿色
            { value: 0, name: '异常', itemStyle: { color: '#FF4444' } }, // 异常-红色
            { value: 0, name: '离线', itemStyle: { color: '#9E9E9E' } }, // 离线-灰色
          ]
        }
      ]
    }
  },
  // 表格
  multiChannel: {
    name: "",
    data: []
  },
  // 网络拓扑连接
  topologyNetwork: {
    name: "网络拓扑连接",
    data: chart2Data
  },

})


