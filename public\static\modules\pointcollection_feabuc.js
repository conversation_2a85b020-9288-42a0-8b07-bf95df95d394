_jsload2&&_jsload2('pointcollection', 'z.extend(zc.prototype,{na:function(a){var b=this;b.map=this.B=a;b.zc=document.createElement("canvas");b.zc.style.cssText="position: absolute; left: 0; top: 0;";b.V=b.zc;a=b.map.cb();b.zc.width=a.width;b.zc.height=a.height;b.zc.id="myCanvasElement";b.map.Tf().Et.appendChild(b.zc);b.Py=new Xf;b.di();b.map.addEventListener("click",function(a){a=Yf(b.Py,a.offsetX,a.offsetY);a!==s&&b.dispatchEvent("click",{point:a.point})});b.Tk=s;b.map.addEventListener("mousemove",function(a){for(var e=b.map.Hx(),f=e.length, g=s,i=0;i<f&&!(e[i]instanceof B.PointCollection&&(g=Yf(e[i].Py,a.offsetX,a.offsetY),g!==s));i++);g===s?(b.Tk!==s&&(b.dispatchEvent("mouseout",{point:b.Tk.point}),b.Tk=s),b.zc.style.cursor=""):(b.Tk===s?(b.dispatchEvent("mouseover",{point:g.point}),b.Tk=g):b.Tk!==g&&(b.dispatchEvent("mouseout",{point:b.Tk.point}),b.dispatchEvent("mouseover",{point:g.point}),b.Tk=g),b.zc.style.cursor="pointer")});return b.zc},di:function(){this.Py.data.MF=[];var a=this.map.cb();this.zc.width=a.width;this.zc.height= a.height;this.zc.style.left=this.map.ze.style.left;this.zc.style.top=this.map.ze.style.top;if(this.ea.ja&&this.ea.ja.length){this.zc.getContext("2d").clearRect(0,0,this.zc.width,this.zc.height);-1!==document.location.href.indexOf(".local")&&(console&&console.time)&&console.time("\\u904d\\u5386\\u7ed8\\u5236\\u9ebb\\u70b9");this.zc.getContext("2d").beginPath();for(var b=0,c;c=this.ea.ja[b];b++){var e=this.map.$b(c);if(!(0>e.x||0>e.y||e.x>a.width||e.y>a.height)){var f;switch(this.z.shape){case 1:f=new Zf; break;case 2:f=new $f;break;case 4:f=new ag;break;case 5:f=new cg;break;case wc:f=new dg;break;default:f=new dg}switch(this.z.size){case 1:("circle"===f.type||"star"===f.type)&&f.xf(1);if("rectangle"===f.type||"rhombus"===f.type)f.zh(2),f.xh(2);break;case 2:("circle"===f.type||"star"===f.type)&&f.xf(2);if("rectangle"===f.type||"rhombus"===f.type)f.zh(4),f.xh(4);break;case 3:("circle"===f.type||"star"===f.type)&&f.xf(4);if("rectangle"===f.type||"rhombus"===f.type)f.zh(8),f.xh(8);break;case xc:("circle"=== f.type||"star"===f.type)&&f.xf(5);if("rectangle"===f.type||"rhombus"===f.type)f.zh(10),f.xh(10);break;case 5:("circle"===f.type||"star"===f.type)&&f.xf(8);if("rectangle"===f.type||"rhombus"===f.type)f.zh(16),f.xh(16);break;case 6:("circle"===f.type||"star"===f.type)&&f.xf(10);if("rectangle"===f.type||"rhombus"===f.type)f.zh(20),f.xh(20);break;case 7:("circle"===f.type||"star"===f.type)&&f.xf(15);if("rectangle"===f.type||"rhombus"===f.type)f.zh(30),f.xh(30);break;default:if(("circle"===f.type||"star"=== f.type)&&f.xf(5),"rectangle"===f.type||"rhombus"===f.type)f.zh(10),f.xh(10)}this.z.color&&("circle"===f.type||"star"===f.type||"rectangle"===f.type||"rhombus"===f.type)&&f.Pk(this.z.color);f.Qo(this.zc.getContext("2d"),e.x,e.y);f.point=c;this.Py.data.MF.push(f)}}this.zc.getContext("2d").fillStyle=this.z.color;this.zc.getContext("2d").fill();-1!==document.location.href.indexOf(".local")&&(console&&console.timeEnd)&&console.timeEnd("\\u904d\\u5386\\u7ed8\\u5236\\u9ebb\\u70b9")}},I_:function(a){this.ea.ja= a;this.di()},ti:function(a){a.color&&(this.z.color=a.color);a.size&&(this.z.size=a.size);a.shape&&(this.z.shape=a.shape);this.di()},clear:function(){this.ea.ja=[];this.di()},remove:function(){this.clear();this.zc&&this.zc.parentNode&&this.zc.parentNode.removeChild(this.zc);this.dispatchEvent(new P("onremove"))}});zc.prototype.initialize=zc.prototype.na;zc.prototype.draw=zc.prototype.di;zc.prototype.setPoints=zc.prototype.I_;zc.prototype.setStyles=zc.prototype.ti;zc.prototype.clear=zc.prototype.clear; zc.prototype.remove=zc.prototype.remove;function Xf(){this.data={MF:[]}}function Yf(a,b,c){for(var e=0,f;f=a.data.MF[e];e++)if(f.yk(),b>f.position.x-f.jp()/2&&b<f.position.x+f.jp()/2&&c>f.position.y-f.yk()/2&&c<f.position.y+f.yk()/2)return f;return s}function $f(){this.type="waterdrop";this.position={x:0,y:0}}var eg=document.createElement("img"),fg=t;eg.onload=function(){fg=q};eg.src=B.ka+"images/point-collection/red-marker-10x13.png";var gg=document.createElement("img");gg.onload=u(); gg.src=B.ka+"images/point-collection/blue-marke-15x16.png";$f.prototype.Qo=function(a,b,c){var e=this;fg===t?setTimeout(function(){e.Qo(a,b,c)},10):(e.z2=a,e.position.x=b,e.position.y=c,a.save(),a.translate(b-eg.width/2,c-eg.height/2),a.drawImage(eg,0,0,eg.width,eg.height,0,0,eg.width,eg.height),a.restore())};$f.prototype.ga=w("position");$f.prototype.jp=function(){return eg.width};$f.prototype.yk=function(){return eg.height}; function dg(a){this.type="circle";a=a||{};this.K={wa:a.radius||10,color:a.color||"#fa937e"};this.position={x:0,y:0}}da=dg.prototype;da.Qo=function(a,b,c){this.position.x=b;this.position.y=c;a.save();a.translate(b,c);a.beginPath();a.fillStyle=this.K.color;a.arc(0,0,this.K.wa,0,2*Math.PI,t);a.fill();a.restore()};da.xf=function(a){this.K.wa=a};da.RL=function(){return this.K.wa};da.Pk=function(a){this.K.color=a};da.ga=w("position");da.jp=function(){return 2*this.K.wa};da.yk=function(){return 2*this.K.wa}; function Zf(a){this.type="star";a=a||{};this.K={wa:a.radius||10,jF:a.pointsNumber||5,nX:a.fraction||0.4,color:a.color||"#fa937e"};this.position={x:0,y:0}}da=Zf.prototype;da.Qo=function(a,b,c){this.position.x=b;this.position.y=c;a.save();a.fillStyle=this.K.color;a.beginPath();a.translate(b,c);a.moveTo(0,0-this.K.wa);for(b=0;b<this.K.jF;b++)a.rotate(Math.PI/this.K.jF),a.lineTo(0,0-this.K.wa*this.K.nX),a.rotate(Math.PI/this.K.jF),a.lineTo(0,0-this.K.wa);a.fill();a.restore()}; da.xf=function(a){this.K.wa=a};da.Pk=function(a){this.K.color=a};da.ga=w("position");da.jp=function(){return 2*this.K.wa};da.yk=function(){return 2*this.K.wa};function ag(a){this.type="rectangle";a=a||{};this.K={width:a.width||10,height:a.height||10,color:a.color||"#fa937e"};this.position={x:0,y:0}}da=ag.prototype;da.Qo=function(a,b,c){this.position.x=b;this.position.y=c;a.save();a.translate(b-this.K.width/2,c-this.K.height/2);a.fillStyle=this.K.color;a.fillRect(0,0,this.K.width,this.K.height);a.restore()}; da.zh=function(a){this.K.width=a};da.jp=function(){return this.K.width};da.xh=function(a){this.K.height=a};da.yk=function(){return this.K.height};da.Pk=function(a){this.K.color=a};da.ga=w("position");function cg(a){this.type="rhombus";a=a||{};this.K={width:a.width||10,height:a.height||10,color:a.color||"#fa937e"};this.position={x:0,y:0}}da=cg.prototype; da.Qo=function(a,b,c){this.position.x=b;this.position.y=c;a.save();a.fillStyle=this.K.color;a.translate(b-this.K.width/2,c-this.K.height/2);a.beginPath();a.moveTo(0+0.5*this.K.width,0);a.lineTo(0,0+0.5*this.K.height);a.lineTo(0+0.5*this.K.width,0+this.K.height);a.lineTo(0+this.K.width,0+0.5*this.K.height);a.lineTo(0+0.5*this.K.width,0);a.closePath();a.fill();a.restore()};da.zh=function(a){this.K.width=a};da.jp=function(){return this.K.width};da.xh=function(a){this.K.height=a};da.yk=function(){return this.K.height}; da.Pk=function(a){this.K.color=a};da.ga=w("position"); ');