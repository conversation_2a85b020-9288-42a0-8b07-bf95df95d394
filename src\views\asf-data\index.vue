<template>
  <div class="con">
    <div class="filter-con">
      <el-form inline :model="form">
        <el-form-item class="date-item" label="时间选择">
          <el-date-picker v-model="form.date" format="YYYY-MM-DD HH:mm:ss" type="datetimerange" range-separator="-"
            start-placeholder="开始时间" end-placeholder="结束时间" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="search-btn">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card class="card">
      <template #header>
        <div class="card-header">
          <span>ASF数据</span>
        </div>
      </template>

      <div class="top-con">
        <lineChart ref="lineChart1" class="chart-area" :data="data.chartData" />
        <div class="right-chart">
          ASF数据模型
        </div>
      </div>

      <el-table :data="data.tableData || []" border style="width: 100%;">
        <el-table-column v-for="col in (data.tableColumn || [])" :key="col.name" v-bind="col" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import { chartColors } from "@/constants/chart";
import { useASFData } from "@/store/modules/asf-data";

const store = useASFData();

const form = reactive({
  date: '',
})

const lineChart1 = ref(null);

const getRandomTableRowData = (st = '', et = '') => {
  return {
    st: `07-16 ${st}`,
    et: `07-16 ${et}`,
    max: 30 + Math.floor(Math.random() * 350),
    min: 30 + Math.floor(Math.random() * 350),
    sd: 30 + Math.floor(Math.random() * 350),
    rms: 30 + Math.floor(Math.random() * 350),
    p95quantile: 30 + Math.floor(Math.random() * 350),
  }
}

const data = reactive({
  chartData: {
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: "value",
    },
    legend: {
      bottom: 10,
      itemStyle: { opacity: 0 },
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: '#2B2E3F',
        lineHeight: 20,
        fontStyle: 'normal',
      },
      data: [],
    },
    grid: {
      top: "10px",
      left: "50px",
      right: "30px",
      bottom: "60px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [
      {
        data: [],
        type: "line",
        showSymbol: false,
        itemStyle: { color: chartColors[0] },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(25, 120, 255, 0.2)'  // 0% 处的颜色
            }, {
              offset: 1, color: 'rgba(255, 255, 255, 0)'  // 100% 处的颜色
            }],
          },
          origin: 'start'  // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: 'solid'
        },
      }
    ],
  },
  tableColumn: [
    { prop: 'st', label: '开始时间' },
    { prop: 'et', label: '结束时间' },
    { prop: 'max', label: '最大值' },
    { prop: 'min', label: '最小值' },
    { prop: 'sd', label: '标准偏差' },
    { prop: 'rms', label: 'RMS' },
    { prop: 'p95quantile', label: '95%分位数' },
  ],
  tableData: [
    getRandomTableRowData('00:00', '02:00'),
    getRandomTableRowData('02:00', '04:00'),
    getRandomTableRowData('04:00', '06:00'),
    getRandomTableRowData('06:00', '08:00'),
    getRandomTableRowData('08:00', '10:00'),
    getRandomTableRowData('10:00', '12:00'),
    getRandomTableRowData('12:00', '14:00'),
    getRandomTableRowData('14:00', '16:00'),
    getRandomTableRowData('16:00', '18:00'),
    getRandomTableRowData('18:00', '20:00'),
    getRandomTableRowData('20:00', '22:00'),
    getRandomTableRowData('22:00', '24:00'),
  ],
});

let xAxisData = [];
let seriesData = [];
for (let i = 0; i < 24; i++) {
  xAxisData.push(`${i}:00`);
  seriesData.push((10 + Math.random() * 90).toFixed(1))
}
data.chartData.xAxis.data = xAxisData;
data.chartData.series[0].data = seriesData;

const onExport = () => {
  console.log('onExport',data.chartData)
}

</script>

<style lang="scss" scoped>
.con {
  :deep(.el-table) {
    margin-top: 10px;
    --el-table-border-color: #E8E8E8;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 8px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__cell {
      border-right: none;
    }

    .el-table__header {
      height: 46px;

      .cell {
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #FAFAFA;
      }
    }
  }

  .filter-con {
    height: 80px;
    background: #FFF;
    border-radius: 2px;
    padding: 0 24px;
    display: flex;
    align-items: center;

    .item-size {
      width: 180px;
    }

    .item-multi-size {
      width: 440px;
    }

    .date-item {
      width: 467px;
    }

    .select-label {
      display: inline;
      margin-left: -14px;
      margin-right: 10px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }

    .hide-select-label {
      display: none;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }

    .date-picker {
      width: 325px;
    }

    .date-type {
      width: 325px;
    }

    .search-btn {
      margin-left: 12px;
      margin-right: 4px;
    }
  }

  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
  }

  .card {
    margin-top: 16px;
    background: #FFF;
    border-radius: 2px;

    :deep(.el-card__body) {
      padding: 24px 16px;
    }

    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .top-con {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .chart-area {
        width: calc(100% - 400px);
        height: 380px;
      }

      .right-chart {
        width: 370px;
        height: 370px;
        border: 1px solid #DCDCDC;

        .pie-chart {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
