<template>
  <div class="con">
    <div class="filter-con">
      <el-form inline :model="form">
        <el-form-item class="date-item" label="时间选择">
          <el-date-picker
            v-model="form.date"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="search-btn" @click="getData"
            >查询</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card class="card">
      <template #header>
        <div class="card-header">
          <span
            v-for="item in Object.keys(objWeatherData)"
            :key="item"
            :class="{ titleName: true, titleInfo: item == navData }"
            @click="setName(item)"
          >
            {{ objWeatherData[item].title }}
          </span>
        </div>
      </template>

      <div class="top-con">
        <EChartComponent
          ref="mainChart"
          @onSearchEchart="onSearchEchart"
          :isdataZoom="true"
        />
      </div>

      <el-table
        :data="objWeatherData[navData].tableData || []"
        border
        style="width: 100%"
      >
        <el-table-column
          v-for="col in tableNameColumn || []"
          :key="col.prop"
          v-bind="col"
        >
          <template #default="scope">
            <div v-if="col.prop == 'startTime'">
              {{ formatTime(scope.row[col.prop]) }}
            </div>
            <div v-else-if="col.prop == 'endTime'">
              {{ formatTime(scope.row[col.prop]) }}
            </div>
            <div v-else>{{ scope.row[col.prop] }}</div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref, watch, defineProps, onMounted } from "vue";
import { chartColors } from "@/constants/chart";
import apiAjax from "@/api/index";
import EChartComponent from "./EChartComponent.vue";
import { ElMessage } from "element-plus";

const mainChart = ref();
const onSearchEchart = () => {
  console.log("435434");
};

// 获取数据
const getData = async () => {
  if (!form.date || !form.date[0] || !form.date[1]) {
    ElMessage.warning("请选择时间范围");
    return;
  }

  const startTimestamp = Math.floor(form.date[0].getTime() / 1000);
  const endTimestamp = Math.floor(form.date[1].getTime() / 1000);

  let chartData = await apiAjax.get(
    `api/jnx/dataManager/getAwsRealtime/${startTimestamp}/${endTimestamp}/1920`,
  );
  let tlabtData = await apiAjax.get(
    `api/jnx/dataManager/getAwsFormData/${startTimestamp}/${endTimestamp}`,
  );

  // 清空之前的数据
  Object.keys(objWeatherData).forEach((i) => {
    objWeatherData[i].tableData = [];
  });

  Object.keys(objWeatherData).forEach((i) => {
    objWeatherData[i].chartData = chartData[i];
    let obj = {};
    Object.keys(objWeatherData[i].tableObj).forEach((key) => {
      obj[objWeatherData[i].tableObj[key]] = tlabtData[0][key];
    });
    objWeatherData[i].tableData.push(obj);
  });
  let key = objWeatherData[navData.value].title;
  let mainChartData = objWeatherData[navData.value].chartData;
  mainChart.value.initChart({ [key]: mainChartData });
};

const colors = ["#3B8CFF", "#F1F2F3"];
const tableNameColumn = [
  { prop: "startTime", label: "开始时间" },
  { prop: "endTime", label: "结束时间" },
  { prop: "min", label: "最小值" },
  { prop: "max", label: "最大值" },
  { prop: "avg", label: "均值" },
];
// 1752508800-这个转化成-2025-07-16 00:00:00
const formatTime = (time) => {
  return new Date(time * 1000).toLocaleString().split('/').join('-');
};
const objWeatherData = reactive({
  temperature: {
    title: "温度",
    chartData: [],
    tableData: [],
    tableObj: {
      startTime: "startTime",
      endTime: "endTime",
      avgTemperature: "avg",
      maxTemperature: "max",
      minTemperature: "min",
    },
  },
  humidity: {
    title: "湿度",
    chartData: [],
    tableData: [],
    tableObj: {
      startTime: "startTime",
      endTime: "endTime",
      avgHumidity: "avg",
      maxHumidity: "max",
      minHumidity: "min",
    },
  },
  airPressure: {
    title: "大气压",
    chartData: [],
    tableData: [],
    tableObj: {
      startTime: "startTime",
      endTime: "endTime",
      avgAirPressure: "avg",
      maxAirPressure: "max",
      minAirPressure: "min",
    },
  },
  rainfall: {
    title: "雨量",
    chartData: [],
    tableData: [],
    tableObj: {
      startTime: "startTime",
      endTime: "endTime",
      avgRainfall: "avg",
      maxRainfall: "max",
      minRainfall: "min",
    },
  },
});

const navData = ref("temperature");

const form = reactive({
  date: "",
});

// 页面加载时先设置默认时间范围
onMounted(() => {
  // 设置默认时间范围为昨天到今天
  const now = new Date();

  // 昨天的开始时间 00:00:00
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0);

  // 今天的结束时间 23:59:59
  const today = new Date(now);
  today.setHours(23, 59, 59, 999);

  form.date = [yesterday, today];
  getData();
});

const setName = (data) => {
  navData.value = data;
  let key = objWeatherData[data].title;
  let mainChartData = objWeatherData[data].chartData;
  mainChart.value.initChart({ [key]: mainChartData });
};
</script>

<style lang="scss" scoped>
.con {
  :deep(.el-table) {
    margin-top: 10px;
    --el-table-border-color: #e8e8e8;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 8px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__cell {
      border-right: none;
    }

    .el-table__header {
      height: 46px;

      .cell {
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }

  .filter-con {
    height: 80px;
    background: #fff;
    border-radius: 2px;
    padding: 0 24px;
    display: flex;
    align-items: center;

    .item-size {
      width: 180px;
    }

    .item-multi-size {
      width: 440px;
    }

    .date-item {
      width: 467px;
    }

    .select-label {
      display: inline;
      margin-left: -14px;
      margin-right: 10px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }

    .hide-select-label {
      display: none;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }

    .date-picker {
      width: 325px;
    }

    .date-type {
      width: 325px;
    }

    .search-btn {
      margin-left: 12px;
      margin-right: 4px;
    }
  }

  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
  }

  .card {
    margin-top: 16px;
    background: #fff;
    border-radius: 2px;

    :deep(.el-card__body) {
      padding: 24px 16px;
    }

    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .top-con {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .chart-area {
        width: 100%;
        height: 380px;
      }

      .right-chart {
        width: 370px;
        height: 370px;
        border: 1px solid #dcdcdc;

        .pie-chart {
          width: 100%;
          height: 100%;
        }

        .message {
          position: relative;
          top: -110px;
          left: 40px;
          width: 300px;

          .total {
            margin-bottom: 12px;
            font-weight: 600;
            font-size: 14px;
            color: #2b2e3f;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }

          .item {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #2b2e3e;
            line-height: 17px;
            text-align: left;
            font-style: normal;

            .rect {
              width: 8px;
              height: 8px;
              margin-right: 5px;
            }

            .s1 {
              width: 120px;
            }

            .s2 {
              width: 30px;
            }

            .s3 {
              margin-right: 17px;
            }

            .s4 {
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
}
.titleName {
  margin: 0 10px;
  cursor: pointer;
}
.titleInfo {
  color: #3b8cff;
}
</style>
