import { ref, reactive } from 'vue'
export const tableData = [
  {
    date: "2016-05-04",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "1",
    state: 1,
    address: "Lohrbergstr. 86c, Süd <PERSON>li, Saarland",
  },
  {
    date: "2016-05-03",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 2,
    address: "760 A Street, South Frankfield, Illinois",
  },
  {
    date: "2016-05-02",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "3",
    state: 1,
    address: "Arnold-Ohletz-Str. 41a, Alt Malinascheid, Thüringen",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 2,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "1",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "1",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },

  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "3",
    state: 2,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "3",
    state: 2,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },

  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },

  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
  {
    date: "2016-05-01",
    name: "接收机1",
    content: "接收机异常",
    typesOf: "2",
    state: 1,
    address: "23618 Windsor Drive, West Ricardoview, Idaho",
  },
];
export const deviceData = {
  barStack: {
    name: "告警趋势",
    data: {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ["一级", "二级", '三级'],
        bottom: 0
      },
      grid: {
        top: "10%",
        left: "3%",
        right: "4%",
        bottom: "20%",
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: ['07-10', '07-11', '07-12', '07-13', '07-14']
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: '一级',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series'
          },
          data: [120, 132, 101, 134, 90, 230, 210]
        },
        {
          name: '二级',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series'
          },
          data: [220, 182, 191, 234, 290, 330, 310]
        },
        {
          name: '三级',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series'
          },
          data: [150, 232, 201, 154, 190, 330, 410]
        },

      ]
    },

  },
  deviceState: {
    name: "设备状态",
    data: {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        orient: 'vertical',
        top: '100',
        right: "160",
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '60%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
          },
          emphasis: {
            label: {
              show: true
            }
          },
          labelLine: {
            show: true,
            length: 15, // 调整引导线的长度，单位为像素
          },
          center: ['30%', '50%'], //
          data: [
            { value: 38, name: '正常', color: '#0BC4A7' },
            { value: 15, name: '在线', color: '#1978FF' },
            { value: 10, name: '异常', color: '#FB7307' },
            { value: 24, name: '离线', color: '#E34D59' },
          ]
        }
      ]
    }
  },
}

