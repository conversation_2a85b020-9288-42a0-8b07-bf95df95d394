// vite.config.ts
import process4 from "node:process";
import { URL, fileURLToPath } from "node:url";
import { defineConfig, loadEnv } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite@5.3.1_@types+node@20.14.8_sass@1.77.6/node_modules/vite/dist/node/index.js";

// build/plugins/index.ts
import vue from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import VueDevtools from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.4_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import progress from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-progress@0.0.7_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-progress/dist/index.mjs";

// build/plugins/router.ts
import ElegantVueRouter from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@elegant-router+vue@0.3.7/node_modules/@elegant-router/vue/dist/vite.mjs";
function setupElegantRouter() {
  return ElegantVueRouter({
    layouts: {
      base: "src/layouts/base-layout/index.vue",
      blank: "src/layouts/blank-layout/index.vue"
    },
    customRoutes: {
      names: [
        "exception_403",
        "exception_404",
        "exception_500",
        "document_project",
        "document_project-link",
        "document_vue",
        "document_vite",
        "document_naive",
        "document_antd"
      ]
    },
    routePathTransformer(routeName, routePath) {
      const key = routeName;
      if (key === "login") {
        const modules = ["pwd-login", "code-login", "register", "reset-pwd", "bind-wechat"];
        const moduleReg = modules.join("|");
        return `/login/:module(${moduleReg})?`;
      }
      return routePath;
    },
    onRouteMetaGen(routeName) {
      const key = routeName;
      const constantRoutes = ["login", "403", "404", "500"];
      const meta = {
        title: key
      };
      if (constantRoutes.includes(key)) {
        meta.constant = true;
      }
      return meta;
    }
  });
}

// build/plugins/unocss.ts
import process2 from "node:process";
import path from "node:path";
import unocss from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@unocss+vite@0.61.0_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/@unocss/vite/dist/index.mjs";
import presetIcons from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@unocss+preset-icons@0.61.0/node_modules/@unocss/preset-icons/dist/index.mjs";
import { FileSystemIconLoader } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@iconify+utils@2.1.25/node_modules/@iconify/utils/lib/loader/node-loaders.mjs";
function setupUnocss(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path.join(process2.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  return unocss({
    presets: [
      presetIcons({
        prefix: `${VITE_ICON_PREFIX}-`,
        scale: 1,
        extraProperties: {
          display: "inline-block"
        },
        collections: {
          [collectionName]: FileSystemIconLoader(
            localIconPath,
            (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
          )
        },
        warn: true
      })
    ]
  });
}

// build/plugins/unplugin.ts
import process3 from "node:process";
import path2 from "node:path";
import Icons from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/resolver.js";
import Components from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/vite.js";
import { AntDesignVueResolver, NaiveUiResolver } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/resolvers.js";
import { FileSystemIconLoader as FileSystemIconLoader2 } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/loaders.js";
import { createSvgIconsPlugin } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function setupUnplugin(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path2.join(process3.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  const plugins = [
    Icons({
      compiler: "vue3",
      customCollections: {
        [collectionName]: FileSystemIconLoader2(
          localIconPath,
          (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        )
      },
      scale: 1,
      defaultClass: "inline-block"
    }),
    Components({
      dts: "src/typings/components.d.ts",
      types: [{ from: "vue-router", names: ["RouterLink", "RouterView"] }],
      resolvers: [
        AntDesignVueResolver({
          importStyle: false
        }),
        NaiveUiResolver(),
        IconsResolver({ customCollections: [collectionName], componentPrefix: VITE_ICON_PREFIX })
      ]
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: "body-last",
      customDomId: "__SVG_ICON_LOCAL__"
    })
  ];
  return plugins;
}

// build/plugins/html.ts
function setupHtmlPlugin(buildTime) {
  const plugin = {
    name: "html-plugin",
    apply: "build",
    transformIndexHtml(html) {
      return html.replace("<head>", `<head>
    <meta name="buildTime" content="${buildTime}">`);
    }
  };
  return plugin;
}

// build/plugins/index.ts
function setupVitePlugins(viteEnv, buildTime) {
  const plugins = [
    vue({
      script: {
        defineModel: true
      }
    }),
    vueJsx(),
    VueDevtools(),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime)
  ];
  return plugins;
}

// build/config/time.ts
import dayjs from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/dayjs.min.js";
import utc from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/utc.js";
import timezone from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/timezone.js";
function getBuildTime() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const buildTime = dayjs.tz(Date.now(), "Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");
  return buildTime;
}

// src/api/config/index.js
var proxySource = {
  "/logApi": "http://************:8087",
  // 外网测试环境
  "/Api": "http://************:8080"
  // 外网测试环境
};
var proxyRoot = [];
for (let key in proxySource) {
  proxyRoot.push(
    process.env.NODE_ENV === "production" ? proxySource[key] : key + "/"
  );
}
var config_default = {
  prefix: "temp_",
  //项目前缀
  xAppVersion: "1.0.0",
  //版本
  base_path: "",
  //  项目地址
  proxySource,
  //代理源配置
  root: proxyRoot,
  // 配置API接口地址
  proxyUrl: {
    defult: ""
  },
  publicKey: "LTAI4GKXdB0hcaFS78629b526a153b17",
  //app 加密密钥
  publicKeyRecharge: "jstxbetcUroad123",
  //圈存加密密钥
  publicKeyMd5Recharge: "LTAI4GKXdB0hcaFS78629b526a153b17",
  isTest: false
  //是否测试环境
};

// vite.config.ts
var __vite_injected_original_import_meta_url = "file:///D:/jnx/single-station-pc/vite.config.ts";
var proxy = {};
for (let key in config_default.proxySource) {
  proxy[key] = {
    target: config_default.proxySource[key],
    changeOrigin: true,
    rewrite: (path3) => path3.replace(`${key}`, "")
  };
}
console.log(proxy);
var vite_config_default = defineConfig((configEnv) => {
  const viteEnv = loadEnv(configEnv.mode, process4.cwd());
  const buildTime = getBuildTime();
  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        "~": fileURLToPath(new URL("./", __vite_injected_original_import_meta_url)),
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    plugins: setupVitePlugins(viteEnv, buildTime),
    define: {
      BUILD_TIME: JSON.stringify(buildTime)
    },
    // server: {
    //   host: '0.0.0.0',
    //   port: 9527,
    //   open: true,
    //   proxy: createViteProxy(viteEnv, configEnv.command === 'serve'),
    //   fs: {
    //     cachedChecks: false
    //   }
    // },
    server: {
      host: "0.0.0.0",
      proxy
    },
    preview: {
      port: 9725
    },
    build: {
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP === "Y",
      commonjsOptions: {
        ignoreTryCatch: false
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
