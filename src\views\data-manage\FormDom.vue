<template>
  <el-dialog v-model="dialogTableVisible" title="预处理" width="500">
    <div class="filter">
      <div class="filter-item">
        <el-checkbox v-model="formData.isRoughFilter">粗差剔除</el-checkbox>
        <div class="slider-demo-block">
          <span class="demonstration">范围</span>
          <el-input
            style="margin-right: 5px"
            placeholder="最大值"
            v-model="formData.max"
          ></el-input>
          <span>-</span>
          <el-input
            style="margin-left: 5px"
            placeholder="最小值"
            v-model="formData.min"
          ></el-input>
        </div>
      </div>
      <div class="filter-item">
        <el-checkbox v-model="formData.isDataRepair">数据修补</el-checkbox>
        <el-select
          v-model="formData.repairMethod"
          style="width: 250px; margin-left: 20px"
          placeholder="请选择修补方法"
        >
          <el-option label="线性插值" value="linear"></el-option>
          <el-option label="纵数插补" value="longitudinal"></el-option>
          <el-option label="均值插补" value="mean"></el-option>
          <el-option label="回归插补" value="regression"></el-option>
        </el-select>
      </div>
    </div>
    <div class="food">
      <el-button type="primary">确定</el-button>
      <el-button @click="dialogTableVisible = false">取消</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
const dialogTableVisible = ref(false);
const showDialogTable = () => {
  dialogTableVisible.value = true;
  formData.value = init;
};

const formData = ref({
  isRoughFilter: false,
  range: "",
  isDataRepair: false,
  repairMethod: "",
  slider: 0,
});
const init = {
  isRoughFilter: false,
  range: "",
  isDataRepair: false,
  repairMethod: "",
  slider: 0,
};
defineExpose({
  showDialogTable,
});
</script>
<style lang="scss" scoped>
.filter {
  display: flex;
  flex-direction: column;
  .filter-item {
    display: flex;
    margin: 10px;
  }
  .slider-demo-block {
    display: flex;
    height: 30px;
    align-items: center;
    width: 250px;

    .demonstration {
      width: 100px;
      margin-left: 20px;
    }
    .slider {
      flex: 1;
    }
  }
}
.food {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
