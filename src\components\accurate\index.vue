<template>
  <div class="accurate">
    <div class="target-container">
      <!-- 从大到小渲染，确保小的环在上层 -->
      <div
        v-for="ring in [...accuracyList].reverse()"
        :key="ring.id"
        class="target-ring"
        :class="{
          active:
            props.accuracy > 0 &&
            props.accuracy <= ring.value &&
            props.accuracy >
              (ring.id > 1 ? accuracyList[ring.id - 2].value : 0),
        }"
        :style="{
          width: `${ring.radius * 2}px`,
          height: `${ring.radius * 2}px`,
          backgroundColor: ring.color,
        }"
      ></div>
    </div>
    <!-- <div class="accuracy-value" v-if="props.accuracy">{{ props.accuracy }}</div> -->
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
const props = defineProps({
  accuracy: {
    type: Number,
    default: 0,
  },
});
const accuracyList = reactive([
  { id: 1, radius: 15, value: 10, color: "#4CAF50" }, // 绿色中心
  { id: 2, radius: 38, value: 100, color: "#FFEB3B" }, // 黄色中环
  { id: 3, radius: 55, value: 10000, color: "rgba(244, 67, 54,.6)" }, // 深红色最外环
]);
</script>
<style scoped lang="scss">
.accurate {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // padding: 20px;

  .target-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .target-ring {
      position: absolute;
      border-radius: 50%;
      opacity: 1; // 未激活状态的环设置为较低透明度
      // box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
      transition: opacity 0.3s ease;

      &.active {
        opacity: 1; // 激活状态保持完全不透明
        // box-shadow: 0 0 10px currentColor;
        animation: pulse 1.5s infinite;
      }
    }
  }

  .accuracy-value {
    margin-top: 20px;
    font-size: 18px;
    font-weight: bold;
  }
}

@keyframes pulse {
  0% {
    // box-shadow: 0 0 5px currentColor;
    transform: scale(1);
  }
  50% {
    // box-shadow: 0 0 15px currentColor, 0 0 5px currentColor;
    transform: scale(1.1);
  }
  100% {
    // box-shadow: 0 0 5px currentColor;
    transform: scale(1);
  }
}
</style>
