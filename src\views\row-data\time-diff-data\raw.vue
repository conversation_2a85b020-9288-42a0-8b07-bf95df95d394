<template>
  <div class="con">
    <div class="top-con">
      <div class="tabContent">
        <el-select
          v-model="rawData.typeMapIndex"
          placeholder="请选择"
          class="item select"
        >
          <el-option
            key="1PPS时差"
            label="1PPS时差"
            value="1PPS时差"
          ></el-option>
          <el-option key="GTP" label="GTP" value="GTP"></el-option>
        </el-select>
        <el-select
          v-model="rawData.receiverchIndex"
          placeholder="请选择"
          class="select"
        >
          <el-option
            v-for="item in rawData.receiverchList"
            :key="item.key"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <lineChart
          ref="lineChartRef"
          class="chartArea"
          :data="rawData.chartData"
        ></lineChart>
      </div>
    </div>
    <el-table
      :data="rawData.tableData || []"
      border
      max-height="2000"
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column align="center" label="设备">
        <template #default="{ row }">
          <div>
            {{ "接收机" + row.receiverId.slice(-1) }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column property="avg" align="center" label="当前值" /> -->
      <el-table-column property="min" align="center" label="最小值" />
      <el-table-column property="max" align="center" label="最大值" />
      <el-table-column property="avg" align="center" label="平均值" />
      <el-table-column property="stdDev" align="center" label="标准差" />
      <el-table-column property="rms" align="center" label="RMS" />
      <el-table-column
        property="percentile95"
        align="center"
        label="95%分位数"
      />
      <el-table-column property="count" align="center" label="总数" />
    </el-table>
  </div>
</template>

<script setup>
import {
  reactive,
  ref,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  getCurrentInstance,
} from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
import apiAjax from "@/api/index";
const { proxy } = getCurrentInstance();
// 模拟数据
// function getRandomValue() {
//   return (Math.random() * 100).toFixed(1); // 生成0到100之间的随机数，并保留1位小数
// }

// let a = {
//   receivers: [
//     {
//       receiverId: "HAA1", //接收机
//       "6780M": "666", // 通道
//       "6780Y": "1",
//       "7430X": "2",
//       "8390M": "3",
//     },
//     {
//       receiverId: "HAA2",
//       "6780M": "777",
//       "6780Y": "12.3",
//       "7430X": "12.4",
//       "8390M": "12.5",
//     },
//     {
//       receiverId: "HAA3",
//       "6780M": "888",
//       "6780Y": "12.3",
//       "7430X": "12.4",
//       "6000M": "12.5",
//     },
//     {
//       receiverId: "HAA4",
//       "6780M": "999",
//       "6780Y": "11",
//       "7430X": "12.4",
//       "6000M": "12.5",
//     },
//   ],
//   receiverNum: "4",
//   dataTp: "2", // 1.1PPS  2.gtp
//   Date: new Date().toISOString(),
// };

const lineChartRef = ref(null);
const rawData = reactive({
  typeMapIndex: "1PPS时差",
  receiverchIndex: "", // 通道选择
  receiverchList: [],
  chartData: {
    title: {
      text: `单位：ns`,
      left: "left",
      top: "50",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "time",
      axisLabel: {
        interval: 0, // 显示所有标签
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false, // 隐藏 y 轴轴线
      },
      splitLine: {
        show: false, // 隐藏 y 轴网格线
      },
      min: 20.0,
      max: 20.2,
    },
    legend: {
      bottom: -4,
      itemHeight: 4,
      icon: "rect",
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: "#2B2E3F",
        lineHeight: 20,
        fontStyle: "normal",
      },
    },
    grid: {
      left: "60px",
      right: "30px",
      top: "100px",
      bottom: "50px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [
      {
        type: "line",
        showSymbol: false,
        data: [],
      },
    ],
  },
  mapType: {
    1: "1PPS时差",
    2: "GTP",
  },
  //这是所有数据处理的地方
  chartDataObj: {
    "1PPS时差": {},
    GTP: {},
  },
  tableData: [], // 现在显示的数据
  tableDataObj: [], //所有的表格数据
});
onMounted(async () => {
  await initScocket();
  getlodData();
});
const setDataFromScd = (data) => {
  let newData = JSON.parse(data);
  console.log("setDataFromScd", newData);
  newData.forEach((i) => {
    if (
      i.transmitter == rawData.receiverchIndex &&
      rawData.mapType[i.dataTp] == rawData.typeMapIndex
    ) {
      //  rawData.tableData 查找是否有接收机一样的值  如果没有就push 有的话就替换
      let index = rawData.tableData.findIndex(
        (item) => item.receiverId === i.receiverId,
      );
      if (index != -1) {
        rawData.tableData[index] = i;
      } else {
        rawData.tableData.push(i);
      }
    }
  });

  console.log("lauhsdoa", rawData.tableData);
};

onUnmounted(() => {
  unsubscribe("/topic/jsInfo/syrealtimedata/DTD-DTZ-SCD");
  unsubscribe("/topic/jsInfo/24hrealscd/24h-REAL-SCD");
});

const getlodData = async (obj) => {
  let { data } = await apiAjax.get(
    `/api/jnx/data/findHistory?startTime=${proxy.configure.getTimestampTwoMinutesAgo(
      3,
    )}&endTime=${proxy.configure.getUtcDate()}&dataType=scd&value=123&twSize=3600&page=1&pageNum=120`,
  );
  console.log(data);
  if (data?.length > 361) {
    data = data.slice(-360);
  }
  data?.forEach((i) => {
    let newData = i;
    const dateTime = proxy.configure.setDate(newData.Date); // 预计算时间戳
    const date = new Date(dateTime);
    let handleData = rawData.chartDataObj[rawData.mapType[newData.dataTp]]; // 找出我需要处理的对象
    transformData(newData, handleData, dateTime, date);
  });
  getChartAreaData();
  subscribe("/topic/jsInfo/syrealtimedata/DTD-DTZ-SCD", setRawData);
  subscribe("/topic/jsInfo/24hrealscd/24h-REAL-SCD", setDataFromScd);
};

const setRawData = (data) => {
  let newData = JSON.parse(data);

  const dateTime = proxy.configure.setDate(newData.Date); // 预计算时间戳
  const date = new Date(dateTime);
  let handleData = rawData.chartDataObj[rawData.mapType[newData.dataTp]]; // 找出我需要处理的对象
  transformData(newData, handleData, dateTime, date);
  // setTableDatas(newData);
  nextTick(() => {
    getChartAreaData();
  });
};
function transformData(obj, list, dateTime, date) {
  // 遍历 obj.receivers 数组中的每一个接收机
  obj.receivers.forEach((receiver) => {
    let newobj = { ...receiver };
    delete newobj.receiverId;
    Object.keys(newobj).forEach((i) => {
      let channelName = i;
      let currentValue = receiver[channelName].data;
      let receiverId = receiver.receiverId;
      // 如果 list 中没有这个通道的数组，先创建一个空数组
      if (!Array.isArray(list[channelName])) {
        list[channelName] = [];
      }
      let existing = list[channelName].find(
        (item) => item.title === receiverId,
      );
      if (existing) {
        // 如果已经存在，则在已有的值数组中添加新的值
        if (currentValue) {
          existing.data.push({
            name: date.toString(),
            value: [date, currentValue],
          });
        }
        if (existing.data.length > 360) {
          existing.data.shift();
        }
      } else {
        // 如果不存在，则创建一个新的记录，并添加到数组中
        list[channelName].push({
          data: [{ name: date.toString(), value: [date, currentValue] }],
          type: "line",
          title: receiverId,
          name: "接收机" + receiverId.slice(-1),
          symbol: "none", // 不显示数据点
        });
      }
    });
  });
}
// 切换的时候动态去做的事情
const selectFirst = (info = false) => {
  if (info) {
    return;
  }
  rawData.receiverchList = [];
  Object.keys(rawData.chartDataObj[rawData.typeMapIndex]).forEach((id) => {
    const optionExists = rawData.receiverchList.some((i) => i.value === id);
    if (!optionExists) {
      rawData.receiverchList.push({
        key: id,
        label: proxy.configure.thoroughfare(id),
        value: id,
      });
    }
    rawData.receiverchIndex = rawData.receiverchIndex || id;
  });
};

const getChartAreaData = (info = false) => {
  selectFirst();
  let datas =
    rawData.chartDataObj[rawData.typeMapIndex][rawData.receiverchIndex];
  if (proxy.configure.isEmpty(datas)) {
    return;
  }
  let list = datas
    .map((i) => {
      let item = i.data.map((o) => {
        return o.value[1] ? o.value[1] - 0 : null;
      });
      return item;
    })
    .flat();
  let max = Math.max(...list);
  let min = Math.min(...list);
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) * 0.1;
    min = min - (max - min) * 0.1;
  }

  if (lineChartRef.value) {
    if (list.every((item) => item == null) || list.length == 0) {
      lineChartRef.value.getIns().setOption(rawData.chartData, true);
      return;
    }
    lineChartRef.value.setInfos();
    lineChartRef.value.getIns().setOption({
      series: datas,
      grid: {
        left: (Math.ceil(max * 1000) / 1000 + "").length * 7 + 25 + "px",
      },
      yAxis: {
        max: Math.ceil(max * 1000) / 1000,
        min: Math.ceil(min * 1000) / 1000,
      },
    });
  }
};

watch(
  () => [rawData.typeMapIndex],
  () => {
    rawData.receiverchIndex = "";
    rawData.tableData = [];
  },
);
watch(
  () => [rawData.receiverchIndex],
  () => {
    rawData.tableData = [];
    lineChartRef.value.getIns().setOption(rawData.chartData, true);
    getChartAreaData(true);
  },
);
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex < 1) {
    if (rowIndex % 4 === 0) {
      return {
        rowspan: 4,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.top-con {
  background: #fff;
  border-radius: 2px;
  height: 440px;

  .tabContent {
    padding: 0 10px;
  }

  .chartArea {
    width: 100%;
    height: 400px;
  }

  .select {
    width: 162px;
    position: relative;
    top: 6px;
    left: 10px;
    z-index: 1;
    height: 0;
  }

  .item {
    margin-right: 16px;
  }

  .select-label {
    position: relative;
    width: 200px;
    top: 0px;
    left: 280px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    text-align: left;
    font-style: normal;
    z-index: 1;
    height: 0;
  }

  .hide-select-label {
    display: none;
  }
}

.con {
  :deep(.el-table) {
    --el-table-border-color: #e7e7e7;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }
}
</style>
