<template>
  <div class="con">
    <div class="card-con">
      <el-card class="card">
        <template #header>
          <div class="card-header">
            <span>基础参数</span>
          </div>
        </template>

        <el-form
          ref="form3Ref"
          :model="store.form3"
          label-width="70px"
          style="width: 100%"
          inline
        >
          <el-form-item label="开机启动" prop="autoRun">
            <el-checkbox v-model="store.form3.autoRun"></el-checkbox>
          </el-form-item>
          <el-form-item label="站点名称" prop="siteName">
            <n-select
              v-model:value="store.form3.siteName"
              filterable
              tag
              placeholder="请选择"
              class="input"
              :options="store.siteList.map(item => ({ label: item.name, value: item.value }))"
            />
          </el-form-item>
          <el-form-item label="屏保" prop="pwd">
            <el-checkbox v-model="store.form3.pwd"></el-checkbox>
          </el-form-item>
          <el-form-item
            v-if="store.form3.pwd"
            label="等待时长"
            prop="sreenLockWaitTime"
          >
            <n-select
              v-model:value="store.form3.sreenLockWaitTime"
              placeholder="请选择"
              class="input"
              :options="[
                { label: '1分钟', value: '1' },
                { label: '5分钟', value: '5' },
                { label: '10分钟', value: '10' },
                { label: '20分钟', value: '20' }
              ]"
            />
          </el-form-item>

          <el-form-item
            v-if="store.form3.pwd"
            label="屏保密码"
            prop="sreenLockPwd"
          >
            <el-input
              v-model="store.form3.sreenLockPwd"
              type="password"
              show-password
              class="input"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="card card3">
        <template #header>
          <div class="card-header">
            <span>数字记录仪通道配置</span>
          </div>
        </template>

        <div class="channel-grid">
          <div
            v-for="item in rowlandData.nevList"
            :key="item.id"
            class="channel-card"
          >
            <div class="channel-num">通道号: {{ item.table }}</div>
            <el-button
              type="primary"
              @click="openChannelDetail(item)"
              class="detail-button"
            >
              <el-icon class="el-icon--left"><View /></el-icon>配置详情
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 通道详情弹出框 -->
      <el-dialog
        v-model="channelDrawerVisible"
        title="通道详情配置"
        width="450px"
        :before-close="onCloseChannelDrawer"
      >
        <div v-if="currentChannel">
          <el-form :model="channelForm" label-width="100px">
            <el-form-item label="通道号">
              <span>{{ currentChannel.table }}</span>
            </el-form-item>
            <el-form-item label="接收机">
              <n-select
                v-model:value="channelForm.receiverId"
                placeholder="请选择接收机"
                @update:value="handleReceiverChange"
                :options="rowlandData.station.map(item => ({ label: item.table, value: item.value }))"
              />
            </el-form-item>
            <el-form-item label="通道">
              <n-select
                v-model:value="channelForm.receiverCh"
                placeholder="请选择通道"
                @update:value="handleChannelChange"
                :options="channelOptions.map(item => ({ label: item.receiverCh, value: item.receiverCh }))"
              />
            </el-form-item>
            <el-form-item label="发波台">
              <el-input v-model="channelForm.transmitter" disabled />
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="saveChannelConfig"
              >保存</el-button
            >
            <el-button @click="channelDrawerVisible = false">取消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <!-- <div class="bottom-bar">
      <el-button class="save-btn" type="primary" @click="onSaveClick"
        >保存</el-button
      >
    </div> -->

    <el-drawer
      class="detail"
      v-model="detailDrawer"
      title="通信参数详情"
      direction="rtl"
      :before-close="onDetailDrawerClose"
    >
      <el-table
        :data="store.detailTableData || []"
        border
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          v-for="(col, index) in store.detailTableColumn || []"
          :key="index"
          v-bind="col"
        />
      </el-table>

      <template #footer>
        <div class="footer">
          <el-button type="primary" @click="onConfirmClick">确认</el-button>
          <!-- <el-button @click="onCancelClick">取消</el-button> -->
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useParamsManage } from "@/store/modules/params-manage";
import { View } from "@element-plus/icons-vue";
import apiAxios from "@/api/index";
import { ElMessage } from "element-plus";
onMounted(async () => {
  let data = await apiAxios.post("/api/jnx/dataParam/paramSettingsSerachAll");
  rowlandData.value.newData = data;
});
const rowlandData = ref({
  nevList: [
    { id: 1, table: "1-2", value: "1-2" },
    { id: 2, table: "3-4", value: "3-4" },
    { id: 3, table: "5-6", value: "5-6" },
    { id: 4, table: "7-8", value: "7-8" },
    { id: 5, table: "9-10", value: "9-10" },
  ],
  newData: [],
  station: [
    { table: "HAA1", value: "HAA1" },
    { table: "HAA2", value: "HAA2" },
    { table: "HAA3", value: "HAA3" },
    { table: "HAA4", value: "HAA4" },
  ],
});

const getRowLan = async (value) => {
  try {
    let data = await apiAxios.post("/api/jnx/dataParam/paramSettingsSerach", {
      receiverId: value,
    });
    if (Array.isArray(data) && data.length > 0) {
      channelOptions.value = data;
      // 如果存在通道选项且当前没有选中通道，则自动选择第一个
      if (channelOptions.value.length && !channelForm.receiverCh) {
        channelForm.receiverCh = channelOptions.value[0].receiverCh;
        channelForm.transmitter = channelOptions.value[0].transmitter;
      }
    } else {
      channelOptions.value = [];
    }

    console.log("获取通道数据成功:", data);
  } catch (error) {
    console.error("获取通道数据失败:", error);
    channelOptions.value = [];
  }
};

const store = useParamsManage();

const form1Ref = ref(null);
const form2Ref = ref(null);
const form3Ref = ref(null);
const form4Ref = ref(null);

const detailDrawer = ref(false);

const channelDrawerVisible = ref(false);
const currentChannel = ref(null);
const channelForm = reactive({
  receiverId: null,
  receiverCh: null,
  transmitter: null,
});
const channelOptions = ref([]);

const handleFolderSelect = (event, propName) => {
  const files = event.target.files;
  if (files.length > 0) {
    let folderPath = files[0].webkitRelativePath;
    if (folderPath.indexOf(".") > -1) {
      folderPath = folderPath.split("/")[0];
      store.form2[propName] = folderPath;
    }
  }
};

const onDetailClick = () => {
  detailDrawer.value = true;
};

const onDetailDrawerClose = () => {
  detailDrawer.value = false;
};

const onConfirmClick = () => {
  detailDrawer.value = false;
};

// 打开通道详情
const openChannelDetail = (row) => {
  currentChannel.value = row;
  // 获取当前通道的配置数据
  const channelData = rowlandData.value.newData.find(
    (item) => item.channel === row.table,
  );
  // 设置表单数据
  channelForm.receiverId = channelData?.receiverId || null;
  channelForm.receiverCh = channelData?.receiverCh || null;
  channelForm.transmitter = channelData?.transmitter || null;

  // 如果有接收机ID，获取通道选项
  if (channelForm.receiverId) {
    handleReceiverChange(channelForm.receiverId);
  } else {
    channelOptions.value = [];
  }

  channelDrawerVisible.value = true;
};

// 处理接收机变更
const handleReceiverChange = async (value) => {
  if (!value) {
    channelOptions.value = [];
    channelForm.receiverCh = null;
    channelForm.transmitter = null;
    return;
  }

  try {
    await getRowLan(value);
  } catch (error) {
    console.error("获取通道数据失败", error);
    channelOptions.value = [];
  }
};

// 处理通道选择变更
const handleChannelChange = (value) => {
  if (!value) {
    channelForm.transmitter = null;
    return;
  }

  // 从通道选项中查找对应的发波台
  const selectedChannel = channelOptions.value.find(
    (item) => item.receiverCh === value,
  );
  if (selectedChannel) {
    channelForm.transmitter = selectedChannel.transmitter;
  }
};

// 保存通道配置
const saveChannelConfig = async () => {
  let data = await apiAxios.post("/api/jnx/dataParam/paramsSettings", [
    {
      receiverId: channelForm.receiverId,
      receiverCh: channelForm.receiverCh,
      transmitter: channelForm.transmitter,
      channel: currentChannel.value.table,
    },
  ]);
  if (data && data.code) {
    ElMessage.warning("保存失败");
  } else {
    ElMessage.success("保存成功");
  }
  channelDrawerVisible.value = false;
};

const onCloseChannelDrawer = () => {
  channelDrawerVisible.value = false;
};
</script>

<style lang="scss" scoped>
.con {
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  :deep(.el-button) {
    border-radius: 3px;
  }

  :deep(.el-card__header) {
    padding: 15px 16px;
  }

  :deep(.el-card__body) {
    padding-top: 24px;
    padding-bottom: 6px;
  }

  .card-con {
    width: 100%;

    :deep(.el-card.is-always-shadow) {
      box-shadow: none;
    }

    .card {
      margin-bottom: 16px;
    }

    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }

  .input {
    width: 326px;
  }
  .inpust {
    width: 1200px;
  }

  .last-btn {
    margin-left: 16px;
  }

  .file-upload {
    display: none;
  }

  .bottom-bar {
    width: 100%;
    height: 74px;
    background: #ffffff;
    border-radius: 2px;

    display: flex;
    justify-content: center;
    align-items: center;

    .save-btn {
      height: 40px;
      border-radius: 3px;
    }
  }

  :deep(.detail) {
    .el-drawer__title {
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .el-drawer__header {
      margin: 0;
      padding: 16px;
      border-bottom: 1px solid #e7e7e7;
    }

    .el-drawer__body {
      padding: 24px;
    }

    .el-drawer__footer {
      padding: 0;
    }

    .footer {
      width: 100%;
      height: 56px;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      border-top: 1px solid #e7e7e7;

      .el-button + .el-button {
        margin-left: 8px;
      }
    }
  }

  :deep(.el-table) {
    --el-table-border-color: #e8e8e8;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__cell {
      border-right: none;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }
}
.card3 {
  .input {
    width: 350px;
  }
  .inpust {
    width: 1200px;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
  border-top: 1px solid #e6e6e6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;

  .el-button + .el-button {
    margin-left: 12px;
  }
}

.channel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  padding: 8px;
}

.channel-card {
  background-color: #f9fafc;
  border: 1px solid #e6e9f0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 120px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-color: #c0c4cc;
  }

  .channel-num {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 16px;
  }

  .detail-button {
    width: 100%;
    margin-top: auto;
    padding: 8px 16px;
    font-weight: normal;
    transition: all 0.3s;
    border-radius: 4px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
