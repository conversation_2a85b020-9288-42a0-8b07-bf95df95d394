import configure from '@/utils/configure.js';
import { ref } from 'vue'
import { ElMessage } from "element-plus";
export const ApiObj = {
  // 增强型罗兰接收机 原始数据
  rolandAOne: {
    url: {
      chart: "api/jnx/dataManager/getEloranRealtimeOrigin?",
      tlabt: "api/jnx/dataManager/getEloranFormDataOrigin?",
    },
    from: {
      channel: "channel",
      dataType: "type",
      endTime: "", //结束时间
      receiveIds: "devName",
      screenWidth: "1920",
      startTime: "",
      transmitterId: "transmitterId",
    },
    chria: {},
    // 请求的数据记录
    objFrom: {},
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.rolandAOne.chria = {};
        ApiObj.rolandAOne.objFrom = {};
        ApiObj.rolandAOne.chria = dataChria;
        childDom[0].initChart(ApiObj.rolandAOne.chria, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.rolandAOne.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.rolandAOne.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      childDom[0].initChart(dataChria, tlabtData);
    },
    //点击比对
    setComparison: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.rolandAOne.chria = { ...ApiObj.rolandAOne.chria, ...dataChria };
        childDom[0].setSeres(ApiObj.rolandAOne.chria, tlabtData);
        Object.keys(fromObj).forEach((i) => {
          ApiObj.rolandAOne.objFrom[i].push(fromObj[i]);
        });
      } else {
        ElMessage.error("未查询到数据");
      }
    },
    //设置from
    setForm: (from, loopType) => {
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1727320180";
      // from.startTime = "1727320168";
      if (from.channel == "all" && from.transmitterId == "all") {
        ElMessage.error("通道和发播台不能同时选择全部");
        return false;
      }

      if (loopType == "setComparison") {
        // 如果是比对的话 先判断时间是否相等
        // 通道号或发播台选中过全部，不能进行比对
        const infoAll =
          ApiObj.rolandAOne.objFrom.channel.includes("all") ||
          ApiObj.rolandAOne.objFrom.transmitterId.includes("all") ||
          from.channel == "all" ||
          from.transmitterId == "all";
        if (infoAll) {
          ElMessage.error("通道号或发播台选过全部，不能进行比对");
          return false;
        }

        const isTimeEqual =
          from.startTime == ApiObj.rolandAOne.objFrom.startTime[0] &&
          from.endTime == ApiObj.rolandAOne.objFrom.endTime[0];
        const isDataTypeEqual =
          from.dataType === ApiObj.rolandAOne.objFrom.dataType[0];
        const isParameterEqual =
          ApiObj.rolandAOne.objFrom.dataType.includes(from.dataType) &&
          ApiObj.rolandAOne.objFrom.channel.includes(from.channel) &&
          ApiObj.rolandAOne.objFrom.receiveIds.includes(from.receiveIds) &&
          ApiObj.rolandAOne.objFrom.transmitterId.includes(from.transmitterId);

        if (!isTimeEqual) {
          ElMessage.error("比对时间必须相等");
          return false;
        }

        if (!isDataTypeEqual) {
          ElMessage.error("比对类型必须相同");
          return false;
        }

        if (isParameterEqual) {
          ElMessage.error("参数重复");
          return false;
        }
      }
      if (loopType == "onSearchEchart") {
        let fromObjData = {};
        Object.keys(ApiObj.rolandAOne.objFrom).forEach((k) => {
          fromObjData[k] = ApiObj.rolandAOne.objFrom[k].join("&" + k + "=");
        });
        fromObjData.startTime = from.startTime;
        fromObjData.endTime = from.endTime;
        fromObjData.screenWidth = 1920;
        fromObjData.dataType = ApiObj.rolandAOne.objFrom.dataType[0];
        return setPromsUrl(fromObjData);
      }
      return setPromsUrl(from);
    },
  },
  // 增强型罗兰接收机 分析数据
  rolandATwo: {
    url: {
      chart: "api/jnx/dataManager/getEloranRealtimeAnalysis?",
      tlabt: "api/jnx/dataManager/getEloranFormDataAnalysis?",
    },
    from: {
      dataType: "type",
      endTime: "", //结束时间
      screenWidth: "1920",
      startTime: "",
      transmitterId: "transmitterId",
    },
    chria: {},
    // 请求的数据记录
    objFrom: {},
    //初始化 表格
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.rolandATwo.chria = {};
        ApiObj.rolandATwo.objFrom = {};
        ApiObj.rolandATwo.chria = dataChria;
        childDom[0].initChart(ApiObj.rolandATwo.chria, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.rolandATwo.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.rolandATwo.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      childDom[0].initChart(dataChria, tlabtData);
    },
    //点击比对
    setComparison: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.rolandATwo.chria = { ...ApiObj.rolandATwo.chria, ...dataChria };
        childDom[0].setSeres(ApiObj.rolandATwo.chria, tlabtData);
        Object.keys(fromObj).forEach((i) => {
          ApiObj.rolandATwo.objFrom[i].push(fromObj[i]);
        });
      } else {
        ElMessage.error("未查询到数据");
      }
    },
    //设置from
    setForm: (from, loopType) => {
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1727517416";
      // from.startTime = "1727517406";
      if (loopType == "setComparison") {
        // 如果是比对的话 先判断时间是否相等
        // 通道号或发播台选中过全部，不能进行比对
        const infoAll =
          ApiObj.rolandATwo.objFrom.transmitterId.includes("all") ||
          from.transmitterId == "all";
        if (infoAll) {
          ElMessage.error("发播台选过全部，不能进行比对");
          return false;
        }
        const isTimeEqual =
          from.startTime == ApiObj.rolandATwo.objFrom.startTime[0] &&
          from.endTime == ApiObj.rolandATwo.objFrom.endTime[0];
        const isDataTypeEqual =
          from.dataType === ApiObj.rolandATwo.objFrom.dataType[0];
        const isParameterEqual =
          ApiObj.rolandATwo.objFrom.dataType.includes(from.dataType) &&
          ApiObj.rolandATwo.objFrom.transmitterId.includes(from.transmitterId);

        if (!isTimeEqual) {
          ElMessage.error("比对时间必须相等");
          return false;
        }

        if (!isDataTypeEqual) {
          ElMessage.error("比对类型必须相同");
          return false;
        }

        if (isParameterEqual) {
          ElMessage.error("参数重复");
          return false;
        }
      }
      if (loopType == "onSearchEchart") {
        let fromObjData = {};
        Object.keys(ApiObj.rolandATwo.objFrom).forEach((k) => {
          fromObjData[k] = ApiObj.rolandATwo.objFrom[k].join("&" + k + "=");
        });
        fromObjData.startTime = from.startTime;
        fromObjData.endTime = from.endTime;
        fromObjData.screenWidth = 1920;
        fromObjData.dataType = ApiObj.rolandATwo.objFrom.dataType[0];
        return setPromsUrl(fromObjData);
      }
      return setPromsUrl(from);
    },
  },
  // 多通道原始数据
  channelOne: {
    url: {
      chart: "api/jnx/dataManager/getDtdRealtimeOrigin?",
      tlabt: "api/jnx/dataManager/getDtdFormDataOrigin?",
    },
    from: {
      channel: "channel",
      dataTp: "type",
      dtdEquipment: "devName",
      endTime: "", //结束时间
      screenWidth: "1920",
      startTime: "",
      transmitterId: "transmitterId",
    },
    chria: {},
    // 请求的数据记录
    objFrom: {},
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.channelOne.chria = {};
        ApiObj.channelOne.objFrom = {};
        ApiObj.channelOne.chria = dataChria;
        childDom[1].initChart(ApiObj.channelOne.chria, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.channelOne.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.channelOne.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      childDom[1].initChart(dataChria, tlabtData);
    },
    //点击比对
    setComparison: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.channelOne.chria = { ...ApiObj.channelOne.chria, ...dataChria };
        childDom[0].setSeres(ApiObj.channelOne.chria, tlabtData);
        Object.keys(fromObj).forEach((i) => {
          ApiObj.channelOne.objFrom[i].push(fromObj[i]);
        });
      } else {
        ElMessage.error("未查询到数据");
      }
    },
    //设置from
    setForm: (from, loopType) => {
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1727580284";
      // from.startTime = "1727580277";
      if (from.channel == "all" && from.transmitterId == "all") {
        ElMessage.error("通道和发播台不能同时选择全部");
        return false;
      }

      if (loopType == "setComparison") {
        // 如果是比对的话 先判断时间是否相等

        // 通道号或发播台选中过全部，不能进行比对
        const infoAll =
          ApiObj.channelOne.objFrom.channel.includes("all") ||
          ApiObj.channelOne.objFrom.transmitterId.includes("all") ||
          from.channel == "all" ||
          from.transmitterId == "all";
        if (infoAll) {
          ElMessage.error("通道号或发播台选过全部，不能进行比对");
          return false;
        }

        const isTimeEqual =
          from.startTime == ApiObj.channelOne.objFrom.startTime[0] &&
          from.endTime == ApiObj.channelOne.objFrom.endTime[0];
        const isDataTypeEqual =
          from.dataTp === ApiObj.channelOne.objFrom.dataTp[0];
        const isParameterEqual =
          ApiObj.channelOne.objFrom.dataTp.includes(from.dataTp) &&
          ApiObj.channelOne.objFrom.channel.includes(from.channel) &&
          ApiObj.channelOne.objFrom.dtdEquipment.includes(from.dtdEquipment) &&
          ApiObj.channelOne.objFrom.transmitterId.includes(from.transmitterId);

        if (!isTimeEqual) {
          ElMessage.error("比对时间必须相等");
          return false;
        }

        if (!isDataTypeEqual) {
          ElMessage.error("比对类型必须相同");
          return false;
        }

        if (isParameterEqual) {
          ElMessage.error("参数重复");
          return false;
        }
      }
      if (loopType == "onSearchEchart") {
        let fromObjData = {};
        Object.keys(ApiObj.channelOne.objFrom).forEach((k) => {
          fromObjData[k] = ApiObj.channelOne.objFrom[k].join("&" + k + "=");
        });
        fromObjData.startTime = from.startTime;
        fromObjData.endTime = from.endTime;
        fromObjData.screenWidth = 1920;
        fromObjData.dataTp = ApiObj.channelOne.objFrom.dataTp[0];
        return setPromsUrl(fromObjData);
      }
      return setPromsUrl(from);
    },
  },
  // 多通道 分析数据
  channelTwo: {
    url: {
      chart: "api/jnx/dataManager/getDtdRealtimeAnalysis?",
      tlabt: "api/jnx/dataManager/getDtdFormDataAnalysis?",
    },
    from: {
      dataType: "type",
      endTime: "", //结束时间
      screenWidth: "1920",
      startTime: "",
      transmitterId: "transmitterId",
    },
    chria: {},
    // 请求的数据记录
    objFrom: {},
    //初始化 表格
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.channelTwo.chria = {};
        ApiObj.channelTwo.objFrom = {};
        ApiObj.channelTwo.chria = dataChria;
        childDom[1].initChart(ApiObj.channelTwo.chria, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.channelTwo.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.channelTwo.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      childDom[1].initChart(dataChria, tlabtData);
    },
    //点击比对
    setComparison: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        ApiObj.channelTwo.chria = { ...ApiObj.channelTwo.chria, ...dataChria };
        childDom[1].setSeres(ApiObj.channelTwo.chria, tlabtData);
        Object.keys(fromObj).forEach((i) => {
          ApiObj.channelTwo.objFrom[i].push(fromObj[i]);
        });
      } else {
        ElMessage.error("未查询到数据");
      }
    },
    //设置from
    setForm: (from, loopType) => {
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1727576150";
      // from.startTime = "1727576142";
      //设置比对的from 值
      if (loopType == "setComparison") {
        // 如果是比对的话 先判断时间是否相等
        // 通道号或发播台选中过全部，不能进行比对
        const infoAll =
          ApiObj.channelTwo.objFrom.transmitterId.includes("all") ||
          from.transmitterId == "all";
        if (infoAll) {
          ElMessage.error("发播台选过全部，不能进行比对");
          return false;
        }
        const isTimeEqual =
          from.startTime == ApiObj.channelTwo.objFrom.startTime[0] &&
          from.endTime == ApiObj.channelTwo.objFrom.endTime[0];
        const isDataTypeEqual =
          from.dataType === ApiObj.channelTwo.objFrom.dataType[0];
        const isParameterEqual =
          ApiObj.channelTwo.objFrom.dataType.includes(from.dataType) &&
          ApiObj.channelTwo.objFrom.transmitterId.includes(from.transmitterId);

        if (!isTimeEqual) {
          ElMessage.error("比对时间必须相等");
          return false;
        }

        if (!isDataTypeEqual) {
          ElMessage.error("比对类型必须相同");
          return false;
        }

        if (isParameterEqual) {
          ElMessage.error("参数重复");
          return false;
        }
      }
      // 再次搜索
      if (loopType == "onSearchEchart") {
        let fromObjData = {};
        Object.keys(ApiObj.channelTwo.objFrom).forEach((k) => {
          fromObjData[k] = ApiObj.channelTwo.objFrom[k].join("&" + k + "=");
        });
        fromObjData.startTime = from.startTime;
        fromObjData.endTime = from.endTime;
        fromObjData.screenWidth = 1920;
        fromObjData.dataType = ApiObj.channelTwo.objFrom.dataType[0];
        return setPromsUrl(fromObjData);
      }
      return setPromsUrl(from);
    },
  },
  // 时间综合测量仪
  timeSynthesis: {
    url: {
      chart: "api/jnx/dataManager/getTmcRealtime/",
      tlabt: 'api/jnx/dataManager/getTmcFormData/',
    },
    from: {
      endTime: "", //结束时间
      screenWidth: "1920",
      startTime: "",
    },
    objFrom: {},
    //初始化 表格
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      if (Object.keys(dataChria).length > 0) {
        let newData = { "NTP时差": dataChria.data }
        ApiObj.timeSynthesis.objFrom = {};
        childDom[2].initChart(newData, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.timeSynthesis.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.timeSynthesis.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      let newData = { "时间综合测量仪": dataChria.data }
      childDom[2].initChart(newData, tlabtData);
    },

    //设置from
    setForm: (from, loopType) => {
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1727264946";
      // from.startTime = "1727264930";
      let proms = from.startTime + '/' + from.endTime + '/' + 1920
      return proms
    },
  },
  // 数字记录仪
  digitalRecorder: {
    url: {
      chart: "api/jnx/dataManager/getMoudobusRealtime/",
      tlabt: false,
    },
    from: {
      channel: "channel",
      endTime: "", //结束时间
      startTime: "",
    },
    chria: {},
    // 请求的数据记录
    objFrom: {},
    //初始化 表格
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      const chriaData = infoDigital(dataChria)
      if (chriaData) {
        ApiObj.digitalRecorder.chria = {};
        ApiObj.digitalRecorder.objFrom = {};
        ApiObj.digitalRecorder.chria = chriaData;
        childDom[3].initChart(ApiObj.digitalRecorder.chria, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.digitalRecorder.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.digitalRecorder.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      const chriaData = infoDigital(dataChria)
      childDom[3].initChart(chriaData, tlabtData);
    },
    //点击比对
    setComparison: (dataChria, tlabtData, fromObj, childDom) => {
      const chriaData = infoDigital(dataChria)
      if (chriaData) {
        ApiObj.digitalRecorder.chria = { ...ApiObj.digitalRecorder.chria, ...chriaData };
        childDom[3].setSeres(ApiObj.digitalRecorder.chria, tlabtData);
        Object.keys(fromObj).forEach((i) => {
          ApiObj.digitalRecorder.objFrom[i].push(fromObj[i]);
        });
      } else {
        ElMessage.error("未查询到数据");
      }
    },
    //设置from
    setForm: (from, loopType) => {
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1757570658";
      // from.startTime = "1707570630";
      let proms = from.startTime + '/' + from.endTime + '/' + from.channel
      //设置比对的from 值
      if (loopType == "setComparison") {
        const isTimeEqual =
          from.startTime == ApiObj.digitalRecorder.objFrom.startTime[0] &&
          from.endTime == ApiObj.digitalRecorder.objFrom.endTime[0];
        const isParameterEqual =
          ApiObj.digitalRecorder.objFrom.channel.includes(from.channel)
        if (!isTimeEqual) {
          ElMessage.error("比对时间必须相等");
          return false;
        }
        if (isParameterEqual) {
          ElMessage.error("参数重复");
          return false;
        }
      }
      // 再次搜索
      if (loopType == "onSearchEchart") {
        proms = from.startTime + '/' + from.endTime + '/' + ApiObj.digitalRecorder.objFrom.channel.join()
      }
      return proms
    },
  },
  // 频率比对测量系统
  frequencyComparison: {
    url: {
      chart: "api/jnx/dataManager/getFcRealtime/",
      tlabt: 'api/jnx/dataManager/getFcFormData/',
      tlabt_two: 'api/jnx/dataManager/getFcSecondsData/',
    },
    from: {
      statns: "transmitterId",
      endTime: "", //结束时间
      startTime: "",
    },
    chria: {},
    // 请求的数据记录
    objFrom: {},
    //初始化 表格
    //初始化 图表
    initChart: (dataChria, tlabtData, fromObj, childDom) => {
      const chriaData = infoFrequencyData(dataChria)
      if (chriaData) {
        ApiObj.frequencyComparison.chria = {};
        ApiObj.frequencyComparison.objFrom = {};
        ApiObj.frequencyComparison.chria = chriaData;
        childDom[4].initChart(ApiObj.frequencyComparison.chria, tlabtData);
        // 记录下来请求的数据
        Object.keys(fromObj).forEach((i) => {
          ApiObj.frequencyComparison.objFrom[i] = [fromObj[i]];
        });
      } else {
        if (Object.keys(ApiObj.frequencyComparison.objFrom).length > 0) {
          ElMessage.error("未查询到数据");
        }
      }
    },
    //点击再次搜索
    onSearchEchart: (dataChria, tlabtData, fromObj, childDom) => {
      const chriaData = infoFrequencyData(dataChria)
      childDom[4].initChart(chriaData, tlabtData);
    },
    //点击比对
    setComparison: (dataChria, tlabtData, fromObj, childDom) => {
      const chriaData = infoFrequencyData(dataChria)
      if (chriaData) {
        ApiObj.frequencyComparison.chria = { ...ApiObj.frequencyComparison.chria, ...chriaData };
        childDom[4].setSeres(ApiObj.frequencyComparison.chria, tlabtData);
        Object.keys(fromObj).forEach((i) => {
          ApiObj.frequencyComparison.objFrom[i].push(fromObj[i]);
        });
      } else {
        ElMessage.error("未查询到数据");
      }
    },
    //设置from
    setForm: (from, loopType) => {
      console.log("from", from)
      // 现在还不能用自己的时间  查不到数据  先用一个默认时间
      // from.endTime = "1757595018";
      // from.startTime = "1707595018";
      let proms = from.startTime + '/' + from.endTime + '/' + from.statns
      //设置比对的from 值
      if (loopType == "setComparison") {
        const isTimeEqual =
          from.startTime == ApiObj.frequencyComparison.objFrom.startTime[0] &&
          from.endTime == ApiObj.frequencyComparison.objFrom.endTime[0];
        const isParameterEqual =
          ApiObj.frequencyComparison.objFrom.statns.includes(from.statns)
        if (!isTimeEqual) {
          ElMessage.error("比对时间必须相等");
          return false;
        }
        if (isParameterEqual) {
          ElMessage.error("参数重复");
          return false;
        }
      }
      // 再次搜索
      if (loopType == "onSearchEchart") {
        proms = from.startTime + '/' + from.endTime + '/' + ApiObj.frequencyComparison.objFrom.statns.join()
      }
      return proms
    },
  },
};
const transmitterIdList = Object.keys(
  configure.typeObj
).map((key) => {
  return {
    transmitterId: key,
    transmitterName: configure.typeObj[key],
  };
})

function setPromsUrl(fromObj) {
  let proms = ''
  Object.keys(fromObj).forEach((key) => {
    if (fromObj[key]) {
      proms += `${key}=${fromObj[key]}&`;
    }
  });
  proms = proms.slice(0, -1);
  return proms
}
//设置通道
const setThoroughfareList = (lent, infoAll = true) => {
  let list = Array.from({ length: lent }, (_, i) => i + 1).map((i) => {
    return { name: '通道' + i, value: i.toString() }
  })
  if (infoAll) {
    list.unshift({ name: "全部", value: "all" })
  }
  return list
}

export const tabList = ref([
  {
    name: "tab1",
    label: "增强型罗兰接收机",
    hasChildTab: true,
    filterConfig: [
      {
        isSetComparison: true, // 是否显示 比对
        isdataZoom: true,
        isShowDevList: true,
        isDevMulti: false,
        filterConfigName: "rolandAOne",
        devList: {
          data: [{ receiverId: "HAA1", name: "接收机一" }],
          type: {
            key: "receiverId",
            value: "receiverId",
            label: "name",
          },
          default: "HAA2",
          tltle: "接收机",
        },
        isShowChannelList: true,
        channelList: {
          data: [...transmitterIdList],
          type: {
            key: "transmitterId",
            value: "transmitterId",
            label: "transmitterName",
          },
          default: "6000M",
          tltle: "发播台",
        },
        isShowTypeList: true,
        typeList: {
          data: [
            { name: "场强", value: "fieldstrength" },
            { name: "信噪比", value: "snr" },
            { name: "包周差", value: "packagetd" },
            { name: "频率偏差", value: "frequencytd" },
          ],
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "snr",
          tltle: "类型",
        },

        isThoroughfare: true, // 设置通道
        thoroughfareList: {
          data: setThoroughfareList(4),
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "1",
          tltle: "通道号",
        },

        istableColumn: true, // 设置图的类型
        tableColumn: [
          { prop: "receiverId", label: "接收机" },
          { prop: "min", label: "最小值" },
          { prop: "max", label: "最大值" },
          { prop: "avg", label: "均值" },
          { prop: "percentile95", label: "95%分位数" },
          { prop: "channel", label: "通道号" },
          { prop: "rms", label: "RMS" },
          { prop: "transmitter", label: "通道" },
          { prop: "stdev", label: "标准差" },
          { prop: "count", label: "总数" },
        ],
      },
      {
        isSetComparison: true, // 是否显示 比对
        isdataZoom: true,
        isShowDevList: false,
        isShowChannelList: true,
        filterConfigName: "rolandATwo",
        channelList: {
          data: [...transmitterIdList],
          type: {
            key: "transmitterId",
            value: "transmitterId",
            label: "transmitterName",
          },
          default: "6000M",
          tltle: "发播台",
        },
        isShowTypeList: true,
        typeList: {
          data: [
            { name: "场强", value: "fieldstrength" },
            { name: "信噪比", value: "snr" },
            { name: "包周差", value: "packagetd" },
            { name: "频率偏差", value: "frequencytd" },
          ],
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "snr",
          tltle: "类型",
        },
        isThoroughfare: false, // 设置通道
        thoroughfareList: {},
        istableColumn: true,
        tableColumn: [
          { prop: "transmitter", label: "发播台" },
          { prop: "min", label: "最小值" },
          { prop: "max", label: "最大值" },
          { prop: "avg", label: "均值" },
          { prop: "percentile95", label: "95%分位数" },
          { prop: "rms", label: "RMS" },
          { prop: "stdev", label: "标准差" },
          { prop: "count", label: "总数" },
        ],
      },
    ],
  },
  // 原始数据有4个下拉框  设备id 发播台 通道  类型 、发播台 通道可全选  前端写死  通道12路
  // 分析数据 有俩  设备id 类型
  {
    name: "tab2",
    label: "多通道时间间隔计数器",
    hasChildTab: true,
    filterConfig: [
      {
        isSetComparison: true, // 是否显示 比对
        isdataZoom: true,
        isShowDevList: true,
        isDevMulti: false,
        filterConfigName: "channelOne",
        devList: {
          data: [{
            "deviceId": "HAC1",
            "deviceName": "HAC1",
          }],
          type: {
            key: "deviceId",
            value: "deviceId",
            label: "deviceName",
          },
          default: "HAC1",
          tltle: "设备ID",
        },
        isShowChannelList: true,
        channelList: {
          data: [...transmitterIdList],
          type: {
            key: "transmitterId",
            value: "transmitterId",
            label: "transmitterName",
          },
          default: "8390Y",
          tltle: "发播台",
        },
        isShowTypeList: true,
        typeList: {
          data: [
            { name: "1PPS原始时差数据", value: "1" },
            { name: "GTP", value: "2" },
          ],
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "1",
          tltle: "类型",
        },

        isThoroughfare: true, // 设置通道
        thoroughfareList: {
          data: setThoroughfareList(16),
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "4",
          tltle: "通道号",
        },

        istableColumn: true, // 设置图的类型
        tableColumn: [
          { prop: "dtdEquipment", label: "设备ID" },
          { prop: "dataTp", label: "数据类型" },
          { prop: "transmitterId", label: "发播台" },
          { prop: "min", label: "最小值" },
          { prop: "max", label: "最大值" },
          { prop: "avg", label: "均值" },
          { prop: "percentile95", label: "95%分位数" },
          { prop: "rms", label: "RMS" },
          { prop: "stdev", label: "标准差" },
          { prop: "count", label: "总数" },
        ],
      },
      {
        isSetComparison: true, // 是否显示 比对
        isdataZoom: true,
        isDevMulti: false,
        filterConfigName: "channelTwo",
        isShowDevList: false,
        devList: {
          data: [],
          type: {
            key: "deviceId",
            value: "deviceId",
            label: "deviceName",
          },
          default: "",
          tltle: "",
        },
        isShowChannelList: true,
        channelList: {
          data: [...transmitterIdList],
          type: {
            key: "transmitterId",
            value: "transmitterId",
            label: "transmitterName",
          },
          default: "6000M",
          tltle: "发播台",
        },
        isShowTypeList: true,
        typeList: {
          data: [
            { name: "授时偏差", value: "tser" },
            { name: "TOA", value: "toa" },
            { name: "TOC", value: "toc" },
          ],
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "tser",
          tltle: "类型",
        },

        isThoroughfare: false, // 设置通道
        thoroughfareList: {
          data: [],
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "",
          tltle: "",
        },

        istableColumn: true, // 设置图的类型
        tableColumn: [
          { prop: "statn", label: "发播台" },
          { prop: "dataType", label: "类型" },
          { prop: "min", label: "最小值" },
          { prop: "max", label: "最大值" },
          { prop: "avg", label: "均值" },
          { prop: "percentile95", label: "95%分位数" },
          { prop: "rms", label: "RMS" },
          { prop: "stdev", label: "标准差" },
          { prop: "count", label: "总数" },
        ],
      },
    ],
  },
  {
    name: "tab3",
    label: "时间综合测量仪",
    hasChildTab: false,
    filterConfig: [
      {
        isdataZoom: true,
        isShowDevList: false,
        isShowChannelList: false,
        isShowTypeList: false,
        lineName: "NTP时差",
        filterConfigName: "timeSynthesis",
        istableColumn: true, // 设置图的类型
        tableColumn: [
          { prop: "startTime", label: "开始时间" },
          { prop: "endTime", label: "结束时间" },
          { prop: "ntpTiAvg", label: "平均偏差" },
          { prop: "ntpSenCur", label: "秒偏差" },
          { prop: "refenStatus", label: "refenStatus" },
          { prop: "data", label: "data" },
          { prop: "ntpRou", label: "ntpRou" },
          { prop: "ntpToStd", label: "ntpToStd" },
          { prop: "ntpToMax", label: "最大值" },
          { prop: "ntpSenStatus", label: "ntpSenStatus" },
          { prop: "ntpToMin", label: "ntpToMin" },
          { prop: "ntpSenOfsetCount", label: "ntpSenOfsetCount" },
        ],
      },

    ],
  },
  {
    name: "tab4",
    label: "数字记录仪",
    hasChildTab: false,
    filterConfig: [
      {
        isSetComparison: true, // 是否显示 比对
        isThoroughfare: true, // 设置通道
        thoroughfareList: {
          data: setThoroughfareList(10, false),
          type: {
            key: "value",
            value: "value",
            label: "name",
          },
          default: "1",
          tltle: "通道号",
        },
        isdataZoom: true,
        isShowDevList: false,
        isShowChannelList: false,
        isShowTypeList: false,
        filterConfigName: "digitalRecorder",
        istableColumn: false,
        tableColumn: [],
      },
    ],
  },
  {
    name: "tab5",
    label: "频率比对测量系统",
    hasChildTab: false,
    filterConfig: [
      {
        isSetComparison: true, // 是否显示 比对
        isShowChannelList: true,
        isdataZoom: true,
        channelList: {
          data: [...transmitterIdList],
          type: {
            key: "transmitterId",
            value: "transmitterId",
            label: "transmitterName",
          },
          default: "8750M",
          tltle: "台站号",
        },
        isShowDevList: false,
        isShowTypeList: false,
        filterConfigName: "frequencyComparison",
        istableColumn: true,
        tableColumn: [
          { prop: "station", label: "台站号" },
          { prop: "min", label: "最小值" },
          { prop: "max", label: "最大值" },
          { prop: "avg", label: "均值" },
          { prop: "percentile95", label: "95%分位数" },
          { prop: "rms", label: "RMS" },
          { prop: "stdev", label: "标准差" },
          { prop: "count", label: "总数" },
        ],
        istableColumnTow: true,// 第二个表格
        tableNameColumn: [
          { prop: "station", label: "台站号" },
          { prop: "alnV1s", label: "1S阿伦方差" },
          { prop: "alnV10s", label: "10S阿伦方差" },
          { prop: "alnV100s", label: "100S阿伦方差" },
          { prop: "alnV1000s", label: "1000S阿伦方差" },
          { prop: "alnV10000s", label: "10000S阿伦方差" },
          { prop: "alnV186400s", label: "86400S阿伦方差" },
        ]
      },
    ],
  },
  {
    name: "tab6",
    label: "频谱干扰测试系统",
  },
  {
    name: "tab7",
    label: "自动气象站",
  },
]);
// 修改表格数据
export const setTabData = (type, tableData) => {
  const obj = {
    channelOne: (tableData) => {
      let dataTpObj = {
        "1": "1PPS原始时差",
        "2": "GTP",
      }
      tableData.forEach(item => {
        item.dataTp = dataTpObj[item.dataTp];
      })
      return tableData
    },
    channelTwo: (tableData) => {
      let dataTypeObj = {
        'tser': '授时偏差',
        'toa': 'TOA',
        'toc': 'TOC',
      }
      tableData.forEach(item => {
        item.dataType = dataTypeObj[item.dataType];
      })
      return tableData
    }
  }
  return obj[type] ? (obj[type](tableData)) : tableData;
}

// 特殊处理请求的url
export const setUrl = (newTypes, url) => {
  const obj = {
    timeSynthesistlabt: (url) => {
      return url.slice(0, -5)
      // return '/1731131287/1731132088'
    }
  }
  return obj[newTypes] ? (obj[newTypes](url)) : url;
}

// 判断是不没有数据
const infoFrequencyData = (data) => {
  const obj = {}
  const type = {
    fieldStrength: "场强",
    compFrequencyTD: "频率偏差"
  }
  Object.keys(data).forEach((i) => {
    if (Object.keys(data[i][0]).length !== 0) {
      let key = i.split("-")[0] + '-' + type[i.split("-")[1]]
      obj[key] = data[i]
    }
  })
  return Object.keys(obj).length !== 0 ? obj : false
}

const infoDigital = (data) => {
  const obj = {}
  Object.keys(data).forEach((i) => {
    if (Object.keys(data[i][0]).length !== 0) {
      let key = '通道' + '-' + i.slice(-1)
      obj[key] = data[i]
    }
  })
  return Object.keys(obj).length !== 0 ? obj : false
}

// 设置表格
export const setfrequencyTabe = (data) => {
  let tableData = [];
  data.forEach((item) => {
    tableData.push({ station: Object.keys(item)[0], ...item[Object.keys(item)[0]][0] })
  })
  return tableData
}
