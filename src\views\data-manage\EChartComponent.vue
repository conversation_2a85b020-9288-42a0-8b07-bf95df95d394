<template>
<div>
  <div class="bodys" v-show="infos">
    <div ref="chart" id="chartArea"></div>
    <img
      :src="search"
      alt="搜索"
      class="search"
      @click="emit('onSearchEchart', { startTime, endTime })"
    />
  </div>
  <div class="bodys loading-container" v-show="!infos">
    <div class="loading-wrapper">
      <div class="loading-spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
      <div class="loading-text">数据加载中...</div>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import * as echarts from "echarts";
import search from "@/assets/imgs/search.png";
const chart = ref(null);
let myChart = null;
const emit = defineEmits(["onSearchEchart"]);
const infos = ref(false);
const props = defineProps({
  isdataZoom: {
    type: Boolean,
    required: false,
    default: false,
  },
});
let startTime,
  endTime = "";
// 准备 x 轴数据（时间）和 y 轴数据（接收到的数据）
const prepareSeriesData = (data) => {
  return Object.keys(data).map((receiverName) => {
    const receiverData = Object.entries(data[receiverName][0]).map(
      ([time, value]) => [parseInt(time, 10) * 1000, value],
    );
    return {
      name: receiverName,
      type: "line",
      showSymbol: receiverData.length !== 1 ? false : true, // 默认情况下不显示标记
      data: receiverData, // 将时间戳转换为毫秒
    };
  });
};

// 初始化图表
const initChart = (data, unit = "", infoClick = false) => {
  infos.value = true;
  if (myChart) {
    myChart.dispose();
  }
  try {
    let dateData = Object.keys(data[Object.keys(data)[0]][0]);
    startTime = dateData[0];
    endTime = dateData[dateData.length - 1];
    myChart = echarts.init(chart.value);
    const seriesData = prepareSeriesData(data);
    const zoomOption = {
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: "none",
          },
          restore: {},
          saveAsImage: {},
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          animation: false,
          label: {
            backgroundColor: "#505765",
          },
        },
        formatter: function (params) {
          return (
            params[0].axisValueLabel +
            "<br/>" +
            params
              .map((param) => param.seriesName + ": " + param.value[1] + unit)
              .join("<br/>")
          );
        },
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
        },
      ],
      grid: {
        left: "50px",
        right: "50px",
        top: "30px",
        bottom: "80px",
      },
      legend: {
        data: Object.keys(data),
      },
      xAxis: {
        type: "time", // 设置为时间类型
      },
      yAxis: {
        type: "value",
        axisLine: {
          show: false, // 隐藏 y 轴轴线
        },
        splitLine: {
          show: false, // 隐藏 y 轴网格线
        },
      },
      series: seriesData,
    };
    const option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          animation: false,
          label: {
            backgroundColor: "#505765",
          },
        },
      },

      grid: {
        left: "50px",
        right: "50px",
        top: "30px",
        bottom: "80px",
      },
      legend: {
        data: Object.keys(data),
      },
      xAxis: {
        type: "time", // 设置为时间类型
      },
      yAxis: {
        type: "value",
        axisLine: {
          show: false, // 隐藏 y 轴轴线
        },
        splitLine: {
          show: false, // 隐藏 y 轴网格线
        },
      },
      series: seriesData,
    };
    myChart.setOption(props.isdataZoom ? zoomOption : option);
  } catch (e) {
    console.log(e);
  }

  nextTick(() => {
    myChart.on("datazoom", function (event) {
      // 获取 dataZoom 事件的相关信息
      if (event.batch) {
        endTime = Math.floor(
          new Date(event.batch[0].endValue).getTime() / 1000,
        );
        startTime = Math.floor(
          new Date(event.batch[0].startValue).getTime() / 1000,
        );
      } else if (event.end) {
        let newData = [];
        Object.keys(data[Object.keys(data)[0]][0]).forEach((key) => {
          newData.push({ date: key });
        });
        let startValue =
          newData[Math.ceil(newData.length * (event.start / 100))];
        let endValue = newData[Math.ceil(newData.length * (event.end / 100))];
        startTime = startValue.date;
        endTime = endValue.date;
      }
    });
  });
  if (infoClick) {
    // 添加点击事件监听器
    myChart.on("click", function (event) {
      if (event.componentType === "series") {
        infoClick(event.value[0]);
      }
    });
  }
};

const setSeres = (data) => {
  const seriesData2 = prepareSeriesData(data);
  myChart.setOption({
    series: seriesData2,
  });
};

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
  }
});

defineExpose({
  initChart,
  setSeres,
});
</script>

<style scoped>
#chartArea {
  width: 85vw;
  height: 400px;
}
.bodys {
  position: relative;
  width: 85vw;
  height: 400px;
}
.search {
  position: absolute;
  top: 6px;
  right: -20px;
  width: 20px;
  height: 20px;
  z-index: 99;
  cursor: pointer;
}

/* 加载样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
}

.loading-wrapper {
  text-align: center;
}

.loading-text {
  margin-top: 12px;
  font-size: 16px;
  color: #3B8CFF;
  font-weight: 500;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 0 auto;
}

.loading-spinner > div {
  width: 12px;
  height: 12px;
  margin: 0 5px;
  background-color: #3B8CFF;
  border-radius: 100%;
  display: inline-block;
  animation: bounce-delay 1.4s infinite ease-in-out both;
}

.loading-spinner .bounce1 {
  animation-delay: -0.32s;
}

.loading-spinner .bounce2 {
  animation-delay: -0.16s;
}

@keyframes bounce-delay {
  0%, 80%, 100% {
    transform: scale(0);
  } 40% {
    transform: scale(1.0);
  }
}
</style>
