/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: any = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      constant: true,
      hideInMenu: true,
      i18nKey: 'route.403'
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      constant: true,
      hideInMenu: true,
      i18nKey: 'route.404'
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      constant: true,
      hideInMenu: true,
      i18nKey: 'route.500'
    }
  },
  {
    name: 'alarm-list',
    path: '/alarm-list',
    component: 'layout.base$view.alarm-list',
    meta: {
      title: '告警列表',
      localIcon: 'warning',
      order: 9
    }
  },
  {
    name: 'asf-data',
    path: '/asf-data',
    component: 'layout.base$view.asf-data',
    meta: {
      title: 'ASF数据',
      localIcon: 'tv20regular',
      order: 7
    }
  },
  {
    name: 'data-analysis',
    path: '/data-analysis',
    component: 'layout.base',
    meta: {
      title: '数据分析',
      localIcon: 'DataPie20Regular',
      order: 4
    },
    children: [
      {
        name: 'data-analysis_preprocessing',
        path: '/data-analysis/preprocessing',
        component: 'view.data-analysis_preprocessing',
        meta: {
          title: '数据预报',
          order: 4
        }
      },
      {
        name: 'data-analysis_synthesize',
        path: '/data-analysis/synthesize',
        component: 'view.data-analysis_synthesize',
        meta: {
          title: '数据综合处理',
          order: 3
        }
      },
      {
        name: 'data-analysis_time-delay-and-field-strength-calc',
        path: '/data-analysis/time-delay-and-field-strength-calc',
        component: 'view.data-analysis_time-delay-and-field-strength-calc',
        meta: {
          title: '时延和场强计算',
          order: 1
        }
      },
      {
        name: 'data-analysis_waveform-comparison',
        path: '/data-analysis/waveform-comparison',
        component: 'view.data-analysis_waveform-comparison',
        meta: {
          title: '波形比对',
          order: 2
        }
      }
    ]
  },
  {
    name: 'data-manage',
    path: '/data-manage',
    component: 'layout.base$view.data-manage',
    meta: {
      title: '数据管理',
      localIcon: 'AlignVerticalBottom',
      order: 8
    }
  },
  {
    name: 'data-statistics',
    path: '/data-statistics',
    component: 'layout.base',
    meta: {
      title: '数据统计',
      localIcon: 'DataTrending20Regular',
      order: 5
    },
    children: [
      {
        name: 'data-statistics_analysis-data',
        path: '/data-statistics/analysis-data',
        component: 'view.data-statistics_analysis-data',
        meta: {
          title: '分析数据',
          order: 2
        }
      },
      {
        name: 'data-statistics_ori-data',
        path: '/data-statistics/ori-data',
        component: 'view.data-statistics_ori-data',
        meta: {
          title: '原始数据',
          order: 1
        }
      }
    ]
  },
  {
    name: 'help',
    path: '/help',
    component: 'layout.base$view.help',
    meta: {
      title: '帮助',
      localIcon: 'Help',
      order: 13
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: '首页',
      localIcon: 'Home',
      i18nKey: 'route.home',
      order: 1
    }
  },
  {
    name: 'log',
    path: '/log',
    component: 'layout.base$view.log',
    meta: {
      title: '日志',
      localIcon: 'Document',
      order: 12
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: '登录',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'params-manage',
    path: '/params-manage',
    component: 'layout.base$view.params-manage',
    meta: {
      title: '参数管理',
      localIcon: 'MailInbox24Regular',
      order: 2
    }
  },
  {
    name: 'performance-assessment',
    path: '/performance-assessment',
    component: 'layout.base$view.performance-assessment',
    meta: {
      title: '评估报告',
      localIcon: 'DocumentCopy20Regular',
      order: 6
    }
  },
  {
    name: 'remote-manage',
    path: '/remote-manage',
    component: 'layout.base$view.remote-manage',
    meta: {
      title: '远程管理',
      localIcon: 'Meter',
      order: 11
    }
  },
  {
    name: 'row-data',
    path: '/row-data',
    component: 'layout.base',
    meta: {
      title: '数据接收',
      localIcon: 'AlignTop28Filled',
      order: 3
    },
    children: [
      {
        name: 'row-data_aggregate-data',
        path: '/row-data/aggregate-data',
        component: 'view.row-data_aggregate-data',
        meta: {
          title: '时间综合测量仪',
          order: 6
        }
      },
      {
        name: 'row-data_external-data',
        path: '/row-data/external-data',
        component: 'view.row-data_external-data',
        meta: {
          title: '外部系统数据',
          order: 7
        }
      },
      {
        name: 'row-data_interfere-data',
        path: '/row-data/interfere-data',
        component: 'view.row-data_interfere-data',
        meta: {
          title: '频谱干扰测试系统',
          order: 4
        }
      },
      {
        name: 'row-data_measure-data',
        path: '/row-data/measure-data',
        component: 'view.row-data_measure-data',
        meta: {
          title: '频率比对测量系统',
          order: 5
        }
      },
      {
        name: 'row-data_receive-data',
        path: '/row-data/receive-data',
        component: 'view.row-data_receive-data',
        meta: {
          title: 'eLORAN参数测量软件',
          order: 1
        }
      },
      {
        name: 'row-data_time-diff-data',
        path: '/row-data/time-diff-data',
        component: 'view.row-data_time-diff-data',
        meta: {
          title: '多通道时差测量软件',
          order: 2
        }
      },
      {
        name: 'row-data_weather-data',
        path: '/row-data/weather-data',
        component: 'view.row-data_weather-data',
        meta: {
          title: '气象数据',
          order: 3
        }
      }
    ]
  },
  {
    name: 'work-condition',
    path: '/work-condition',
    component: 'layout.base$view.work-condition',
    meta: {
      title: '工况信息',
      localIcon: 'Timer20Regular',
      order: 10
    }
  }
];
