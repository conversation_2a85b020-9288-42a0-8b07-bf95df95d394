<template>
  <el-dialog
    v-model="dialogVisible"
    title="设置数据"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="data-setting">
      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <el-form :inline="true" :model="queryParams">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="X"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :loading="loading">
              查询
            </el-button>
          </el-form-item>
        </el-form>
        <div class="right-buttons">
          <!-- <el-button type="success" @click="handleAddRule">
            新增规则
          </el-button> -->
          <el-button type="primary" @click="handleGlobalSetting">
            设置全局
          </el-button>
          <el-button
            type="warning"
            @click="handleBatchToggle(1)"
            :disabled="!selectedRows.length"
          >
            批量屏蔽
          </el-button>
          <el-button
            type="success"
            @click="handleBatchToggle(0)"
            :disabled="!selectedRows.length"
          >
            批量恢复
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        height="400"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="startTime" label="开始时间" width="180" />
        <el-table-column prop="endTime" label="结束时间" width="180" />
        <el-table-column prop="duration" label="持续时间(s)" width="120" />
        <el-table-column prop="transmitter" label="发波台" width="120">
          <template #default="{ row }">
            {{ proxy.configure.typeObj[row.transmitter] }}
          </template>
        </el-table-column>
        <el-table-column prop="ignoreFlag" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.ignoreFlag === 1 ? 'warning' : 'success'">
              {{ row.ignoreFlag === 1 ? "已屏蔽" : "正常" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              link
              :type="row.ignoreFlag === 1 ? 'success' : 'warning'"
              @click="handleToggleStatus(row)"
            >
              {{ row.ignoreFlag === 1 ? "恢复" : "屏蔽" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <global-setting-dialog
      v-model:visible="globalSettingVisible"
      @success="handleGlobalSettingSuccess"
    />

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="查看详情"
      width="600px"
      append-to-body
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="开始时间">
          {{ viewData.startTime }}
        </el-descriptions-item>
        <el-descriptions-item label="结束时间">
          {{ viewData.endTime }}
        </el-descriptions-item>
        <el-descriptions-item label="持续时间">
          {{ viewData.duration }}秒
        </el-descriptions-item>
        <el-descriptions-item label="发波台">
          {{ proxy.configure.typeObj[viewData.transmitter] }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewData.ignoreFlag === 1 ? 'warning' : 'success'">
            {{ viewData.ignoreFlag === 1 ? "已屏蔽" : "正常" }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import apiAjax from "@/api/index";
import GlobalSettingDialog from "./GlobalSettingDialog.vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "success"]);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 查询参数
const queryParams = reactive({
  page: 1,
  size: 10,
  startTime: undefined,
  endTime: undefined,
});

// 日期范围
const dateRange = ref([]);
// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    queryParams.startTime = parseInt(newVal[0]);
    queryParams.endTime = parseInt(newVal[1]);
  } else {
    queryParams.startTime = undefined;
    queryParams.endTime = undefined;
  }
});

const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRows = ref([]);

// 格式化时间
const formatTime = (timestamp) => {
  return dayjs(timestamp * 1000).format("YYYY-MM-DD HH:mm:ss");
};

// 查询数据
const handleQuery = async (info = false) => {
  if (!info) return;
  loading.value = true;
  try {
    // const res = await apiAjax.post(
    //   `api/jnx/dataProcess/getComprehensive?endTime=${queryParams.endTime}&page=${queryParams.page}&size=${queryParams.size}&startTime=${queryParams.startTime}`
    // );
    const res = await apiAjax.post(
      `api/jnx/dataProcess/getComprehensive?endTime=${1736508863}&page=${
        queryParams.page
      }&size=${queryParams.size}&startTime=${1736505463}`
    );

    if (res && res[0]) {
      tableData.value = res[0].content;
      total.value = res[0].totalElements;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.size = val;
  queryParams.page = 1;
  handleQuery();
};

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.page = val;
  handleQuery();
};

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 新增规则
const handleAddRule = () => {
  // TODO: 实现新增规则的逻辑
};

// 设置全局
const globalSettingVisible = ref(false);

const handleGlobalSetting = () => {
  globalSettingVisible.value = true;
};

const handleGlobalSettingSuccess = () => {
  handleQuery();
};

// 修改状态切换处理函数
const handleToggleStatus = async (row) => {
  try {
    await apiAjax.post("/api/jnx/dataProcess/setComprehensive", [
      {
        id: row.id,
        ignoreFlag: row.ignoreFlag === 1 ? 0 : 1,
      },
    ]);
    ElMessage.success(row.ignoreFlag === 1 ? "恢复成功" : "屏蔽成功");
    handleQuery(true);
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error("操作失败");
  }
};

// 批量状态切换
const handleBatchToggle = async (targetStatus) => {
  if (!selectedRows.value.length) {
    ElMessage.warning("请选择要操作的数据");
    return;
  }

  try {
    const toggleData = selectedRows.value
      .filter((row) => row.ignoreFlag !== targetStatus)
      .map((row) => ({
        id: row.id,
        ignoreFlag: targetStatus,
      }));

    if (!toggleData.length) {
      ElMessage.warning(
        targetStatus === 1 ? "所选数据都已被屏蔽" : "所选数据都已是正常状态"
      );
      return;
    }

    await apiAjax.post("/api/jnx/dataProcess/setComprehensive", toggleData);
    ElMessage.success(targetStatus === 1 ? "批量屏蔽成功" : "批量恢复成功");
    handleQuery(true);
  } catch (error) {
    console.error("批量操作失败:", error);
    ElMessage.error("批量操作失败");
  }
};

// 查看详情相关
const viewDialogVisible = ref(false);
const viewData = ref({
  startTime: "",
  endTime: "",
  duration: 0,
  transmitter: "",
  ignoreFlag: 0,
});

// 查看
const handleView = (row) => {
  viewData.value = {
    startTime: formatTime(row.startTimeSeconds),
    endTime: formatTime(row.endTimeSeconds),
    duration: row.duration,
    transmitter: row.transmitter,
    ignoreFlag: row.ignoreFlag,
  };
  viewDialogVisible.value = true;
};

// 编辑
const handleEdit = (row) => {
  // TODO: 实现编辑的逻辑
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确定要删除这条数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      // TODO: 调用删除接口
      ElMessage.success("删除成功");
      handleQuery();
    } catch (error) {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  viewDialogVisible.value = false;
  dateRange.value = [];
  selectedRows.value = [];
  queryParams.page = 1;
};

// 监听弹窗显示
watch(
  () => dialogVisible.value,
  (newVal) => {
    if (newVal) {
      // 设置默认时间范围为今天的0点到23:59:59
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayEnd = new Date();
      todayEnd.setHours(23, 59, 59, 999);

      // 先设置查询参数
      queryParams.startTime = Math.floor(today.getTime() / 1000);
      queryParams.endTime = Math.floor(todayEnd.getTime() / 1000);

      // 设置日期范围，但不触发查询
      dateRange.value = [queryParams.startTime, queryParams.endTime];

      // 直接查询一次
      handleQuery(true);
    }
  },
  { immediate: false } // 添加 immediate: false 确保不会在组件初始化时执行
);

defineExpose({
  handleClose,
});
</script>

<style lang="scss" scoped>
.data-setting {
  .toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .right-buttons {
      display: flex;
      gap: 12px;
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  :deep(.el-table) {
    margin: 16px 0;
  }
}
</style>
