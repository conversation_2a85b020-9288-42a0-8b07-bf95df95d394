<template>
  <div class="receiver-dome-container">
    <!-- 筛选条件区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="originalForm" class="filter-form">
        <el-form-item label="通道">
          <el-select
            style="width: 130px"
            v-model="originalForm.channel"
            placeholder="请选择通道"
          >
            <el-option
              v-for="item in newthoroughfare"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="originalForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>

        <el-form-item class="button-group">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button type="primary" @click="handleExport">导出</el-button>
          <!-- <el-button type="primary" @click="handleCompare">比对</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表区域 -->
    <div class="chart-container">
      <lineChart ref="lineChartRef" :data="chartData" />

      <!-- 幅度统计卡片 -->
      <div
        class="stats-card"
        ref="statsCard"
        :style="{ left: cardPosition.x + 'px', top: cardPosition.y + 'px' }"
        :class="{ dragging: isDragging }"
        @mousedown="startDrag"
      >
        <div class="stats-card-header">
          <span>幅度统计</span>
          <i class="el-icon-rank drag-handle"></i>
        </div>
        <div class="stats-card-content">
          <div class="stats-item">
            <el-tag size="small" type="success">最大值</el-tag>
            <span class="stats-value">{{ amplitudeStats.max }}</span>
          </div>
          <div class="stats-item">
            <el-tag size="small" type="danger">最小值</el-tag>
            <span class="stats-value">{{ amplitudeStats.min }}</span>
          </div>
          <div class="stats-item">
            <el-tag size="small" type="primary">均值</el-tag>
            <span class="stats-value">{{ amplitudeStats.avg.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import { ElMessage } from "element-plus";
import apiAjax from "@/api/index";
const loading = ref(false);
const newthoroughfare = [
  {
    id: 1,
    label: "波形组1",
    value: "1",
  },
  {
    id: 2,
    label: "波形组2",
    value: "2",
  },
  {
    id: 3,
    label: "波形组3",
    value: "3",
  },
  {
    id: 4,
    label: "波形组4",
    value: "4",
  },
  {
    id: 5,
    label: "波形组5",
    value: "5",
  },
];
// 原始数据筛选表单
const originalForm = reactive({
  channel: "1",
  dataType: "xxx",
  key: "DVR",
  receiverId: "xxx",
  stationCode: "J02",
  transmitter: "xxx",
  waveTypeTd: "xxx",
  timeRange: [
    new Date(
      Date.UTC(
        new Date().getUTCFullYear(),
        new Date().getUTCMonth(),
        new Date().getUTCDate() - 1,
      ),
    )
      .toISOString()
      .split("T")[0],
    new Date(
      Date.UTC(
        new Date().getUTCFullYear(),
        new Date().getUTCMonth(),
        new Date().getUTCDate(),
      ),
    )
      .toISOString()
      .split("T")[0],
  ],
});

// 幅度数据
const amplitudeData = ref([]);

// 格式化时间戳为YYYY-MM-DD HH:mm:ss格式
const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 仅格式化时间为HH:mm:ss格式
const formatTimeOnly = (timestamp) => {
  const date = new Date(timestamp);
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};
// 幅度统计数据
const amplitudeStats = reactive({
  max: 0,
  min: 0,
  avg: 0,
});

// 图表数据
const chartData = ref({
  xAxis: {
    type: "time",
    axisLabel: {
      formatter: function (value) {
        return formatTimeOnly(value);
      },
    },
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      data: [],
      type: "line",
      smooth: true,
    },
  ],
  // 添加数据缩放功能
  dataZoom: [
    {
      show: true,
      realtime: true,
      start: 0,
      end: 5,
    },
  ],
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      let result = formatTimestamp(params[0].value[0]) + "<br/>";
      params.forEach((param) => {
        result += param.seriesName + "：" + param.value[1] + "<br/>";
      });
      return result;
    },
  },
  toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: "none",
      },
      saveAsImage: {},
    },
  },
  legend: {
    data: [],
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    top: "15%",
    containLabel: true,
  },
});

const lineChartRef = ref(null);

// 幅度统计卡片拖拽相关
const statsCard = ref(null);
const chartContainer = ref(null);
const isDragging = ref(false);
const cardPosition = reactive({ x: 30, y: 60 }); // 设置初始位置
const initialMousePosition = reactive({ x: 0, y: 0 });
const initialCardPosition = reactive({ x: 0, y: 0 });

const seturlQuery = (url) => {
  const postData = {
    endTime: Math.floor(
      new Date(originalForm.timeRange[1] + "T23:59:59").getTime() / 1000,
    ),
    startTime: Math.floor(
      new Date(originalForm.timeRange[0] + "T00:00:00").getTime() / 1000,
    ),
    ...url,
  };
  delete postData.timeRange;
  let urlQuery = Object.keys(postData)
    .map((key) => `${key}=${postData[key]}`)
    .join("&");
  urlQuery.startsWith("&") && (urlQuery = urlQuery.slice(1));
  return urlQuery;
};

// 查询按钮处理函数
const handleSearch = async () => {
  loading.value = true;
  const urlQuery = seturlQuery(originalForm);
  try {
    let data = await apiAjax.post("/api/jnx/ls/getNormalHistory?" + urlQuery);
    if (Object.getOwnPropertyNames(data.series).length == 0) {
      ElMessage.warning("暂无数据");
      loading.value = false;
      return;
    }
    lineChartRef.value.setInfos();
    updateAmplitudeStats(data);
    // 更新图表和表格
    updateChart(data);
  } catch (error) {
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 更新图表数据
const updateChart = (data) => {
  // 根据选择的通道计算对应的channel编号
  const channelNumber = parseInt(originalForm.channel);
  const waveformChannel = `channel${channelNumber * 2 - 1}`; // 波形通道（奇数）
  const amplitudeChannel = `channel${channelNumber * 2}`; // 幅度通道（偶数）

  const waveformData = data.series[waveformChannel] || [];
  const amplitudeData = data.series[amplitudeChannel] || [];

  // 转换数据格式为echarts需要的格式
  const waveformSeriesData = waveformData.map((item) => [
    item.timeSeconds * 1000, // 转换为毫秒时间戳
    item.value,
  ]);

  const amplitudeSeriesData = amplitudeData.map((item) => [
    item.timeSeconds * 1000, // 转换为毫秒时间戳
    item.value,
  ]);

  // 更新图表数据
  chartData.value.series = [
    {
      name: "波形",
      data: waveformSeriesData,
      type: "line",
      smooth: true,
    },
    {
      name: "幅度",
      data: amplitudeSeriesData,
      type: "line",
      smooth: true,
    },
  ];

  // 更新图表图例
  chartData.value.legend.data = ["波形", "幅度"];
};

// 更新幅度统计数据
const updateAmplitudeStats = (data) => {
  console.log("data", data);
  // 根据选择的通道计算对应的幅度channel编号
  const channelNumber = parseInt(originalForm.channel);
  const amplitudeChannel = `channel${channelNumber * 2}`; // 幅度通道（偶数）
  console.log("amplitudeChannel", amplitudeChannel);
  // 获取统计数据
  const stats = data.statistics?.[amplitudeChannel];

  if (stats) {
    // 更新统计数据
    amplitudeStats.max = stats.max;
    amplitudeStats.min = stats.min;
    amplitudeStats.avg = stats.avg;
  } else {
    // 如果没有统计数据，重置为0
    amplitudeStats.max = 0;
    amplitudeStats.min = 0;
    amplitudeStats.avg = 0;
  }
};

onMounted(() => {
  handleSearch();
});

// 处理导出功能
const handleExport = () => {
  console.log("导出数据");
  // 导出功能实现
};

// 开始拖动
const startDrag = (event) => {
  isDragging.value = true;
  initialMousePosition.x = event.clientX;
  initialMousePosition.y = event.clientY;
  initialCardPosition.x = cardPosition.x;
  initialCardPosition.y = cardPosition.y;

  document.addEventListener("mousemove", onDrag);
  document.addEventListener("mouseup", stopDrag);
};

// 拖动中
const onDrag = (event) => {
  if (!isDragging.value) return;

  // 计算新位置
  const deltaX = event.clientX - initialMousePosition.x;
  const deltaY = event.clientY - initialMousePosition.y;

  let newX = initialCardPosition.x + deltaX;
  let newY = initialCardPosition.y + deltaY;

  // 获取容器和卡片的尺寸，限制拖动范围
  const containerRect = document
    .querySelector(".chart-container")
    .getBoundingClientRect();
  const cardRect = statsCard.value.getBoundingClientRect();

  // 限制拖动范围，确保卡片不会拖出容器
  const maxX = containerRect.width - cardRect.width - 20; // 20是内边距
  const maxY = containerRect.height - cardRect.height - 20;

  // 应用限制
  newX = Math.max(10, Math.min(newX, maxX));
  newY = Math.max(10, Math.min(newY, maxY));

  cardPosition.x = newX;
  cardPosition.y = newY;
};

// 停止拖动
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
};

// 组件卸载前清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
});
</script>

<style lang="scss" scoped>
.receiver-dome-container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  .button-group {
    margin-left: auto;
  }
  .filter-container {
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }

    .data-type-toggle {
      // margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      flex: 1;

      .el-form-item {
        margin-bottom: 10px;
        margin-right: 16px;
      }
    }
  }

  .chart-container {
    flex: 1;
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    position: relative;
    overflow: hidden;

    .stats-card {
      position: absolute;
      width: 160px;
      background-color: rgba(255, 255, 255, 0.95);
      border-radius: 6px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      z-index: 10;
      overflow: hidden;
      border: 1px solid #ebeef5;
      cursor: move;
      user-select: none;
      transition: box-shadow 0.2s ease;

      &.dragging {
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
        opacity: 0.9;
      }

      .stats-card-header {
        padding: 8px 10px;
        font-weight: bold;
        text-align: center;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .drag-handle {
          cursor: move;
          color: #909399;
        }
      }

      .stats-card-content {
        padding: 12px;

        .stats-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .stats-value {
            font-weight: bold;
            color: #606266;
          }
        }
      }
    }
  }
}
</style>
