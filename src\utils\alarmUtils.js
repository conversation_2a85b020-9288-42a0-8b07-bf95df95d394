/**
 * 获取告警等级对应的文本
 * @param {string} level 告警等级
 * @returns {string} 告警等级文本
 */
export const getAlarmLevelText = (level) => {
  const levelMap = {
    '1': '一级告警',
    '2': '二级告警',
    '3': '三级告警'
  };
  return levelMap[level] || level;
};

/**
 * 获取告警等级对应的颜色
 * @param {string} level 告警等级
 * @returns {string} 告警等级颜色
 */
export const getAlarmLevelColor = (level) => {
  const colorMap = {
    '3': '#BBFF00',
    '2': '#FFA500',
    '1': '#FF0000'
  };
  return colorMap[level] || '';
};
