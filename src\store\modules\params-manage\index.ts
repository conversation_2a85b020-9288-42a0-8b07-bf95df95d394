import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import { detailTableColumn, detailTableData } from '@/views/params-manage/data.js';

export const useParamsManage = defineStore(
  SetupStoreId.ParamsManage,
  () => {
    // states
    const data = reactive({
      form1: {
        localIp: "***********:8080"
      },
      form2: {
        path1: '',
        path2: '',
        path3: '',
        path4: '',
        path5: '',
      },
      form3: {
        autoRun: true,
        siteName: '',
        pwd: true,
        sreenLockWaitTime: 20,
        sreenLockPwd: '123'
      },
      form4: {
        path: '',
        port: '',
        account: '',
        pwd: '',
        dbName: '',
      },
      siteList: [
        { name: '敦煌授时监测站', value: '敦煌授时监测站' },
        { name: '敦煌授时监测站', value: '敦煌授时监测站' },
        { name: '库尔勒授时监测站', value: '库尔勒授时监测站' },
        { name: '拉萨授时监测站', value: '拉萨授时监测站' },
        { name: '敦煌监测中心站', value: '敦煌监测中心站' },
      ],
      detailTableData,
      detailTableColumn
    });
    // getters

    // actions

    return {
      ...toRefs(data),
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: true,
  }
);
