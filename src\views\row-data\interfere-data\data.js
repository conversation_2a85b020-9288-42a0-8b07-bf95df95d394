import { ref, reactive } from "vue";


function setSeries(min = 0, max, formatter) {
  return {
    series: [
      {
        type: "gauge",
        startAngle: 205,
        endAngle: -25,
        min: min,
        max: max,
        radius: '93%',      // 调整仪表盘的半径
        center: ['50%', '50%'], // 调整仪表盘的中心位置 [水平, 垂直]，百分比表示相对于容器的中心
        axisLine: {
          lineStyle: {
            width: 20, // 设置圆弧宽度为10
            color: [
              [
                0.3,
                {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    { offset: 0, color: "#67e0e3" },
                    { offset: 1, color: "#67e0e3" },
                  ],
                },
              ],
              [
                0.7,
                {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    { offset: 0, color: "#67e0e3" },
                    { offset: 1, color: "#fd666d" },
                  ],
                },
              ],
              [
                1,
                {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    { offset: 0, color: "#fd666d" },
                    { offset: 1, color: "#fd666d" },
                  ],
                },
              ],
            ],
          },
        },
        pointer: {
          itemStyle: {
            color: "auto",
          },
        },
        axisTick: {
          length: 18,
          lineStyle: {
            color: "#fff",
            width: 1,
          },
        },
        splitLine: {
          lineStyle: {
            color: "#fff",
            width: 4,
          },
        },
        axisLabel: {
          color: "#000",
          distance: 12,
          fontSize: 12,
        },
        detail: {
          valueAnimation: true,
          formatter: "{value}" + formatter,
          color: '#266fe8', // 设置为蓝色
          bottom: '10%'
        },
        data: [
          {
            value: 0,
          },
        ],
      },
    ],
  }
}

export let chartData = reactive({
  cfreq: {
    name: "干扰中心频谱",
    data: {
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%'
      },
      series: [
        {
          min: 0,
          max: 200,
          radius: '93%',      // 调整仪表盘的半径
          center: ['50%', '50%'], // 调整仪表盘的中心位置 [水平, 垂直]，百分比表示相对于容器的中心
          type: "gauge",
          startAngle: 205,
          endAngle: -25,
          axisLine: {
            lineStyle: {
              width: 20, // 设置圆弧宽度为10
              color: [
                [
                  0.3,
                  {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      { offset: 0, color: "#1978FF" },
                      { offset: 1, color: "#FF8888" },
                    ],
                  },
                ],
                [
                  0.7,
                  {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      { offset: 0, color: "#FF8888" },
                      { offset: 1, color: "#FF8888" },
                    ],
                  },
                ],
                [
                  1,
                  {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      { offset: 0, color: "#FF8888" },
                      { offset: 1, color: "#1978FF" },
                    ],
                  },
                ],
              ],
            },
          },
          pointer: {
            itemStyle: {
              color: "auto",
            },
          },
          axisTick: {
            length: 18,
            lineStyle: {
              color: "#fff",
              width: 1,
            },
          },
          splitLine: {
            lineStyle: {
              color: "#fff",
              width: 4,
            },
          },
          axisLabel: {
            color: "#000",
            distance: 12,
            fontSize: 12,
          },
          detail: {
            valueAnimation: true,
            formatter: "{value}KHz",
            color: '#266fe8' // 设置为蓝色
          },
          data: [
            {
              value: 0,
            },
          ],
        },
      ],
    },
  },
  avgPwr: {
    name: "干扰平均功率",
    data: setSeries(-10, 100, " dB")
  },
  maxPwr: {
    name: "干扰最大功率",
    data: setSeries(-10, 100, " dB")
  },
  pwrVar: {
    name: "干扰功率方差",
    data: setSeries(-10, 100, "")
  },
  intDur: {
    name: "干扰时长",
    data: setSeries(0, 3600, "s")
  }
})
export let detailsData = reactive({
  chartsss: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        var tooltipText = params[0].name + "<br>";
        params.forEach(function (item) {
          var seriesName = item.seriesName;
          var value = Math.abs(item.value); // 取绝对值以显示正数
          tooltipText += seriesName + ": " + value + "<br>";
        });
        return tooltipText;
      },
    },
    legend: {
      data: ["干扰中心频谱",],
      bottom: 0
    },
    grid: {
      top: "10%",
      left: "3%",
      right: "4%",
      bottom: "20%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        axisTick: {
          show: false,
        },
        data: [
          "00:00",
          "01:00",
          "02:00",
          "03:00",
          "04:00",
          "05:00",
          "06:00",
          "07:00",
          "08:00",
          "09:00",
          "10:00",
          "11:00",
          "12:00",
          "13:00",
          "14:00",
          "15:00",
          "16:00",
          "17:00",
          "18:00",
        ],
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: function (value) {
            return Math.abs(value); // 在 y 轴标签上显示正数
          },
        },
      },
    ],
    series: [
      {
        name: "干扰中心频谱",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          position: "top",
        },
        itemStyle: {
          color: '#1978ff' // 设置柱状图的颜色为红色
        },
        emphasis: {
          focus: "series",
        },
        data: [
          320, 302, 341, 374, 390, 450, 420, 0, 100, 500, 600, 0, 411, 50, 353,
          354, 0, 543, 65,
        ],
      },
      {
        name: "干扰平均功率",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          position: "bottom",
          formatter: function (params) {
            return Math.abs(params.value); // 将负数值转换为正数显示支出
          },
        },
        emphasis: {
          focus: "series",
        },
        itemStyle: {
          color: '#0fc596' // 设置柱状图的颜色为红色
        },
        data: [
          -120, -132, -101, -134, -190, -230, -210, -52, -23, -54, -4, -5, -7,
          -41, -55, -11, -74, -44, -11,
        ], // 负数表示支出的绝对值
      },
    ],
  },
  chart: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        var tooltipText = params[0].name + "<br>";
        params.forEach(function (item) {
          var seriesName = item.seriesName;
          var value = Math.abs(item.value[1]); // 取绝对值以显示正数
          tooltipText += seriesName + ": " + value + "<br>";
        });
        return tooltipText;
      },
    },
    legend: {
      data: ["干扰中心频谱", "干扰平均功率"],
      bottom: 0
    },
    grid: {
      top: "10%",
      left: "3%",
      right: "4%",
      bottom: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "time"
    },
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: function (value) {
            return Math.abs(value); // 在 y 轴标签上显示正数
          },
        },
      },
    ],
    series: [
      {
        name: "干扰中心频谱",
        type: "bar",
        stack: "total",
        label: {
          show: true,
          position: "top",
        },
        itemStyle: {
          color: '#1978ff' // 设置柱状图的颜色为红色
        },
        emphasis: {
          focus: "series",
        },
        data: [],
      },
      // {
      //   name: "干扰平均功率",
      //   type: "bar",
      //   stack: "total",
      //   label: {
      //     show: true,
      //     position: "bottom",
      //     formatter: function (params) {
      //       return Math.abs(params.value[1]); // 将负数值转换为正数显示支出
      //     },
      //   },
      //   emphasis: {
      //     focus: "series",
      //   },
      //   itemStyle: {
      //     color: '#0fc596' // 设置柱状图的颜色为红色
      //   },
      //   data: [], // 负数表示支出的绝对值
      // },
    ],
  },
  tableData: [],
  chartList: {
    cfreq: [],
    avgPwr: []
  }
})
// 模拟假的数据
export const setDatse = () => {
  function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  function generateTimestamp(baseTime, offsetSeconds) {
    const baseDate = new Date(baseTime);
    baseDate.setSeconds(baseDate.getSeconds() + offsetSeconds);
    return baseDate.toISOString().replace(/[-:.TZ]/g, '').slice(0, 14);
  }

  // 设定干扰开始时间为当前时间
  const intStart = new Date();
  const intStartTimestamp = intStart.toISOString().replace(/[-:.TZ]/g, '').slice(0, 14);

  // 计算干扰时长（秒）
  const intDur = getRandomInt(0, 3600);

  // 生成其他时间戳
  const intEndTimestamp = generateTimestamp(intStart, intDur);
  const uploadTimestamp = generateTimestamp(intStart, intDur + getRandomInt(1, 10)); // 上传时间在干扰结束后1-10秒之间
  const monTimestamp = generateTimestamp(intStart, getRandomInt(0, intDur)); // 监测时间在干扰开始和结束之间

  return [{
    "intStart": intStartTimestamp + "", // 干扰开始时间
    "upTime": uploadTimestamp + "", // 上传时间
    "intDur": intDur + '', // 干扰时长 0-3600 s
    "cfreq": getRandomInt(0, 200), // 干扰中心频谱 0 - 200 KHz
    "maxPwr": getRandomInt(-10, 100), // 干扰最大功率 -10 - 100 dB
    "pwrVar": getRandomInt(0, 100), // 干扰功率方差 0 - 100 dB
    "intEnd": intEndTimestamp, // 干扰截止时间
    "avgPwr": getRandomInt(-10, 100), // 干扰平均功率 -10 - 100 dB
    "monTime": monTimestamp + '', // 监测时间
  }];
};


