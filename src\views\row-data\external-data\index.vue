<template>
  <div class="external-data">
    <div class="one-card">
      <el-card class="cardBox cardBox_box1">
        <template #header>
          <div class="card-header">
            <span>参考时码(时频分系统)</span>
          </div>
        </template>
        <div class="card-content">
          <div class="card-date">
            <div class="date-title">{{ externalData.time }}</div>
            <div class="date-time"><FlipClock /></div>
          </div>
          <div class="card-time-text">
            <div class="nav">实时报文</div>
            <div class="box">
              {{ message }}
            </div>
          </div>
        </div>
      </el-card>
      <el-card class="cardBox">
        <template #header>
          <div class="card-header">
            <span>DUT1(1603T程)</span>
          </div>
        </template>
        <div class="card-content">
          <div class="card-data">
            <div class="date-actual">
              <div class="value">100</div>
              <div class="navs">采集零时值</div>
            </div>
            <div class="date-expected">
              <div class="value">200</div>
              <div class="navs">预测零时值</div>
            </div>
          </div>
          <div class="card-time-text">
            <div class="nav">实时报文</div>
            <div class="box">
              #NTPRST=12,0,3929241072.9601971,3929241072.9522464,3929241072.9522466,3929241072.9601968,5,-19,0,-6.06349206349206,8.58916448414229,0,0,0,NTP
              Up,Locked GPS
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="two-card">
      <el-card class="cardBox">
        <template #header>
          <div class="card-header">
            <span>差分修正量(参数测量软件)</span>
          </div>
        </template>
        <el-table class="tableHome" :data="data1">
          <el-table-column property="date" label="名称" />
          <el-table-column property="date" label="标称ASF值" />
          <el-table-column property="state" label="差分站状态" />
          <el-table-column property="state" label="预报模型起点时刻" />
          <el-table-column property="state" label="外推模型参数" />
          <el-table-column property="state" label="数据期龄" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import FlipClock from "@/components/clock/index.vue";
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
import dayjs from "dayjs";
import { reactive, onMounted, onUnmounted, ref } from "vue";
const externalData = reactive({
  time: dayjs(new Date()).format("YYYY年MM月DD日"),
});
const message1 = ref("");
onMounted(async () => {
  await initScocket();
  subscribe("/topic/jsInfo/tmprepro/TCP-DTZ-TMREPRO", setFrequency);
});

onUnmounted(() => {
  unsubscribe("/topic/jsInfo/tmprepro/TCP-DTZ-TMREPRO");
});
// 设置时频分系统数据
const setFrequency = (data) => {
  let newData = JSON.parse(data);
  message1.value = newData.data;
};

const data1 = [
  { date: "07-01 13:00", state: "97" },
  { date: "07-01 13:00", state: "97" },
  { date: "07-01 13:00", state: "97" },
];
</script>

<style lang="scss" scoped>
.external-data {
  width: 100%;
  height: 96%;
  display: flex;
  flex-direction: column;
  .one-card {
    display: flex;
    .cardBox {
      height: 354px;
      background: #ffffff;
    }
    .cardBox_box1 {
      margin-right: 16px;
    }
  }
  .two-card {
    margin-top: 16px;
    display: flex;
    flex: 1;
  }
  .cardBox {
    flex: 1;
    :deep(.el-card__header) {
      padding: 15px 16px;
    }
    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .card-content {
    display: flex;
    flex-direction: column;
    .card-date {
      display: flex;
      flex-direction: column;
      .date-title {
        font-weight: 600;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 17px;
      }
      .date-time {
        display: flex;
        margin-left: -10px;
        margin-bottom: 25px;
      }
    }
    .value {
      font-weight: 600;
      font-size: 24px;
      color: #2b2e3f;
    }
    .navs {
      margin-top: 17px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 65px;
    }
  }
  .card-time-text {
    display: flex;
    flex-direction: column;
    height: 110px;
    background: hsla(217, 81%, 53%, 0.062);
    padding: 18px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.9);
    .nav {
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 7px;
    }
    .box {
      width: 755px;
      word-break: break-all;
    }
  }
  .card-data {
    display: flex;
  }
  .date-actual {
    display: flex;
    flex-direction: column;
    margin-left: 20px;
  }
  .date-expected {
    margin-left: 40px;
    display: flex;
    flex-direction: column;
  }
}
.tableHome {
  :deep(.is-leaf) {
    background: #fafafa;
  }
  :deep(.el-table__cell) {
    padding: 10px 0;
  }
}
</style>
