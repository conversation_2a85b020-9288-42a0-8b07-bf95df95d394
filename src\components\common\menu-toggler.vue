<script lang="ts" setup>
import { computed } from "vue";
import { $t } from "@/locales";

defineOptions({ name: "MenuToggler" });

interface Props {
  /** Show collapsed icon */
  collapsed?: boolean;
  /** Arrow style icon */
  arrowIcon?: boolean;
  zIndex?: number;
}

const props = withDefaults(defineProps<Props>(), {
  arrowIcon: false,
  zIndex: 98,
});

type NumberBool = 0 | 1;

const icon = computed(() => {
  const icons: any = {
    1: "foldleft",
    0: "foldright",
  };

  const collapsed = Number(props.collapsed || false) as NumberBool;

  return icons[collapsed];
});
</script>

<template>
  <ButtonIcon
    :key="String(collapsed)"
    :tooltip-content="collapsed ? '展开菜单' : '折叠菜单'"
    tooltip-placement="bottom-start"
    :z-index="zIndex"
  >
    <SvgIcon :localIcon="icon" />
  </ButtonIcon>
</template>
