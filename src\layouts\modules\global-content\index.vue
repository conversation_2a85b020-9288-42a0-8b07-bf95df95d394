<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { LAYOUT_SCROLL_EL_ID } from '@sa/materials';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { useRouteStore } from '@/store/modules/route';
import { useTabStore } from '@/store/modules/tab';
import GlobalBreadcrumb from '../global-breadcrumb/index.vue';

defineOptions({
  name: 'GlobalContent'
});

interface Props {
  /** Show padding for content */
  showPadding?: boolean;
}

withDefaults(defineProps<Props>(), {
  showPadding: true
});

const appStore = useAppStore();
const themeStore = useThemeStore();
const routeStore = useRouteStore();
const tabStore = useTabStore();

const transitionName = computed(() => (themeStore.page.animate ? themeStore.page.animateMode : ''));

function resetScroll() {
  const el = document.querySelector(`#${LAYOUT_SCROLL_EL_ID}`);

  el?.scrollTo({ left: 0, top: 0 });
}

const contentMinWidth = computed(() => {
  // 这里必须用siderd的width和collapsedWidth比较的结果来判断侧边栏状态，
  // 因位这两个值是立即更新的，而siderCollapse是等到折叠动画播放完才更新的，
  // 若通过该值判断设置页面最小宽度，在播放折叠动画过程中会出现水平滚动条。
  return themeStore.sider.width <= themeStore.sider.collapsedWidth ?
    themeStore.page.minWidth - themeStore.sider.collapsedWidth - 20 :
    themeStore.page.minWidth - themeStore.sider.width - 20;
});

onMounted(() => {
  document.documentElement.style.setProperty('--layout-bg-color', '240 242 245'); // #f0f2f5
})
</script>

<template>
  <RouterView v-slot="{ Component, route }">
    <Transition
      :name="transitionName"
      mode="out-in"
      @before-leave="appStore.setContentXScrollable(true)"
      @after-leave="resetScroll"
      @after-enter="appStore.setContentXScrollable(false)"
    >
      <KeepAlive v-if="route.name === 'login'">
        <component
          :is="Component"
          v-if="appStore.reloadFlag"
          :key="tabStore.getTabIdByRoute(route)"
          :class="{ 'p-16px': showPadding }"
          class="flex-grow bg-layout transition-300"
        />
      </KeepAlive>

      <KeepAlive v-else :include="routeStore.cacheRoutes">
        <div
          v-if="appStore.reloadFlag"
          :key="tabStore.getTabIdByRoute(route)"
          :class="{ 'content-padding': showPadding }"
          class="flex-grow bg-layout transition-300"
          :style="`min-width: ${contentMinWidth}px;`"
        >
          <GlobalBreadcrumb v-if="!appStore.isMobile" class="breadcrumb" />
          <component :is="Component" />
        </div>
      </KeepAlive>
    </Transition>
  </RouterView>
</template>

<style lang="scss" scoped>
.content-padding {
  padding: 0 16px 16px 16px;
}
.breadcrumb {
  margin: 12px 12px 12px 7px;
  font-weight: 400;
  font-size: 12px;
  color: #000000;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
</style>
