/**
 * Created by zcr on 2018/1/25.
 */
// 使用条件逻辑来处理可能的未定义情况
let url = '';
try {
  url = import.meta.env?.VITE_SERVICE_BASE_URL
} catch (e) {
  url = 'http://10.17.7.24';
}

console.log("VITE_SERVICE_BASE_URL", url);
let proxySource = {
  //10.31.15.74
  // 云服务 10.31.1.4
  "/Api": `${url}:8085`, // 外网测试环境
  "/logApi": `${url}:8080`, // 外网测试环境
},
  proxyRoot = [];
for (let key in proxySource) {
  proxyRoot.push(
    process.env.NODE_ENV === "production" ? proxySource[key] : key + "/"
  );
}
export default {
  prefix: "temp_", //项目前缀
  xAppVersion: "1.0.0", //版本
  base_path: "", //  项目地址
  proxySource: proxySource, //代理源配置
  root: proxyRoot, // 配置API接口地址
  proxyUrl: {
    defult: "",
  },
  publicKey: "LTAI4GKXdB0hcaFS78629b526a153b17", //app 加密密钥
  publicKeyRecharge: "jstxbetcUroad123", //圈存加密密钥
  publicKeyMd5Recharge: "LTAI4GKXdB0hcaFS78629b526a153b17",
  isTest: false, //是否测试环境
}