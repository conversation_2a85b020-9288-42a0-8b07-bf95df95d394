<template>
  <div class="con">
    <div class="top-con">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="场强" name="fieldStrength" :lazy="true">
          <div class="tabContent">
            <el-select
              v-model="preferredObj.optionIndex"
              placeholder="请选择"
              class="select"
              @change="selectChangeHandle"
            >
              <el-option
                v-for="item in preferredObj.optionList"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <div
              :class="
                preferredObj.optionIndex === '6000M'
                  ? 'select-label'
                  : 'hide-select-label'
              "
            ></div>
            <lineChart
              ref="lineChart1"
              class="chartArea"
              :data="preferredObj.chartData"
            ></lineChart>
          </div>
        </el-tab-pane>
        <el-tab-pane label="信噪比" name="snr" :lazy="true">
          <div class="tabContent">
            <el-select
              v-model="preferredObj.optionIndex"
              placeholder="请选择"
              class="select"
              @change="selectChangeHandle"
            >
              <el-option
                v-for="item in preferredObj.optionList"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <div
              :class="
                preferredObj.optionIndex === '6000M'
                  ? 'select-label'
                  : 'hide-select-label'
              "
            ></div>
            <lineChart
              ref="lineChart2"
              class="chartArea"
              :data="preferredObj.chartData"
            ></lineChart>
          </div>
        </el-tab-pane>
        <el-tab-pane label="包周差" name="packageTD" :lazy="true">
          <div class="tabContent">
            <el-select
              v-model="preferredObj.optionIndex"
              placeholder="请选择"
              class="select"
              @change="selectChangeHandle"
            >
              <el-option
                v-for="item in preferredObj.optionList"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <div
              :class="
                preferredObj.optionIndex === '6000M'
                  ? 'select-label'
                  : 'hide-select-label'
              "
            ></div>
            <lineChart
              ref="lineChart3"
              class="chartArea"
              :data="preferredObj.chartData"
            ></lineChart>
          </div>
        </el-tab-pane>
        <el-tab-pane label="频率偏差" name="frequencyTD" :lazy="true">
          <div class="tabContent">
            <el-select
              v-model="preferredObj.optionIndex"
              placeholder="请选择"
              class="select"
              @change="selectChangeHandle"
            >
              <el-option
                v-for="item in preferredObj.optionList"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <div
              :class="
                preferredObj.optionIndex === '6000M'
                  ? 'select-label'
                  : 'hide-select-label'
              "
            ></div>
            <lineChart
              ref="lineChart4"
              class="chartArea"
              :data="preferredObj.chartData"
            ></lineChart>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-table
      :data="preferredObj.tableData || []"
      border
      max-height="2000"
      style="width: 100%; margin-top: 16px"
    >
      <!-- <el-table-column property="name" align="center" label="设备" /> -->
      <!-- <el-table-column property="name" label="预测值" /> -->
      <!-- <el-table-column property="data" align="center" label="当前值" /> -->
      <el-table-column property="min" align="center" label="最小值" />
      <el-table-column property="max" align="center" label="最大值" />
      <el-table-column property="avg" align="center" label="平均值" />
      <el-table-column property="stdDev" align="center" label="标准差" />
      <el-table-column property="rms" align="center" label="RMS" />
      <el-table-column
        property="percentile95"
        align="center"
        label="95%分位数"
      />
      <el-table-column property="count" align="center" label="总数" />
    </el-table>
  </div>
</template>

<script setup>
import {
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  watch,
  nextTick,
  getCurrentInstance,
} from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import { chartColors } from "@/constants/chart";
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
import dayjs from "dayjs";
const { proxy } = getCurrentInstance();
const activeName = ref("fieldStrength");
const tabName = ref("场强");
const lineChart1 = ref(null);
const lineChart2 = ref(null);
const lineChart3 = ref(null);
const lineChart4 = ref(null);

onMounted(async () => {
  await initScocket();
  subscribe("/topic/jsInfo/syrealtimedata/ELORAN-DTZ-YXJCD", setDataFbjcd); // 图的实时数据
  subscribe("/topic/jsInfo/24hrealyxjcd/24h-REAL-YXJCD", setDataFromYXJCD); // 下面表格的实时数据
});
const setDataFromYXJCD = (datas) => {
  let newData = JSON.parse(datas);
  preferredObj.tableDataObj = newData;
  newData.forEach((i) => {
    if (
      i.dataTp == activeName.value &&
      i.transmitter == preferredObj.optionIndex
    ) {
      preferredObj.tableData[0] = i;
    }
  });
};
onBeforeUnmount(() => {
  unsubscribe("/topic/jsInfo/syrealtimedata/ELORAN-DTZ-YXJCD");
  unsubscribe("/topic/jsInfo/24hrealyxjcd/24h-REAL-YXJCD");
});

//总的值
const preferredObj = reactive({
  optionList: [], //下拉框的选的值 动态传入
  optionIndex: "6000M", // 默认选中的值，默认选中的是第一个值
  chartData: {
    title: {
      text: `单位：`,
      left: "left",
      top: "50",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "time",
      axisLabel: {
        interval: 0, // 显示所有标签
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false, // 隐藏 y 轴轴线
      },
      splitLine: {
        show: false, // 隐藏 y 轴网格线
      },
      min: 20.0,
      max: 20.2,
    },
    legend: {
      bottom: -4,
      itemHeight: 4,
      icon: "rect",
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: "#2B2E3F",
        lineHeight: 20,
        fontStyle: "normal",
      },
    },
    grid: {
      left: "60px",
      right: "30px",
      top: "100px",
      bottom: "50px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [
      {
        type: "line",
        data: [],

        showSymbol: false, // 默认情况下不显示标记
        symbolSize: 10, // 标记的大小
        itemStyle: {
          color: "#1d7bff",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(25, 120, 255, 0.2)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255, 255, 255, 0)", // 100% 处的颜色
              },
            ],
          },
          origin: "start", // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: "solid",
        },
      },
    ],
  },
  //这是所有数据处理的地方
  chartDataObj: {},
  tableData: [], // 现在显示的数据
  tableDataObj: [], //所有的表格数据
});

const setDataFbjcd = (data) => {
  const newData = JSON.parse(data);
  const dateTime = proxy.configure.setDate(newData.Date); // 预计算时间戳
  const date = new Date(dateTime);
  newData.transmitters.forEach((transmitter) => {
    const {
      transmitter: id,
      fieldStrength,
      snr,
      packageTD,
      frequencyTD,
    } = transmitter;

    const optionExists = preferredObj.optionList.some((i) => i.value === id);

    if (!optionExists) {
      preferredObj.optionList.push({
        key: id,
        label: proxy.configure.thoroughfare(id),
        value: id,
      });
    }
    preferredObj.optionIndex = preferredObj.optionIndex || id;

    // 确保目标对象存在
    if (!preferredObj.chartDataObj[id]) {
      preferredObj.chartDataObj[id] = {
        fieldStrength: [], //场强
        snr: [], //信噪比
        packageTD: [], //包周差
        frequencyTD: [], //频率偏差
      };
    }

    // 优化：用一个映射和循环来减少重复代码
    const dataMap = {
      fieldStrength,
      snr,
      packageTD,
      frequencyTD,
    };
    Object.keys(dataMap).forEach((key) => {
      if (dataMap[key]) {
        preferredObj.chartDataObj[id][key].push({
          name: date.toString(),
          value: [date, dataMap[key]],
        });
      }
    });

    const MAX_LENGTH = 360;
    //删除多余的数据
    if (preferredObj.chartDataObj[id].fieldStrength.length > MAX_LENGTH) {
      preferredObj.chartDataObj[id].fieldStrength.shift();
      preferredObj.chartDataObj[id].snr.shift();
      preferredObj.chartDataObj[id].packageTD.shift();
      preferredObj.chartDataObj[id].frequencyTD.shift();
    }
    nextTick(() => {
      getChartAreaData();
    });
  });
};
//得到当前应该显示的数据
const getChartAreaData = () => {
  const chartMap = {
    fieldStrength: {
      dom: lineChart1.value,
      name: "fieldStrength",
      title: "场强",
      unit: "dBμV/m",
    },
    snr: { dom: lineChart2.value, name: "snr", title: "信噪比", unit: "dB" },
    packageTD: {
      dom: lineChart3.value,
      name: "packageTD",
      title: "包周差",
      unit: "μs",
    },
    frequencyTD: {
      dom: lineChart4.value,
      name: "frequencyTD",
      title: "频率偏差",
      unit: "Hz",
    },
  };
  const chart = chartMap[activeName.value];
  if (
    Object.keys(preferredObj.chartDataObj).length === 0 ||
    preferredObj.optionIndex == undefined
  ) {
    return;
  }
  const list = preferredObj.chartDataObj[preferredObj.optionIndex][chart.name];
  if (proxy.configure.isEmpty(list)) {
    return;
  }
  let max = Math.max(...list.map((i) => i.value[1]));
  let min = Math.min(...list.map((i) => i.value[1]));
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) * 0.1;
    min = min - (max - min) * 0.1;
  }
  if (chart.dom) {
    chart.dom.setInfos();
    chart.dom.getIns().setOption({
      title: {
        text: `单位：${chart.unit}`,
      },
      grid: {
        left: (Math.ceil(max * 1000) / 1000 + "").length * 7 + 25 + "px",
      },
      yAxis: {
        max: Math.ceil(max * 1000) / 1000,
        min: Math.ceil(min * 1000) / 1000,
      },
      series: [
        {
          data: list,
          name: chart.title,
        },
      ],
    });
  }
};
watch(activeName, () => {
  //切换了类型
  getChartAreaData();
  preferredObj.optionIndex = preferredObj.optionList[0].id;
});
//切换发播台
const selectChangeHandle = () => {
  getChartAreaData();
  preferredObj.tableData = [];
};

const handleClick = (tab) => {
  tabName.value = tab.props.label;
  preferredObj.tableData = [];
};
</script>

<style lang="scss" scoped>
.top-con {
  background: #fff;
  border-radius: 2px;
  height: 440px;
  padding: 8px 0;

  :deep(.el-tabs__header) {
    margin: 0 0 0 2px;
  }

  .tabContent {
    padding: 0 10px;
  }

  .chartArea {
    width: 100%;
    height: 370px;
  }

  .select {
    width: 162px;
    position: absolute;
    top: 8px;
    left: 30px;
    z-index: 1;
  }

  .select-label {
    position: absolute;
    top: 14px;
    left: 170px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    text-align: center;
    font-style: normal;
    z-index: 1;
  }

  .hide-select-label {
    display: none;
  }

  :deep(.el-tabs) {
    .el-tabs__item {
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      text-align: right;
      font-style: normal;
      padding: 12px 12px 14px 12px;
      height: 48px;
      margin-right: 16px;
    }

    .el-tabs__nav {
      padding: 0 10px;
    }
  }
}

.con {
  :deep(.el-table) {
    --el-table-border-color: #e7e7e7;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }
}
</style>
<style>
.synthesize_tooltip {
  color: #266fe8 !important;
}
</style>
