<template>
  <div class="work-condition">
    <div class="Cabinet">
      <deviceDom
        v-for="item in cabinetAllData"
        :key="item.code"
        :cabinetData="item"
        :selectedData="selectedData"
        :mouseoverData="mouseoverData"
        :statusAllData="statusAllData"
        @setMagnified="setMagnified"
        @setSelectedData="setSelectedData"
      />
    </div>
    <div class="chartList">
      <el-card
        v-for="key in Object.keys(deviceData)"
        :key="key"
        class="cardItem"
      >
        <template #header>
          <div class="card-header" v-if="key !== 'multiChannel'">
            {{ deviceData[key].name }}
          </div>
          <div class="card-header" v-else>
            {{ tableData.name }}
          </div>
        </template>
        <div class="cardBox" v-if="key !== 'multiChannel'">
          <lineChart
            class="chartstate_chart"
            :data="deviceData[key].data"
            :ref="(el) => (boxP[key] = el)"
          />
          <div v-show="key == 'deviceState'">
            <div class="deviceState_nav">
              设备总数 {{ deviceStateAllData.all }}
            </div>
            <div class="deviceState_list">
              <div
                v-for="item in deviceStateAllData.deviceStateData"
                :key="item.name"
                class="deviceState_item"
              >
                <div class="deviceState_itemName">{{ item.value }}台</div>
                <div class="deviceState_itemValue">
                  占比
                  <span :style="'color:' + item?.itemStyle.color">{{
                    item.account
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chartstate_chart" v-else>
          <el-table :data="tableData.data" class="tableHome">
            <el-table-column property="name" align="center" label="工况类型" />
            <el-table-column
              property="displayValue"
              align="center"
              label="工况状态"
            >
              <template #default="scope">
                <div
                  :class="{
                    statusnormal: scope.row.code == 0,
                    statusabnormal: scope.row.code != 0,
                    status: true,
                  }"
                  v-if="'code' in scope.row"
                >
                  {{ scope.row.code != 0 ? "异常" : "正常" }}
                </div>
                <div v-else class="status">
                  {{ scope.row.type }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onUnmounted, defineProps } from "vue";
import { deviceData } from "./data";
import lineChart from "../../components/lineChart/lineCharts.vue";
import { initScocket, subscribe, unsubscribe, unsubscribeAll } from "@/api/ws";
import deviceDom from "@/components/device/deviceDom.vue";
import apiAjax from "@/api/index";
import { ElNotification } from "element-plus";
import { useRoute } from "vue-router";
const boxP = ref({}); // 图表数组
const route = useRoute();

const selectedData = ref({ id: undefined }); // 选中的数据
const mouseoverData = ref({ id: undefined }); // 鼠标悬停的id
const codeId = ref(""); // 存储当前下面列表的设备code

const setMagnified = (item = { id: undefined }) => {
  mouseoverData.value = item;
};

const setSelectedData = async (item = { id: undefined }) => {
  selectedData.value = item;
  if (item.id) {
    ElNotification.closeAll();
    if (statusAllData.value[item.code].opsCond == -1) {
      ElNotification({
        title: "提醒",
        message:
          statusAllData.value[item.code].name + "没有无设备连接状态，请重试",
        type: "warning",
        duration: 2000,
      });
    } else {
      await getGkRealtime(statusAllData.value[item.code]);
    }
  }
};

//机柜的状态 0-正常normal 1-警告fault 2-异常abnormal
const cabinetStatus = {
  0: {
    title: "正常",
    color: "#00C851", // 更鲜艳的绿色
    class: "normal",
  },
  1: {
    title: "警告",
    color: "#9E9E9E", // 更柔和的灰色
    class: "fault",
  },
  2: {
    title: "异常",
    color: "#FF4444", // 更鲜艳的红色
    class: "abnormal",
  },
};
// 设备的状态
const equipmentStatus = {
  0: { status: 0, title: "正常", class: "normal", color: "#00C851" }, // 正常-绿色
  2: { status: 2, title: "离线", class: "fault", color: "#9E9E9E" }, // 离线-灰色
  1: { status: 1, title: "异常", class: "abnormal", color: "#FF4444" }, // 异常-红色
};
let indexpositionright = {
  0: -300,
  1: -300,
  2: -300,
  3: 250,
  4: 250,
};
// 机柜数据
const cabinetAllData = ref([]);

// 全部的设备状态
const deviceStateAllData = reactive({
  all: 0,
  deviceStateData: [
    { value: 0, name: "正常", itemStyle: { color: "#00C851" }, account: "33%" }, // 正常-绿色
    { value: 0, name: "异常", itemStyle: { color: "#FF4444" }, account: "33%" }, // 异常-红色
    { value: 0, name: "离线", itemStyle: { color: "#9E9E9E" }, account: "33%" }, // 离线-灰色
  ],
});

// 这里直接扁平化 来处理状态  好更新一点
const statusAllData = ref({});
// 中间的表格数据
const tableData = ref({
  name: "",
  data: [],
});

// 获取机柜数据
const getGkInit = async () => {
  let data = await apiAjax.post("/api/jnx/dataGkInfo/getGkStatns");
  let stateAll = await apiAjax.post("/api/jnx/dataGkInfo/getGkInit");
  if (data) {
    setcabinetAllData(data, stateAll);
    // 第一次请求的话 这里去找一个 有code 的设备去查询状态
    const code = Object.values(statusAllData.value).find((item) => {
      return item.opsCond != -1;
    });
    if (code) {
      await getGkRealtime(code);
    } else {
      // 没有设备提醒一下
      ElNotification({
        title: "提醒",
        message: "当前没有无设备连接状态，请重试",
        type: "warning",
        duration: 3000,
      });
    }
  }
};

// 获取设备的里面的状态列表
const getGkRealtime = async (code) => {
  if (codeId.value == code.statn) {
    return;
  }

  if (codeId.value) {
    unsubscribe(
      "/topic/jsInfo/elorangkdinfo/ELORAN-DTZ-GKDINFO:" + codeId.value
    );
  }

  codeId.value = code.statn;

  tableData.value.name = code.name;
  tableData.value.data = await apiAjax.post(
    "/api/jnx/dataGkInfo/getGkRealtime",
    {
      opsCond: code.opsCond,
      statn: code.statn,
    }
  );
  subscribe(
    "/topic/jsInfo/elorangkdinfo/ELORAN-DTZ-GKDINFO:" + codeId.value,
    (data) => {
      if (data) {
        tableData.value.data = data;
      }
    }
  );
};

// 处理数据
const setcabinetAllData = (data, stateAll) => {
  cabinetAllData.value = [];
  data.forEach((i, index) => {
    i.allWorkStatus = Math.max(
      ...i.resources.map((item) => {
        let j = stateAll.find((item1) => item.code == item1.statn);
        if (j) {
          item.opsCond = j.opsCond;
          item.equipmentStatus = equipmentStatus[j.status];
        } else {
          item.opsCond = "-1";
          item.equipmentStatus = equipmentStatus[0];
        }
        statusAllData.value[item.code] = {
          id: item.id,
          name: i.description + "-" + item.name,
          opsCond: item.opsCond,
          statn: item.code,
          ...item.equipmentStatus,
        };
        item.positionright = indexpositionright[index];
        if (item.extendInfo?.importantLevel == 1) {
          // 这里是一级设备
          return item.workStatus >= 4 ? 2 : item.workStatus == 3 ? 1 : 0;
        } else {
          // 这里是不重要的设备
          return item.workStatus >= 3 ? 1 : 0;
        }
      })
    );
    i.cabinetStatus = cabinetStatus[i.allWorkStatus];
    if (i.code.indexOf("JG") !== -1) {
      cabinetAllData.value.push(i);
    }
  });
};

// 处理设备状态的数据
const setDeviceStateData = () => {
  let allNumber = cabinetAllData.value.reduce(
    (sum, i) => sum + i.resources.length,
    0
  );
  let normal = 0; // 正常
  let fault = 0; //  离线
  let abnormal = 0; //  异常

  Object.values(statusAllData.value).forEach((i) => {
    if (i.status == 1) {
      fault++;
    } else if (i.status == 2) {
      abnormal++;
    }else{
      normal++;
    }
  });

  // normal = allNumber - fault - abnormal;
  deviceStateAllData.all = allNumber;
  deviceStateAllData.deviceStateData = [
    {
      value: normal,
      name: "正常",
      itemStyle: { color: "#00C851" }, // 正常-绿色
      account: ((normal / allNumber) * 100).toFixed(1) + "%",
    },
    {
      value: fault,
      name: "异常",
      itemStyle: { color: "#FF4444" }, // 异常-红色
      account: ((fault / allNumber) * 100).toFixed(1) + "%",
    },
    {
      value: abnormal,
      name: "离线",
      itemStyle: { color: "#9E9E9E" }, // 离线-灰色
      account: ((abnormal / allNumber) * 100).toFixed(1) + "%",
    },
  ];
  boxP.value.deviceState.getIns().setOption({
    series: [{ data: [...deviceStateAllData.deviceStateData] }],
  });
};

onMounted(async () => {
  await initScocket();
  if (route.params) {
    const id = route.params.id; // 获取传递的 id
    console.log(id); // 输出：123
  }

  await getGkInit();
  setDeviceStateData();
  subscribe("/topic/jsInfo/realtimedata/ELORAN-DTZ-GKD", (data) => {
    if (data) {
      JSON.parse(data).forEach((i) => {
        let statusItem = equipmentStatus[i.status];
        statusAllData.value[i.statn] = {
          ...statusAllData.value[i.statn],
          ...i,
          ...statusItem,
        };
        setDeviceStateData();
      });
    }
  });
});
onUnmounted(() => {
  unsubscribeAll();
});
</script>

<style lang="scss" scoped>
.work-condition {
  width: 100%;
  .Cabinet {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 428px;
  }
  .chartList {
    display: flex;
    :deep(.el-card__header) {
      padding: 16px 24px;
    }
    margin-top: 16px;
    .cardItem {
      flex: 1;
      .card-header {
        font-weight: 600;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
      }
      .cardBox {
        position: relative;
        .deviceState_list {
          display: flex;
          flex-direction: column;
          position: absolute;
          top: 80px;
          right: 10px;
          .deviceState_item {
            display: flex;
            margin-bottom: 4px;
            .deviceState_itemName {
              width: 45px;
            }
          }
        }
        .deviceState_nav {
          position: absolute;
          top: 10px;
          right: 10px;
          font-weight: 600;
          font-size: 30px;
          color: #2b2e3f;
        }
      }
      .chartstate_chart {
        height: 380px;
      }
    }
    .cardItem:nth-child(2) {
      margin: 0 16px;
    }
  }
  .listDatas {
    margin-top: 16px;
    padding: 29px 24px;
    background-color: #fff;
    .listNav {
      display: flex;
      .listItem {
        display: flex;
        align-items: center;
        margin-right: 15px;
        .nav {
          margin-right: 15px;
        }
        .btn {
          margin-left: 15px;
        }
      }
    }
    .list {
      margin-top: 16px;
      :deep(.el-pagination) {
        margin: 10px;
        justify-content: end;
      }
    }
  }
}
.tableHome {
  height: 380px;
  :deep(.is-leaf) {
    background: #fafafa;
  }
  :deep(.el-table__cell) {
    padding: 10px 0;
  }
}
.statusnormal {
  width: 42px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  border-radius: 6px;
  border: 1px solid #00c851;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 100%);
  font-size: 13px;
  color: #00c851;
  margin: 0 auto;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 200, 81, 0.15);
  transition: all 0.3s ease;
}

.statusnormal:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 200, 81, 0.25);
}

.statusabnormal {
  width: 42px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  border-radius: 6px;
  border: 1px solid #ff4444;
  background: linear-gradient(135deg, #ffe8e8 0%, #fff0f0 100%);
  font-size: 13px;
  color: #ff4444;
  margin: 0 auto;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(255, 68, 68, 0.15);
  transition: all 0.3s ease;
}

.statusabnormal:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 68, 68, 0.25);
}

.statusoffline {
  width: 42px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  border-radius: 6px;
  border: 1px solid #9e9e9e;
  background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
  font-size: 13px;
  color: #9e9e9e;
  margin: 0 auto;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(158, 158, 158, 0.15);
  transition: all 0.3s ease;
}

.statusoffline:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(158, 158, 158, 0.25);
}

// 媒体查询更具不同的高度来适配不同分辨率的屏幕
// @media only screen and (max-height: 1080px) {
//   .Cabinet {
//     height: 450px;
//   }
// }

// @media only screen and (max-height: 919px) {
//   .Cabinet {
//     height: 408px;
//   }
// }
</style>


