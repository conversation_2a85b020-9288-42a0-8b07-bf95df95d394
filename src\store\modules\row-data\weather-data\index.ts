import { SetupStoreId } from '@/enum';
import { subscribe, unsubscribe } from '@/api/ws';
import { useSetWsData } from '@/store/ws';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs, watch } from 'vue';

const CHANNEL_WEATHER_DATA = '/topic/jsInfo/realtimedata/TCP-DTZ-AWS';
let wsWeatherData = {};
let timer:any;

export const useWeatherData = defineStore(
	SetupStoreId.WeatherData,
	() => {
		// states
		const data = reactive({
			wsWeatherData
		});

		// getters

		// actions
		const wsStore:any = useSetWsData();

		// ws气象数据
		watch(() => wsStore.websocketData[CHANNEL_WEATHER_DATA], (val) => {
		  data.wsWeatherData = val;
		});

    const init = () => {
      subscribe(CHANNEL_WEATHER_DATA);
      timer = setInterval(() => {
        data.wsWeatherData = {
          Temperature: '26°C',
          Humidity: '67%',
          WSpeed: '2.4m/s',
          WindDir: '东南风',
          Rainfall: '0mm',
          AtmosPress: '998.0hPa',
        };
      }, 1000);
    }

    const dispose = () => {
      unsubscribe(CHANNEL_WEATHER_DATA);

      clearInterval(timer);
    }

		return {
			...toRefs(data),
      init,
      dispose,
		};
	}
);
