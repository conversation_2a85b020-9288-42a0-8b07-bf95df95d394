<template>
  <!-- 对话框组件 -->
  <el-dialog
    v-model="dialogVisible"
    title="设置门限"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <!-- 表单组件 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="threshold-form"
    >
      <!-- 参数类型选择 -->
      <el-form-item label="参数类型" prop="cutoffType">
        <el-select v-model="formData.cutoffType" placeholder="请选择参数类型">
          <el-option :value="0" label="场强" />
          <el-option :value="1" label="授时偏差" />
        </el-select>
      </el-form-item>
      <!-- 最小值输入 -->
      <el-form-item label="最小值" prop="minValue">
        <el-input-number
          style="width: 240px"
          v-model="formData.minValue"
          :precision="2"
          :step="0.1"
          placeholder="请输入最小值"
        />
      </el-form-item>
      <!-- 最大值输入 -->
      <el-form-item label="最大值" prop="maxValue">
        <el-input-number
          style="width: 240px"
          v-model="formData.maxValue"
          :precision="2"
          :step="0.1"
          placeholder="请输入最大值"
        />
      </el-form-item>
    </el-form>
    <!-- 对话框底部按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import apiAjax from "@/api/index";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "success"]);

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 表单引用
const formRef = ref();
// 加载状态
const loading = ref(false);

// 表单数据
const formData = reactive({
  cutoffType: 0, // 默认选择场强
  minValue: null,
  maxValue: null,
  timeSeconds: Math.floor(Date.now() / 1000),
  timestamp: new Date().toISOString(),
  id: "",
});

// 存储所有门限数据
const thresholdData = ref([]);

// 监听参数类型变化
watch(
  () => formData.cutoffType,
  (newType) => {
    const data = thresholdData.value.find(
      (item) =>
        (newType === 0 && item.cutoffType === "FieldStrength") ||
        (newType === 1 && item.cutoffType === "Tser")
    );

    if (data) {
      formData.minValue = data.minValue;
      formData.maxValue = data.maxValue;
      formData.timeSeconds = data.timeSeconds;
      formData.timestamp = new Date(data.timestamp).toISOString();
      formData.id = data.id;
    }
  }
);

watch(
  () => dialogVisible.value,
  async (newVisible) => {
    if (newVisible) {
      const res = await apiAjax.post("/api/jnx/dataProcess/getCutoffParam");
      if (res) {
        thresholdData.value = res;
        // 初始化场强数据
        const fieldStrengthData = res.find(
          (item) => item.cutoffType === "FieldStrength"
        );
        if (fieldStrengthData) {
          formData.minValue = fieldStrengthData.minValue;
          formData.maxValue = fieldStrengthData.maxValue;
          formData.timeSeconds = fieldStrengthData.timeSeconds;
          formData.timestamp = new Date(
            fieldStrengthData.timestamp
          ).toISOString();
          formData.id = fieldStrengthData.id;
        }
      }
    }
  }
);
// 表单验证规则
const rules = {
  cutoffType: [
    { required: true, message: "请选择参数类型", trigger: "change" },
  ],
  minValue: [{ required: true, message: "请输入最小值", trigger: "blur" }],
  maxValue: [
    { required: true, message: "请输入最大值", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value <= formData.minValue) {
          callback(new Error("最大值必须大于最小值"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        // 更新时间戳
        formData.timeSeconds = Math.floor(Date.now() / 1000);
        formData.timestamp = new Date().toISOString();
        await apiAjax.post("api/jnx/dataProcess/setCutoffParam", [formData]);
        ElMessage.success("设置成功");
        emit("success");
        handleClose();
      } catch (error) {
        console.error("设置门限失败:", error);
        ElMessage.error("设置失败，请重试");
      } finally {
        loading.value = false;
      }
    }
  });
};

// 暴露方法给父组件
defineExpose({
  handleClose,
});
</script>

<style lang="scss" scoped>
.threshold-form {
  padding: 20px;

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
