_jsload2 &&
  _jsload2(
    'buslinesearch',
    'z.extend(Yd.prototype,{Sd:function(){for(var a=0,b=this.Pa.length;a<b;a++){var c=this.Pa[a];this[c.method].apply(this,c.arguments)}delete this.Pa},pv:function(a,b,c){a=S.Eb(a);return B.vp+"?c="+a.lng+","+a.lat+"&i="+b+","+encodeURIComponent(c)+"&s="+encodeURIComponent("tpl:LinesQuery")},getBusList:function(a){var b=this;this.Mg(this.Ad,function(c){c?rd.ab(function(a,c){b.GQ(a,c)},{qt:b.TP,c:c,wd:a},{vb:a}):(this.xc=new ai({vb:a,city:"",ni:"",Dh:""}),b.<PERSON>(5),b.<PERSON>a(<PERSON><PERSON><PERSON>,this.xc))})},GQ:function(a,b){this.clearResults(); var c=a.result;if(!a.content||0!=c.error||c.type!=this.VP)this.xc=new ai({vb:b.vb,city:a.current_city.name,ni:"",Dh:a.current_city.code}),this.Ta(7),this.Fa(W.Fu,this.xc);else{for(var c=a.content,e=[],f=0,g=c.length;f<g;f++)e.push({name:c[f].name,hk:c[f].uid,Dh:a.current_city.code,Rj:f,TA:b.vb,Pi:c[f].poiType||2});this.xc=new ai({vb:b.vb,city:a.current_city.name,ni:this.pv(ab.Be(a.current_city.geo,q).point,a.current_city.code,b.vb),WM:e,Dh:a.current_city.code});this.Ta(0);this.Fa(W.Fu,this.xc);this.hU(); this.gT(a.current_city.geo,a.current_city.level,a.current_city.code)}},hU:function(){if(this.j.la.Ma&&this.j.la.Ma.appendChild&&this.xc&&0<this.xc.$s()){var a=L("div",{style:"font:12px "+G.fontFamily+";background:#fff"});a.id="divResult"+this.aa;for(var b=this.xc.$s(),c=[],e=0;e<b;e++){var f=this.xc.MD(e).name;c.push(\'<dl style="margin:3px 3px"><dt><span style="cursor:pointer" onclick=Instance(\\\'\'+this.aa+"\')._selectBusListItem("+e+")><img id=imgBLIcon"+e+" src="+Yd.zv+\' style="border:none" /></span>&nbsp;&nbsp;<a style="color:blue" href="javascript:void(0)" onclick=Instance(\\\'\'+ this.aa+"\')._selectBusListItem("+e+")>"+f+"</a></dt><dd id=ddBLInfo"+e+\' style="display:none;margin:2px 0px"></dd></dl>\')}b="";this.xc.moreResultsUrl&&(b=b+"<div style=\'color:#7777cc;background:#e5ecf9;overflow:hidden;padding:2px;text-align:right\'>"+("<a style=\'color:#7777cc\' href=\'"+this.xc.moreResultsUrl+"\' target=\'_blank\'>\\u5230\\u767e\\u5ea6\\u5730\\u56fe\\u67e5\\u770b&#187;</a>"),b+="&nbsp;</div>");a.innerHTML=c.join("")+b;this.j.la.Ma.appendChild(a);this.Fa(W.vG,a)}},gT:function(a,b,c){var e=this.j.la.map; e&&(a=ab.Be(a,q).point,this.pB?this.pB!=c&&(e.Fd(a,b),this.pB=c):(e.Fd(a,b),this.pB=c))},UU:function(a){if(this.xc)for(var b=0,c=this.xc.$s();b<c;b++){var e=z.Fc("ddBLInfo"+b),f=z.Fc("imgBLIcon"+b);b==a?"none"==e.style.display?(e.style.display="block",f.src=Yd.YS):(e.style.display="none",f.src=Yd.zv):(e.style.display="none",f.src=Yd.zv)}},_selectBusListItem:function(a,b){if(!b){var c=z.Fc("ddBLInfo"+a),e=z.Fc("imgBLIcon"+a);if("block"==c.style.display){c.style.display="none";e.src=Yd.zv;return}}if(this.xc&& this.xc[a]&&0<this.xc[a].cp())this.Gc=this.xc[a],this.Ta(0),this.Fa(W.Eu,this.Gc),this.ZI(a),this.YI(a);else{var f=this,c=this.xc.MD(a);rd.ab(function(b,c){f.FQ(b,c,a)},{qt:f.SP,c:c.Dh,uid:c.hk},{name:c.name,Pi:c.Pi})}},clearResults:function(){delete this.xc;delete this.Gc;delete this.Si;delete this.T1;this.Hj();this.j.la.Ma&&(this.j.la.Ma.innerHTML="")},getBusLine:function(a){a&&("object"==typeof a&&a.hk&&""!=a.hk&&"undefined"!=typeof a.Dh&&""!=a.Dh.toString()&&"undefined"!=typeof a.Rj&&""!=a.Rj.toString()&& "undefined"!=typeof a.TA&&""!=a.TA.toString())&&(this.xc&&0<this.xc.$s()&&a.Dh==this.xc.Dh&&a.TA==this.xc.keyword)&&this._selectBusListItem(a.Rj,q)},FQ:function(a,b,c){var e=a.result;if(!a.content||!a.content[0]||0!=e.error||e.type!=this.UP)this.Gc=new bi({name:b.name}),this.Ta(7),this.Fa(W.Eu,this.Gc);else{var a=a.content[0],f=e="",g="",i={},k=[],e=a.startTime,f=a.endTime,g=a.company;a.geo&&(i=ab.Be(a.geo,q),i=new Gc(i.ja));if(a.stations)for(var m=0,n=a.stations.length;m<n;m++)if(a.stations[m].geo){var o= ab.Be(a.stations[m].geo,q);k.push({name:a.stations[m].name,position:o.point,hk:a.stations[m].uid})}this.Gc=new bi({name:b.name,startTime:e,fX:f,gW:g,OZ:i,HO:k,Pi:b.Pi});this.Ta(0);this.Fa(W.Eu,this.Gc);this.xc[c]=this.Gc;this.ZI(c);this.YI(c)}},ZI:function(a){if(this.j.la.Ma&&this.j.la.Ma.appendChild&&this.Gc&&0<this.Gc.cp()){this.UU(a);var b=z.Fc("ddBLInfo"+a);if(b){var c=[];c.push(\'<table style="width:100%;background:#CDCDCD;font:12px \'+G.fontFamily+\'" cellspacing="1" cellpadding="1" ><tbody>\'); c.push(\'<tr><td style="width:95px;line-height:22px;padding:0px 8px;text-align:left;vertical-align:top;background:#F4F4F4" >\\u9996\\u672b\\u8f66\\u65f6\\u95f4</th><td  style="background:#FFFFFF;line-height:22px;padding:0px 8px" >\'+this.Gc.startTime+"-"+this.Gc.endTime+"</td></tr>");c.push(\'<tr><td style="width:95px;line-height:22px;padding:0px 8px;text-align:left;vertical-align:top;background:#F4F4F4" >\\u6240\\u5c5e\\u516c\\u53f8</th><td  style="background:#FFFFFF;line-height:22px;padding:0px 8px" >\'+this.Gc.company+ "</td></tr>");c.push("</tbody></table>");4==this.Gc.Pi?c.push(\'<p style="margin:2px 0px;font:12px \'+G.fontFamily+\';">\\u6cbf\\u7ebf\\u5730\\u94c1\\u7ad9:</p>\'):c.push(\'<p style="margin:2px 0px;font:12px \'+G.fontFamily+\';">\\u6cbf\\u7ebf\\u516c\\u4ea4\\u7ad9\\u70b9:</p>\');c.push(\'<table style="width:100%;font:12px \'+G.fontFamily+\';" ><tbody>\');for(var e=0,f=this.Gc.cp();e<f;e++)c.push(\'<tr><td style="width:20px">\'+(e+1)+"</th><td><a id=aStop_"+a+"_"+e+\' style="color:blue" href="javascript:void(0)" onclick=Instance(\\\'\'+ this.aa+"\')._selectBusStop("+a+","+e+")>"+this.Gc.ux(e).name+"</a></td></tr>");c.push("</tbody></table>");b.innerHTML=c.join("")}a=z.Fc("divResult"+this.aa);this.Fa(W.uG,a)}},YI:function(a){if(this.j.la.map&&this.Gc&&0<this.Gc.cp()){this.Hj();V.Co(this.j.la.map,this.Gc.Ue());var b=this.Gc.fp();this.Fa(W.Gu,b);var c=this;this.Si=[];for(var e=0,f=this.Gc.cp();e<f;e++){var g=this.Gc.ux(e).position,i=this.Gc.ux(e).name,g=V.sV(this.j.la.map,g,i);(function(b,e,f){b.addEventListener("click",function(g){var i= ["<div style=\'font:12px "+G.fontFamily+"\'>"];i.push("<div style=\'margin:10px 1em 24px 0\'><b>"+e+"</b></div>");i.push("</div>");var v=new tc(i.join(""),{title:"",height:0,width:220,margin:[10,10,20,10]});v.addEventListener("open",function(){var b=z.Fc("aStop_"+a+"_"+f);b&&(b.style.backgroundColor="#cccccc")});v.addEventListener("close",function(){var b=z.Fc("aStop_"+a+"_"+f);b&&(b.style.backgroundColor="#ffffff")});i=c.j.la.map;i.K.tD?qe.tM(v,i.getCurrentCity().code,{panoInstance:i.P,lngLat:g.target.ga(), titleTip:e,type:"busline"},function(){b.pc(v)}):b.pc(v)})})(g,i,e);g.LB=i;this.Si.push(g)}this.Fa(W.bq,this.Si);this.j.la.Zg&&this.j.la.map.yh(b.Ue(),{margins:[5,5,5,5]})}},_selectBusStop:function(a,b){if(this.j.la.map&&this.Si&&0<this.Si.length){var c=this.Si[b];if(c.LB){var e=["<div style=\'font:12px "+G.fontFamily+"\'>"];e.push("<div style=\'margin:10px 1em 24px 0\'><b>"+c.LB+"</b></div>");e.push("</div>");var f=new tc(e.join(""),{title:"",height:0,width:220,margin:[10,10,20,10]});f.addEventListener("open", function(){var c=z.Fc("aStop_"+a+"_"+b);c&&(c.style.backgroundColor="#cccccc")});f.addEventListener("close",function(){var c=z.Fc("aStop_"+a+"_"+b);c&&(c.style.backgroundColor="#ffffff")});e=this.j.la.map;e.K.tD?qe.tM(f,e.getCurrentCity().code,{panoInstance:e.P,lngLat:c.ga(),titleTip:c.LB,type:"busline"},function(){c.pc(f)}):c.pc(f)}}else{this.PN&&(this.PN.style.backgroundColor="#ffffff");if(e=z.Fc("aStop_"+a+"_"+b))e.style.backgroundColor="#cccccc";this.PN=e}},Hj:function(){this.j.la.map&&this.j.la.map.yK()}, Ta:function(a){"number"==typeof a?this.Me=a:delete this.Me}});function ai(a){this.keyword=a.vb||"";this.city=a.city;this.moreResultsUrl=a.ni;this.XA=a.WM&&a.WM.slice(0)||[];this.Dh=a.Dh}z.extend(ai.prototype,{MD:function(a){if(this.XA[a])return this.XA[a]},$s:function(){return this.XA.length}});var ci=ai.prototype;T(ci,{getNumBusList:ci.$s,getBusListItem:ci.MD});function bi(a){this.name=a.name||"";this.startTime=a.startTime||"";this.endTime=a.fX||"";this.company=a.gW||"";this.$j=a.OZ||{};this.NB=a.HO&&a.HO.slice(0)||[];this.Pi=a.Pi||2}z.extend(bi.prototype,{ux:function(a){if(this.NB[a])return this.NB[a]},cp:function(){return this.NB.length},fp:w("$j"),Ue:function(){if(this.$j.Ue())return this.$j.Ue()}});var di=bi.prototype;T(di,{getNumBusStations:di.cp,getBusStation:di.ux,getPath:di.Ue,getPolyline:di.fp}); '
  )
