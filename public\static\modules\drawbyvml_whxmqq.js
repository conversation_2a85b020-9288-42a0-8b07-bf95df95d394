_jsload2&&_jsload2('drawbyvml', 'function Eg(a){this.B=a;this.dV={strokeweight:"weight",strokecolor:"color",fillcolor:"color",strokeopacity:"opacity",fillopacity:"opacity",strokestyle:"dashstyle"};this.Mb="vml"}Eg.zK={orange:"#ffa500"};Eg.prototype=new B.jz;var Fg=Eg.prototype; Fg.setAttribute=function(a,b,c){a&&(0==b.indexOf("stroke")?a=a.getElementsByTagName("stroke")[0]:0==b.indexOf("fill")&&(a=a.getElementsByTagName("fill")[0]),0<b.indexOf("color")&&(c?(a.on=q,Eg.zK[c]&&(c=Eg.zK[c])):a.on=t),"strokestyle"==b&&(c="solid"==c?"solid":"4 2 1 2"),"strokeweight"==b&&(c+="px"),a[this.Wq(b)]=c||"")};Fg.Wq=function(a){return this.dV[a]||a};Fg.Ao=function(){return Ab(this.B.Tf().Et,\'<v:shape style="behavior:url(#default#VML);z-index:1;width:1px;height:1px;position:absolute;left:0;top:0;"coordsize="1,1" coordorigin="0,0" filled="t" fillcolor="white"><v:stroke style="behavior:url(#default#VML);" endcap="round" oned="true" /><v:fill style="behavior:url(#default#VML)" /></v:shape>\')}; Fg.ke=function(a,b){this.setAttribute(a,"path",this.tA(b));6==z.ca.ia&&(a.style.display="none",a.style.display="")};Fg.tA=function(a){if(0==a.length)return"";var b=[];z.mc.Fb(a,function(a){if(!(2>a.length)){b.push("m "+a[0].x+" "+a[0].y+" l");for(var e=1,f=a.length;e<f;e++)b.push(a[e].x),b.push(a[e].y);b.push("e")}});return b.join(" ")||" "};B.bQ=Eg; ');