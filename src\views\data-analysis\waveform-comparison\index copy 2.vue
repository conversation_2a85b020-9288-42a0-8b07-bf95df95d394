<template>
  <div class="con">
    <el-card class="card">
      <template #header>
        <span class="card-header">波形参数1</span>
      </template>
      <lineChart
        ref="lineChart1"
        class="chartArea"
        :data="waveformComparisonoBj.lineChart1Obj.lineChart"
      />
      <el-table :data="tableDataAll.tableData1 || []" border>
        <el-table-column property="data1" align="center" label="接收机一" />
        <el-table-column property="data2" align="center" label="接收机二" />
        <el-table-column property="data3" align="center" label="接收机三" />
      </el-table>
    </el-card>
    <el-card class="card">
      <template #header>
        <span class="card-header">波形参数2</span>
      </template>
      <lineChart
        ref="lineChart2"
        class="chartArea"
        :data="waveformComparisonoBj.lineChart2Obj.lineChart"
      />
      <el-table
        style="margin-top: 6px"
        :data="tableDataAll.tableData2 || []"
        border
      >
        <el-table-column property="data4" align="center" label="接收机四" />
        <el-table-column property="data5" align="center" label="接收机五" />
        <el-table-column property="data6" align="center" label="接收机六" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import {
  reactive,
  onMounted,
  ref,
  onBeforeUnmount,
  getCurrentInstance,
  onUnmounted,
  nextTick,
} from "vue";
import { useWaveformComparison } from "@/store/modules/data-analysis/waveform-comparison";
import { chartColors } from "@/constants/chart";
import apiAjax from "@/api/index";
import {  initScocket,subscribe, unsubscribe } from "@/api/ws";
const { proxy } = getCurrentInstance();
const lineChart1 = ref(null); // 第一个的数据
const lineChart2 = ref(null); // 第二个的数据
const setSeries = (key) => {
  let series = [];
  Object.keys(waveformComparisonoBj[key].seriesList).forEach((k) => {
    series.push({
      name: "接收机" + k.slice(-1),
      type: "line",
      data: waveformComparisonoBj[key].seriesList[k],
      showSymbol: false,
    });
  });
  return series;
};
// 初始化图标的数据
const setChartData = () => {
  return {
    title: {
      text: `单位：ns`,
      left: "left",
      top: "-5",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "time",
      axisLabel: {
        interval: 0, // 显示所有标签
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false, // 隐藏 y 轴轴线
      },
      splitLine: {
        show: false, // 隐藏 y 轴网格线
      },
      // min: -5,
      // max: 5,
    },
    legend: {
      bottom: -4,
      itemHeight: 4,
      icon: "rect",
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: "#2B2E3F",
        lineHeight: 20,
        fontStyle: "normal",
      },
    },
    grid: {
      left: "40px",
      right: "10px",
      top: "40px",
      bottom: "50px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [],
  };
};

//所以数据在的地方
const waveformComparisonoBj = reactive({
  lineChart1Obj: {
    lineChart: setChartData(),
    seriesList: {
      channel1: [],
      channel2: [],
      channel3: [],
    },
    list: [],
  },
  lineChart2Obj: {
    lineChart: setChartData(),
    seriesList: {
      channel4: [],
      channel5: [],
      channel6: [],
    },
    list: [],
  },
});

const tableDataAll = reactive({
  tableData1: [], // 第一个 表格
  tableData2: [], // 第二个 表格
});

const getLodeData = async () => {
  let data = await apiAjax.get(
    `/api/jnx/data/findHistory?startTime=${proxy.configure.getTimestampTwoMinutesAgo(
      45
    )}&endTime=${proxy.configure.getUtcDate()}&dataType=modbus_real&value=1&twSize=10&page=1&pageNum=300`
  );
  if (data) {
    data = data.reverse();
    data.forEach((item) => {
      setChartDataItems(item);
    });
  }

  // 先去渲染一手
  setChartDomd();
  await initScocket();
  // 在去获取 他的实时数据
  nextTick(() => {
    subscribe("/topic/jsInfo/modbus/TCP-DTZ-MODBUS", setDataItems);
  });
};
onMounted(() => {
  getLodeData();
});
onUnmounted(() => {
  unsubscribe("/topic/jsInfo/modbus/TCP-DTZ-MODBUS");
});
const setDataItems = (datas) => {
  const newData = JSON.parse(datas.body);
  setChartDataItems(newData);
  setChartDomd();
  setTableData(newData);
};
// 设置表格
const setTableData = (newData) => {};

const setChartDataItems = (newData) => {
  const dateTime = newData.Date; // 预计算时间戳
  const date = new Date(dateTime);
  Object.keys(waveformComparisonoBj).forEach((key) => {
    Object.keys(waveformComparisonoBj[key].seriesList).forEach((k) => {
      if (waveformComparisonoBj[key].seriesList[k].length > 2700) {
        waveformComparisonoBj[key].seriesList[k].shift();
      }
      waveformComparisonoBj[key].seriesList[k].push({
        name: date.toString(),
        value: [date, newData.data[k]],
      });
      // waveformComparisonoBj[key].list.push(newData.data[k]);
    });
  });
  tableDataAll.tableData1 = [
    {
      data1: newData.data.channel1,
      data2: newData.data.channel2,
      data3: newData.data.channel3,
    },
  ];
  tableDataAll.tableData2 = [
    {
      data4: newData.data.channel4,
      data5: newData.data.channel5,
      data6: newData.data.channel6,
    },
  ];
};
// 渲染页面
const setChartDomd = () => {
  lineChart1.value.getIns().setOption({
    series: setSeries("lineChart1Obj"),
  });
  lineChart2.value.getIns().setOption({
    series: setSeries("lineChart2Obj"),
  });
};

// 计算最大最小值
const setMaxMin = (key) => {
  let max = Math.max(waveformComparisonoBj[key].list);
  let min = Math.min(waveformComparisonoBj[key].list);
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) * 0.1;
    min = min - (max - min) * 0.1;
  }
  return {
    max: Math.ceil(max * 1000) / 1000,
    min: Math.ceil(min * 1000) / 1000,
  };
};
</script>

<style lang="scss" scoped>
.con {
  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
  }

  .card {
    margin-bottom: 16px;
    height: 470px;
    background: #ffffff;
    border-radius: 2px;
    .chartArea {
      width: 100%;
      height: 282px;
    }
  }

  .card-header {
    font-weight: 600;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }

  :deep(.el-tabs) {
    .el-tabs__header {
      height: 56px;
      padding: 12px;
      background: #fff;
      border-radius: 2px;
    }

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__item.is-top.is-active {
      background: #ecf2fe;
      border-radius: 3px;
    }

    .el-tabs__item {
      width: 96px;
      height: 32px;
      padding: 0 4px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  :deep(.el-table) {
    margin-top: 10px;
    --el-table-border-color: #e8e8e8;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 8px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__cell {
      border-right: none;
    }

    .el-table__header {
      height: 46px;

      .cell {
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }

  .custom-tabs-label {
    display: flex;
    align-items: center;
  }

  .custom-tabs-label span {
    margin-left: 4px;
  }
}
:deep(.el-card__header) {
  padding: 8px 15px;
}
</style>
