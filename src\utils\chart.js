import * as echarts from 'echarts';
export const usabilityChart = (value) => {
  return {
    backgroundColor: 'transparent',
    title: {
      text: `${value}%`,
      subtext: '',
      left: 'center',
      top: 'center', //top待调整
      textStyle: {
        color: '#fff',
        fontSize: 20,
        fontFamily: 'DINAlternate-Bold',
      },
      subtextStyle: {
        color: '#ff',
        fontSize: 8,
        fontFamily: 'PingFangSC-Regular',
        top: 'center'
      },
      itemGap: 0 //主副标题间距
    },
    xAxis: {
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      axisLine: {
        show: false
      }

    },
    yAxis: {
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      axisLine: {
        show: false
      }
    },
    series: [
      // 内圆
      {
        type: 'pie',
        radius: ['0', '65%'],
        center: ['50%', '50%'],
        z: 0,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(85, 166, 255, 0.8)' // 天空蓝
              },
              {
                offset: 0.5,
                color: 'rgba(52, 125, 235, 0.6)' // 中等蓝
              },
              {
                offset: 1,
                color: 'rgba(25, 99, 201, 0.7)' // 深蓝
              }
            ]),
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          },
        },
        label: {
          normal: {
            position: "center",
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: [100],
      },
      // 进度圈
      {
        type: 'pie',
        clockWise: true,
        radius: ["80%", "80%"],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [{
          value: value,
          itemStyle: {
            normal: {
              borderWidth: 10,
              borderColor: {
                colorStops: [{
                  offset: 0,
                  color: '#55A6FF' // 天空蓝
                }, {
                  offset: 1,
                  color: '#1963C9' // 深蓝
                }]
              },
              color: { // 完成的圆环的颜色
                colorStops: [{
                  offset: 0,
                  color: '#55A6FF' // 天空蓝
                }, {
                  offset: 1,
                  color: '#1963C9' // 深蓝
                }]
              },
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
            },
          }
        },
        {
          name: '',
          value: 100 - value,
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0,
            }
          },
        }
        ]
      },
    ]
  }
}
export const continuityChart = (value) => {
  return {
    backgroundColor: 'transparent',
    title: {
        text: `${value}%`,
        subtext: '',
        left: 'center',
        top: 'center',//top待调整
        textStyle: {
            color: '#000',
            fontSize: 23,
            fontFamily: 'DINAlternate-Bold'
        },
        subtextStyle: {
            color: '#000',
            fontSize: 15,
            fontFamily: 'PingFangSC-Regular',
            top: 'center'
        },
        itemGap: -4//主副标题间距
    },
    series: [{
        name: 'pie1',
        type: 'pie',
        clockWise: true,
        radius: ["80%", "80%"],
        itemStyle: {
            normal: {
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                }
            }
        },
        hoverAnimation: false,
        data: [{
            value: value,
            name: 'completed',
            itemStyle: {
                normal: {
                    borderWidth: 10,
                    borderColor: {
                        colorStops: [{
                            offset: 0,
                            color: '#55A6FF' // 天空蓝
                        }, {
                            offset: 1,
                            color: '#1963C9' // 深蓝
                        }]
                    },
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#55A6FF' // 天空蓝
                        }, {
                            offset: 1,
                            color: '#1963C9' // 深蓝
                        }]
                    },
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    }
                }
            }
        }, {
            name: 'gap',
            value: 100 - value,
            itemStyle: {
                normal: {
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    },
                    color: 'rgba(0, 0, 0, 0)',
                    borderColor: 'rgba(0, 0, 0, 0)',
                    borderWidth: 0
                }
            }
        }]
    }]
}
}
