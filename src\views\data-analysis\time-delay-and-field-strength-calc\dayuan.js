function dms2degrees(dms) {
  return dms[0] + dms[1] / 60 + dms[2] / 3600;
}

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}

export function dayuan(p1, p2) {
  const earthRadius = 6378.14; // 地球赤道半径，单位：km
  const earthEllipse = 1 / 298.257; // 地球椭率

  // 将经纬度转换为弧度
  const lon1 = deg2rad(p1.x);
  const lat1 = deg2rad(p1.y);
  const lon2 = deg2rad(p2.x);
  const lat2 = deg2rad(p2.y);

  // 计算视地球为球形时收发两地的大圆距离 D0
  const l = (lon1 - lon2) / 2;
  const D0 = Math.acos(Math.sin(lat1) * Math.sin(lat2) + Math.cos(lat1) * Math.cos(lat2) * Math.cos(2 * l)) * earthRadius;

  const S = Math.pow(Math.sin(D0 / (2 * earthRadius)), 2);
  const C = Math.pow(Math.cos(D0 / (2 * earthRadius)), 2);
  const R = Math.sqrt(S * C) * D0 / (2 * earthRadius);

  // 计算授时台至接收天线的大圆距离 D
  const latMean = (lat1 + lat2) / 2;
  const latMin = (lat1 - lat2) / 2;

  const D = D0 + earthEllipse * D0 * Math.pow(Math.sin(latMean), 2) * Math.pow(Math.cos(latMin), 2) * ((3 * R - 1) / (2 * C))
    - earthEllipse * D0 * Math.pow(Math.cos(latMean), 2) * Math.pow(Math.sin(latMin), 2) * ((3 * R + 1) / (2 * S));

  return D + "KM";
}
