import { ElNotification } from 'element-plus'

let ws = null;
let isConnected = false;
let subscribeMap = {};
let heartbeatInterval = null;
let idleTimer = null; // 新增空闲定时器
const IDLE_TIMEOUT = 30000; // 30秒空闲时间

// 打开WebSocket连接
export async function initScocket() {
  new Promise((resolve, reject) => {
    if (isConnected) {
      console.log('WebSocket已经连接');
      resolve();
      return;
    }
    const wsUrl = import.meta.env.VITE_SERVICE_BASE_URL_WS;
    // sock = new SockJS(wsUrl + ':8088/ws')
    ws = new WebSocket(wsUrl);
    ws.onopen = () => {
      isConnected = true;
      console.log('WebSocket连接成功');
      subscribeAll();
      startHeartbeat();
      resetIdleTimer();
      resolve(true);
    };
    ws.onmessage = (event) => {
      try {

        const message = event.data;
        if (message === 'pang') {
          handleHeartbeat();
        } else {
          handleMessage(JSON.parse(message));
        }
      } catch (e) {
        console.error('消息解析失败:', e);
      }
      resetIdleTimer();
    };
    ws.onerror = (error) => {
      ElNotification.closeAll();
      console.error('WebSocket错误:', error);
      isConnected = false;
      ElNotification({
        title: '错误',
        message: 'WebSocket连接发生错误，请检查服务是否启动',
        type: 'error',
        duration: 0,
      });
      reject(false)
    };

    ws.onclose = () => {

      isConnected = false;
      console.log('WebSocket连接关闭');
      stopHeartbeat();
      clearIdleTimer();
      resolve(false)
    };
  })
}
// 处理接收到的消息
function handleMessage(message) {
  const handler = subscribeMap[message.path];
  if (handler) {
    // let data = { ...message?.body }
    // delete data.data;
    handler(message?.body.data || {});
  } else {
    console.log('未找到对应的订阅处理函数', message);
  }
}

// 处理心跳消息
function handleHeartbeat() {
  // console.log('收到心跳包');
}

// 添加订阅
export function subscribe(path, onmessage) {
  subscribeMap[path] = onmessage;
  if (isConnected) {
    ws.send(JSON.stringify({ msgType: 'SUB', path }));
    console.log('添加订阅', path);
  }
}

// 取消订阅
export function unsubscribe(path) {
  if (!subscribeMap[path]) return;
  console.log('取消订阅', path);
  delete subscribeMap[path];
  if (isConnected) {
    ws.send(JSON.stringify({ msgType: 'UNSUB', path }));
  }
}

// 给所有订阅map中的记录建立订阅
export function subscribeAll() {
  Object.keys(subscribeMap).forEach(path => subscribe(path, subscribeMap[path]));
}

// 给所有订阅map中的记录取消订阅
export function unsubscribeAll() {
  Object.keys(subscribeMap).forEach(path => unsubscribe(path));
}

// 发送消息
export function send(destination, message) {
  if (!ws || !isConnected) return;
  ws.send(JSON.stringify({ destination, message }));
  resetIdleTimer();
}

// 控制台输出相关日志信息
export function log() {
  console.log(`
    isConnected: ${isConnected},
    subscribeMap: ${JSON.stringify(subscribeMap)},
  `);
}

// 销毁当前所有内容
export function dispose() {
  if (ws) {
    ws.close();
    ws = null;
  }
  subscribeMap = {};
  isConnected = false;
  console.log("WebSocket连接已关闭");
  stopHeartbeat();
  clearIdleTimer();
}

// 启动心跳定时器
function startHeartbeat() {
  heartbeatInterval = setInterval(() => {
    if (isConnected) {
      ws.send('ping');
    }
  }, 5000);
}

// 停止心跳定时器
function stopHeartbeat() {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
}

// 重置空闲定时器
function resetIdleTimer() {
  if (idleTimer) {
    clearTimeout(idleTimer);
  }
  idleTimer = setTimeout(() => {
    if (isConnected) {
      ws.send('ping');
    }
  }, IDLE_TIMEOUT);
}

// 清除空闲定时器
function clearIdleTimer() {
  if (idleTimer) {
    clearTimeout(idleTimer);
    idleTimer = null;
  }
}

