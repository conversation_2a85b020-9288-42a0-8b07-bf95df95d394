{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["./src/*"], "~/*": ["./*"]}, "resolveJsonModule": true, "types": ["vite/client", "node", "unplugin-icons/types/vue", "naive-ui/volar"], "strict": true, "strictNullChecks": true, "noUnusedLocals": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true}, "include": ["./**/*.ts", "./**/*.tsx", "./**/*.vue", "src/store/modules/data-manage/index.js", "src/store/modules/alarm-list/index.js", "src/store/modules/data-analysis/synthesize/index.js", "src/store/modules/data-analysis/synthesize/index.js", "src/store/modules/row-data/time-diff-data/index.js"], "exclude": ["node_modules", "dist"]}