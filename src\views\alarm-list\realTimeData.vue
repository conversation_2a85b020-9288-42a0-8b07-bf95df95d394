<template>
  <div class="history">
    <div class="historyBox">
      <div class="oneTable">
        <el-table
          class="table"
          :data="store.realTimeAlarmData.tableData || []"
          border
          :height="740"
          @cell-dblclick="onDoBtnClickinfo"
          :row-class-name="tableRowClassName"
        >
          <el-table-column property="timestamp" label="告警开始时刻">
            <template #default="scope">
              {{ proxy.configure.formatTimestamp(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column property="alarmFrom" label="发波台">
            <template #default="scope">
              {{
                store.getValueLabel(
                  scope.row.alarmFrom,
                  store.historyAlarmData.fromParams.alarmFromList.data,
                )
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmField" label="告警类型">
            <template #default="scope">
              {{
                store.getValueLabel(
                  scope.row.alarmField,
                  store.historyAlarmData.fromParams.alarmLeveleList.data,
                )
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmType" label="告警等级">
            <template #default="scope">
              <span :style="{ color: getAlarmLevelColor(scope.row.alarmType) }">
                {{ getAlarmLevelText(scope.row.alarmType) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="操作">
            <template #default="scope">
              <el-button link type="primary" @click="onShowData(scope.row)">
                关闭
              </el-button>
            </template>
          </el-table-column>
          <template #empty>
            <div class="table-empty">
              <img :src="emptyIcon" />
              <div class="text">暂无数据</div>
            </div>
          </template>
        </el-table>
      </div>
    </div>
    <div class="historyChart">
      <div class="towTable">
        <el-table border :height="405" :data="infoClick.data || []">
          <el-table-column property="timeSeconds" width="160" label="告警开始时刻">
            <template #default="scope">
              {{
                proxy.configure.formatTimestamp(scope.row?.timeSeconds * 1000)
              }}</template
            >
          </el-table-column>
          <el-table-column property="alarmType" width="100" label="告警等级">
            <template #default="scope">
              <span :style="{ color: getAlarmLevelColor(scope.row.alarmType) }">
                {{ getAlarmLevelText(scope.row.alarmType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column property="alarmContent" width="220px" label="告警描述">
            <template #default="scope">
              {{
                store.getValueLabel(
                  scope.row?.alarmContent?.split("-")[0],
                  store.historyAlarmData.fromParams.alarmFromList.data,
                ) +
                "-" +
                store.getValueLabel(
                  scope.row?.alarmContent?.split("-")[1],
                  store.historyAlarmData.fromParams.alarmLeveleList.data,
                )
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmValue" label="异常值" >
            <template #default="scope">
              <div style="white-space: pre-line;">{{ setalarmValue(scope.row.alarmValue) }}</div>
            </template>
          </el-table-column>
          <template #empty>
            <div class="table-empty">
              <img :src="emptyIcon" />
              <div class="text">暂无数据</div>
            </div>
          </template>
        </el-table>
      </div>
      <el-card class="chartBox pie">
        <template #header>
          <div class="card-header">
            <span>当日告警比例</span>
          </div>
        </template>
        <lineChart class="lineChart" ref="pie" />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import lineChart from "@/components/lineChart/lineCharts.vue";
import emptyIcon from "@/assets/imgs/no_data.png";
import { useAlarmInfo } from "@/store/modules/alarm-list";
import {
  onMounted,
  reactive,
  ref,
  getCurrentInstance,
  watch,
  onUnmounted,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import apiAjax from "@/api/index";
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
import { getAlarmLevelText, getAlarmLevelColor } from "@/utils/alarmUtils";

const { proxy } = getCurrentInstance();
const store = useAlarmInfo();
const trend = ref();
const pie = ref();
const infoClick = ref({
  data: [],
  info: false,
  alarmContent: "",
}); // 是否被点击了列表
const setalarmValue=(value)=>{
  // 判断如果有逗号（中文或英文）就换行
  if (!value) return value;
  const newValue = value.replace(/[，,]/g, "\n");
  return newValue;
}


onMounted(async () => {
  await store.search("realTimeAlarmData");
  await initScocket();
  subscribe("/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO", (val) => {
    let data = JSON.parse(val);
    if (data.showDownType) {
      // 删除操作
      store.setDateList(data, "delete");
      // 如果删除的是当前选中的数据
      if (data.showDownType === infoClick.value.alarmContent) {
        infoClick.value.info = false;
        // 删除后自动选中第一条数据
        if (store.realTimeAlarmData.tableData[0]) {
          cellClick(store.realTimeAlarmData.tableData[0]);
        } else {
          clearDetailInfo();
        }
      }
    } else {
      // 新增操作
      store.setDateList(data, "insert");
      // 如果没有手动选中任何数据，则自动选中第一条
      if (!infoClick.value.info) {
        cellClick(data);
      }
    }
  });

  await store.initCharData("realTimeAlarmData", {
    trend: trend.value,
    pie: pie.value,
  });
  initData();
});

watch(
  () => store.initType,
  () => {
    infoClick.value.data = [];
    infoClick.value.info = false;
    infoClick.value.alarmContent = "";
  },
);
//初始化左边的表格
const initData = async () => {
  cellClick(store.realTimeAlarmData.tableData[0]);
};

const tableRowClassName = ({ row, rowIndex }) => {
  if (infoClick.value.info) {
    if (row.alarmContent === infoClick.value.alarmContent) {
      return "warning-row";
    }
  } else {
    if (rowIndex === 0) {
      return "warning-row";
    }
  }
  return "";
};

const onDoBtnClickinfo = async (row) => {
  infoClick.value.info = true;
  await cellClick(row);
};

const cellClick = async (val) => {
  try {
    if (infoClick.value.alarmContent) {
      unsubscribe(
        "/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO/" +
          infoClick.value.alarmContent,
      );
    }

    let newData = await apiAjax.post(
      "/api/jnx/dataAlarm/getAlarmInfoProcess?id=" +
        val.id +
        "&isRealData=true",
      {},
    );

    infoClick.value.data = Array.isArray(newData)
      ? newData.reverse()
      : newData
        ? [newData].reverse()
        : [];
    infoClick.value.alarmContent = val.alarmContent;
    onDoBtnClick();
  } catch (error) {
    console.error("获取告警详情失败:", error);
    // ElMessage({
    //   type: "error",
    //   message: "获取告警详情失败",
    // });
    infoClick.value.data = [];
    infoClick.value.info = false;
  }
};

const onDoBtnClick = async (row) => {
  let time = "";
  subscribe(
    "/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO/" +
      infoClick.value.alarmContent,
    (val) => {
      let data = JSON.parse(val);
      let timeSeconds = data.timeSeconds;
      if (time !== timeSeconds) {
        infoClick.value.data.unshift({ ...data });
        time = timeSeconds;
      }
    },
  );
};

const onShowData = async (val) => {
  await ElMessageBox.confirm("是否关闭该条告警", "", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      let data = await apiAjax.post(
        `/api/jnx/dataAlarm/processAlarm?ids=` + val.id,
      );
      console.log(data);
      if (data) {
        ElMessage({
          type: "error",
          message: "操作失败，请重试！",
        });
      } else {
        ElMessage({
          type: "success",
          message: "操作成功！",
        });
        store.search(store.initType);
      }
    })
    .catch(() => {});
};

// 默认选中的数据
const defaultSelect = () => {};

// 添加一个清空详情信息的方法
const clearDetailInfo = () => {
  infoClick.value.data = [];
  infoClick.value.info = false;
  infoClick.value.alarmContent = "";
  if (infoClick.value.alarmContent) {
    unsubscribe(
      "/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO/" +
        infoClick.value.alarmContent,
    );
  }
};

// 修改 watch
watch(
  () => store.realTimeAlarmData.tableData,
  (newVal) => {
    if (newVal.length === 0) {
      clearDetailInfo();
    }
  },
  { deep: true },
);

onUnmounted(() => {
  // 取消主告警信息的订阅
  unsubscribe("/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO");

  // 如果有选中的告警，取消其详情订阅
  if (infoClick.value.alarmContent) {
    unsubscribe(
      "/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO/" +
        infoClick.value.alarmContent,
    );
  }
});

defineExpose({
  initData,
});
</script>

<style lang="scss" scoped>
.history {
  margin-top: 18px;
  width: 100%;
  // height: 800px;
  display: flex;
  .historyBox {
    display: flex;
    width: 100%;
    flex: 1.2;
  }
}

.oneTable {
  width: 100%;
  margin-right: 16px;
}
.towTable {
  flex: 1;
}
.table {
  max-height: 650px;
}
.paginations {
  border-left: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
  display: flex;
  width: 100%;
  background: #fff;
  justify-content: end;
  padding: 10px;
}
.historyChart {
  flex: 1;
  display: flex;
  flex-direction: column;
  .chartBox {
    flex: 1;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    .card-header {
      height: 40px;
      padding: 10px 0px 8px 12px;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }
    :deep(.el-card__header) {
      padding: 0 !important;
    }
    :deep(.el-card__body) {
      flex: 1;
    }
    .lineChart {
      flex: 1;
      height: 214px;
    }
  }
  .pie {
    margin-top: 8px;
  }
}
:deep(.warning-row) {
  background-color: #f0f9eb;
}
.table-empty {
  img {
    margin: 0 auto;
  }
}
</style>
