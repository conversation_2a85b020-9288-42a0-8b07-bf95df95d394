_jsload2&&_jsload2('drawbycanvas', 'function Gg(a){this.B=a;this.NQ={strokeweight:"lineWidth",strokecolor:"strokeStyle",fillcolor:"fillStyle",strokeopacity:"globalAlpha",fillopacity:"globalAlpha"};this.Mb="canvas"}Gg.prototype=new B.jz;var Hg=Gg.prototype;Hg.Ao=function(){if(!this.canvas||this.canvas&&!zb(this.canvas)){var a=this.canvas=L("canvas");this.B.Tf().Et.appendChild(a);a.style.position="absolute";a=a.getContext("2d");a.lineCap="round";a.lineJoin="round";a.save();this.zU(this.canvas)}return this.canvas}; Hg.ke=function(a,b,c){if(a&&0!==b[0].length){var e=a.getContext("2d");this.sa(a);var f=parseInt(a.style.top),g=parseInt(a.style.left);e.beginPath();z.mc.Fb(b,function(a){if(0!==a.length){e.moveTo(a[0].x-g,a[0].y-f);for(var b=1,c=a.length;b<c;b++)e.lineTo(a[b].x-g,a[b].y-f)}});this.FR(a,c);"dashed"===c.strokeStyle&&this.HR(e,b,g,f,c)}}; Hg.HR=function(a,b,c,e,f){var g=this;a.beginPath();z.mc.Fb(b,function(b){if(0!==b.length){for(var k=[],m=0,n=b.length;m<n;m++)k.push({x:b[m].x-c,y:b[m].y-e});a.strokeStyle=f.strokeColor||"#3a6bdb";g.GR(a,k,{lineWidth:f.rc||5,interval:2*f.rc||10,lineLength:2*f.rc||10,strokeStyle:g.JS(a.strokeStyle,f.td)})}})}; Hg.GR=function(a,b,c){for(var c=c||{},e=c.Wx||10,f=c.lineWidth||5,g=c.m4||10,c=c.strokeStyle||"#3a6bdb",i=b.length-1,k=0,m=g+e,n=0,o=0;o<i;o++)var p=b[o].x,v=b[o].y,x=b[o+1].x,y=b[o+1].y,n=n+Math.sqrt((x-p)*(x-p)+(y-v)*(y-v));if(n<g)a.strokeStyle=c,a.lineWidth=f,a.lineJoin="round",a.lineCap="round",a.beginPath(),a.moveTo(b[0].x,b[0].y),a.lineTo(b[i].x,b[i].y),a.stroke();else for(n=0;n<i;n++){var p=b[n].x,v=b[n].y,x=b[n+1].x,y=b[n+1].y,o=Math.sqrt((x-p)*(x-p)+(y-v)*(y-v))+k+g,A=m-Math.abs(k);if(k<= m){var E=parseInt(o/m),C=e*(x-p)/o,F=e*(y-v)/o;xMove=g*(x-p)/o;yMove=g*(y-v)/o;startX=p+A*(x-p)/o;startY=v+A*(y-v)/o;tailX=p+(g-Math.abs(k))*(x-p)/o;tailY=v+(g-Math.abs(k))*(y-v)/o;a.beginPath();a.strokeStyle=c;a.lineJoin="round";a.lineCap="round";a.lineWidth=f;D?a.moveTo(D,I):a.moveTo(p,v);0>=k&&a.lineTo(p,v);a.lineTo(tailX,tailY);a.moveTo(startX,startY);D=0;for(I=2*E;D<I;D++)if(D%2)startX+=C,startY+=F,a.moveTo(startX,startY);else{startX+=xMove;startY+=yMove;if(0>(x-startX)*xMove)break;a.lineTo(startX, startY)}a.stroke()}var k=parseInt(o-m*E)-g,D=x-Math.abs(k)*(x-p)/o,I=y-Math.abs(k)*(y-v)/o;n===i-1&&0<k&&(a.beginPath(),a.strokeStyle=c,p=x-k*(x-p)/o,leftY=y-k*(y-v)/o,k<=g?(a.moveTo(p,leftY),a.lineTo(x,y)):(a.moveTo(p,leftY),a.moveTo(p+xMove,leftY+yMove)),a.stroke())}}; Hg.JS=function(a,b){if(4===a.length)var c=parseInt(a.substr(1,1)+a.substr(1,1),16),e=parseInt(a.substr(2,1)+a.substr(2,1),16),f=parseInt(a.substr(3,1)+a.substr(3,1),16);else c=parseInt(a.substr(1,2),16),e=parseInt(a.substr(3,2),16),f=parseInt(a.substr(5,2),16);return c="rgba("+c+","+e+","+f+","+b+")"};Hg.setAttribute=function(a,b,c){if(a){var e=a.getContext("2d"),f=this.Wq(b);try{e[f]=c}catch(g){}a.setAttribute("_"+b,c||"");e.save()}};Hg.Wq=function(a){return this.NQ[a]||a}; Hg.zU=function(a){var b=this.B,c=b.K.lx,e=b.width+2*c,f=b.height+2*c,g=-b.offsetX-c,b=-b.offsetY-c,c=a.getContext("2d"),i={strokeStyle:c.strokeStyle,fillStyle:c.fillStyle,globalAlpha:c.globalAlpha,lineWidth:c.lineWidth,lineCap:"round",lineJoin:"round"};a.setAttribute("width",e);a.setAttribute("height",f);for(var k in i)c[k]=i[k];c.save();a=a.style;a.top=b+"px";a.left=g+"px"};Hg.XQ=function(a){a.clearRect(0,0,9999,9999)}; Hg.FR=function(a,b){var c=a.getContext("2d");c.globalAlpha=a.getAttribute("_fillopacity");0!==c.globalAlpha&&a.getAttribute("_fillcolor")&&c.fill();c.globalAlpha=a.getAttribute("_strokeopacity");0!==c.globalAlpha&&(a.getAttribute("_strokecolor")&&"dashed"!==b.strokeStyle)&&c.stroke()};Hg.sa=function(a){var b=this.B,c=b.K.lx,e=-b.offsetX-c,a=a.style;a.top=-b.offsetY-c+"px";a.left=e+"px"};B.zP=Gg; ');