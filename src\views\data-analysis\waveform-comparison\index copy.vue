<template>
  <div class="con">
    <el-tabs v-model="activeName">
      <el-tab-pane name="tab1">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Files /></el-icon>
            <span>接收机1</span>
          </span>
        </template>

        <el-card class="card">
          <template #header>
            <span class="card-header">接收机1-波形参数1</span>
          </template>
          <lineChart
            ref="lineChart1"
            class="chartArea"
            :data="options1.chartData"
          />
          <el-table :data="options1.tableData || []" border style="width: 100%">
            <el-table-column
              v-for="col in options1.tableColumn || []"
              :key="col.name"
              v-bind="col"
            />
          </el-table>
        </el-card>
        <el-card class="card">
          <template #header>
            <span class="card-header">接收机1-波形参数2</span>
          </template>
          <lineChart
            ref="lineChart2"
            class="chartArea"
            :data="options2.chartData"
          />
          <el-table :data="options2.tableData || []" border style="width: 100%">
            <el-table-column
              v-for="col in options2.tableColumn || []"
              :key="col.name"
              v-bind="col"
            />
          </el-table>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="接收机2" name="tab2">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Files /></el-icon>
            <span>接收机2</span>
          </span>
        </template>

        <el-card class="card">
          <template #header>
            <span class="card-header">接收机2-波形参数1</span>
          </template>
          <lineChart
            ref="lineChart3"
            class="chartArea"
            :data="options3.chartData"
          />
          <el-table :data="options3.tableData || []" border style="width: 100%">
            <el-table-column
              v-for="col in options3.tableColumn || []"
              :key="col.name"
              v-bind="col"
            />
          </el-table>
        </el-card>
        <el-card class="card">
          <template #header>
            <span class="card-header">接收机2-波形参数2</span>
          </template>
          <lineChart
            ref="lineChart4"
            class="chartArea"
            :data="options4.chartData"
          />
          <el-table :data="options4.tableData || []" border style="width: 100%">
            <el-table-column
              v-for="col in options4.tableColumn || []"
              :key="col.name"
              v-bind="col"
            />
          </el-table>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="接收机3" name="tab3">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Files /></el-icon>
            <span>接收机3</span>
          </span>
        </template>

        <el-card class="card">
          <template #header>
            <span class="card-header">接收机3-波形参数1</span>
          </template>
          <lineChart
            ref="lineChart5"
            class="chartArea"
            :data="options5.chartData"
          />
          <el-table :data="options5.tableData || []" border style="width: 100%">
            <el-table-column
              v-for="col in options5.tableColumn || []"
              :key="col.name"
              v-bind="col"
            />
          </el-table>
        </el-card>
        <el-card class="card">
          <template #header>
            <span class="card-header">接收机3-波形参数2</span>
          </template>
          <lineChart
            ref="lineChart6"
            class="chartArea"
            :data="options6.chartData"
          />
          <el-table :data="options6.tableData || []" border style="width: 100%">
            <el-table-column
              v-for="col in options6.tableColumn || []"
              :key="col.name"
              v-bind="col"
            />
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { reactive, onMounted, ref, onBeforeUnmount } from "vue";
import { useWaveformComparison } from "@/store/modules/data-analysis/waveform-comparison";
import { chartColors } from "@/constants/chart";

const store = useWaveformComparison();

const activeName = ref("tab1");

const lineChart1 = ref(null);
const lineChart2 = ref(null);
const lineChart3 = ref(null);
const lineChart4 = ref(null);
const lineChart5 = ref(null);
const lineChart6 = ref(null);

let oneSecond = 1000;

const getRandomWaveList = (isNow = true, n, v) => {
  let now = isNow ? new Date() : n;
  let val = v + Math.random() * 100 - 50;
  return {
    name: now.toString(),
    value: [now, Math.round(val)],
  };
};

let value1 = Math.random() * 300;
let value2 = Math.random() * 300;
let value3 = Math.random() * 300;
let value4 = Math.random() * 300;
let value5 = Math.random() * 300;
let value6 = Math.random() * 300;

let currentTime = 0;
const getRandomData = (value) => {
  let data = [];
  for (var i = 0, len = 200; i < len; i++) {
    data.push(
      getRandomWaveList(
        false,
        new Date(currentTime - oneSecond * (len - i - 1)),
        value
      )
    );
  }
  return data;
};

const getRandomTableRowData = (name = "") => {
  return {
    name,
    currentValue: 30 + Math.floor(Math.random() * 350),
    max: 30 + Math.floor(Math.random() * 350),
    min: 30 + Math.floor(Math.random() * 350),
    avg: 30 + Math.floor(Math.random() * 350),
    a1p: 30 + Math.floor(Math.random() * 350),
    a1m: 30 + Math.floor(Math.random() * 350),
    a2p: 30 + Math.floor(Math.random() * 350),
    a2m: 30 + Math.floor(Math.random() * 350),
    a3p: 30 + Math.floor(Math.random() * 350),
    a3m: 30 + Math.floor(Math.random() * 350),
    a4p: 30 + Math.floor(Math.random() * 350),
    a4m: 30 + Math.floor(Math.random() * 350),
    a5p: 30 + Math.floor(Math.random() * 350),
    a5m: 30 + Math.floor(Math.random() * 350),
    a6p: 30 + Math.floor(Math.random() * 350),
    a6m: 30 + Math.floor(Math.random() * 350),
    a7p: 30 + Math.floor(Math.random() * 350),
    a7m: 30 + Math.floor(Math.random() * 350),
    // sd: 30 + Math.floor(Math.random() * 350),
    // rms: 30 + Math.floor(Math.random() * 350),
    p95quantile: 30 + Math.floor(Math.random() * 350),
  };
};

const getOptions = (value) => {
  currentTime = new Date().getTime();
  return {
    chartData: {
      xAxis: {
        type: "time",
      },
      yAxis: {
        type: "value",
      },
      legend: {
        bottom: 10,
        itemStyle: { opacity: 0 },
        textStyle: {
          fontWeight: 400,
          fontSize: 12,
          color: "#2B2E3F",
          lineHeight: 20,
          fontStyle: "normal",
        },
        data: ["采集波形", "标准波形"],
      },
      grid: {
        top: "10px",
        left: "50px",
        right: "30px",
        bottom: "60px",
      },
      tooltip: {
        trigger: "axis",
      },
      series: [
        {
          data: getRandomData(value),
          type: "line",
          name: "采集波形",
          showSymbol: false,
          itemStyle: { color: chartColors[0] },
        },
        {
          data: getRandomData(value),
          type: "line",
          name: "标准波形",
          showSymbol: false,
          itemStyle: { color: chartColors[1] },
        },
      ],
    },
    tableColumn: [
      { prop: "name", label: "统计值", align: "left", width: 130 },
      { prop: "currentValue", label: "当前值", align: "left", width: 130 },
      { prop: "a1p", label: "a1+", align: "left", width: 100 },
      { prop: "a1m", label: "a1-", align: "left", width: 100 },
      { prop: "a2p", label: "a2+", align: "left", width: 100 },
      { prop: "a2m", label: "a2-", align: "left", width: 100 },
      { prop: "a3p", label: "a3+", align: "left", width: 100 },
      { prop: "a3m", label: "a3-", align: "left", width: 100 },
      { prop: "a4p", label: "a4+", align: "left", width: 100 },
      { prop: "a4m", label: "a4-", align: "left", width: 100 },
      { prop: "a5p", label: "a5+", align: "left", width: 100 },
      { prop: "a5m", label: "a5-", align: "left", width: 100 },
      { prop: "a6p", label: "a6+", align: "left", width: 100 },
      { prop: "a6m", label: "a6-", align: "left", width: 100 },
      { prop: "a7p", label: "a7+", align: "left", width: 100 },
      { prop: "a7m", label: "a7-", align: "left", width: 100 },
      { prop: "max", label: "α", align: "left", width: 100 },
      { prop: "min", label: "ω", align: "left", width: 100 },
      { prop: "avg", label: "φ", align: "left", width: 100 },
      // { prop: "p95quantile", label: "预报偏差", align: "left" },
    ],
    tableData: [getRandomTableRowData("偏差")],
  };
};
const options1 = reactive(getOptions(value1));

console.log("options1", options1);

const options2 = reactive(getOptions(value2));
const options3 = reactive(getOptions(value3));
const options4 = reactive(getOptions(value4));
const options5 = reactive(getOptions(value5));
const options6 = reactive(getOptions(value6));
console.log("getOptions", options1);
let timer = null;

const refreshData = () => {
  const currentTime = new Date();
  let series = options1.chartData.series;
  for (let i = 0; i < series.length; i++) {
    series[i].data.shift();
    series[i].data.push(getRandomWaveList(false, currentTime, value1));
  }
  options1.tableData[0] = getRandomTableRowData("偏差");

  series = options2.chartData.series;
  for (let i = 0; i < series.length; i++) {
    series[i].data.shift();
    series[i].data.push(getRandomWaveList(false, currentTime, value2));
  }
  options2.tableData[0] = getRandomTableRowData("偏差");

  series = options3.chartData.series;
  for (let i = 0; i < series.length; i++) {
    series[i].data.shift();
    series[i].data.push(getRandomWaveList(false, currentTime, value3));
  }
  options3.tableData[0] = getRandomTableRowData("偏差");

  series = options4.chartData.series;
  for (let i = 0; i < series.length; i++) {
    series[i].data.shift();
    series[i].data.push(getRandomWaveList(false, currentTime, value4));
  }
  options4.tableData[0] = getRandomTableRowData("偏差");

  series = options5.chartData.series;
  for (let i = 0; i < series.length; i++) {
    series[i].data.shift();
    series[i].data.push(getRandomWaveList(false, currentTime, value5));
  }
  options5.tableData[0] = getRandomTableRowData("偏差");

  series = options6.chartData.series;
  for (let i = 0; i < series.length; i++) {
    series[i].data.shift();
    series[i].data.push(getRandomWaveList(false, currentTime, value6));
  }
  options6.tableData[0] = getRandomTableRowData("偏差");
};

onMounted(() => {
  if (timer) clearInterval(timer);
  timer = setInterval(refreshData, 1000);
});

onBeforeUnmount(() => {
  if (timer) clearInterval(timer);
  timer = null;
});
</script>

<style lang="scss" scoped>
.con {
  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
  }

  .card {
    margin-bottom: 16px;
    height: 425px;
    background: #ffffff;
    border-radius: 2px;

    .chartArea {
      width: 100%;
      height: 222px;
    }
  }

  .card-header {
    font-weight: 600;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }

  :deep(.el-tabs) {
    .el-tabs__header {
      height: 56px;
      padding: 12px;
      background: #fff;
      border-radius: 2px;
    }

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__item.is-top.is-active {
      background: #ecf2fe;
      border-radius: 3px;
    }

    .el-tabs__item {
      width: 96px;
      height: 32px;
      padding: 0 4px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  :deep(.el-table) {
    margin-top: 10px;
    --el-table-border-color: #e8e8e8;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 8px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__cell {
      border-right: none;
    }

    .el-table__header {
      height: 46px;

      .cell {
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }

  .custom-tabs-label {
    display: flex;
    align-items: center;
  }

  .custom-tabs-label span {
    margin-left: 4px;
  }
}
</style>
