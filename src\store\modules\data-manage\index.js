import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs } from 'vue';
import apiAjax from "@/api/index";
import configure from '@/utils/configure.js';

export const useDataManage = defineStore(
  SetupStoreId.DataManage,
  () => {
    // 状态
    const data = reactive({
      configObj: {
        Configuration: [],
        Receiver: [],
        getTransmitterList: []
      }
    });

    // 获取配置数据
    const gitConfigData = async () => {
      try {
        const [Configuration, Receiver, getTransmitterList] = await Promise.all([
          apiAjax.get("api/jnx/statistics/config/getManyChannelDeviceChannel"),  //获取多通道设备通道配置
          apiAjax.get("api/jnx/statistics/config/getReceiverChannel"), //获取接收机通道配置
          apiAjax.get("api/jnx/statistics/config/getTransmitterList") //获取发播台配置
        ]);

        data.configObj.Configuration = Configuration;
        data.configObj.Receiver = Receiver;
        data.configObj.getTransmitterList = getTransmitterList;

        setConfingObj();
      } catch (error) {
        console.error("Failed to fetch configuration data:", error);
      }
    };

    // 判断是否所有数组都已填充
    const areAllArraysFilled = (obj) => {
      return Object.values(obj).every(value => Array.isArray(value) && value.length > 0);
    };

    // 初始化
    const init = async () => {
      if (!areAllArraysFilled(data.configObj)) {
        await gitConfigData();
      }
      // 始终返回一个 Promise，这样 await 能正常工作
      return Promise.resolve(data.configObj);
    };

    // 设置配置对象
    const setConfingObj = () => {
      console.log(" data.configObj", data.configObj)
      data.configObj.Receiver = data.configObj.Receiver.map((i) => {
        const newItem = { ...i }; // 创建副本
        newItem.name = '接收机' + newItem.receiverName.slice(-1);
        newItem.channelMapList.forEach(item => {
          item.name = configure.thoroughfare(item.transmitterId);
        });
        return newItem;
      });
    };

    return {
      ...toRefs(data),
      gitConfigData,
      areAllArraysFilled,
      init
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  }
);
