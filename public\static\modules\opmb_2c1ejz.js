_jsload2&&_jsload2('opmb', 'var Vg=t,Wg=s; B.Ye(function(a){function b(){f&&(f=t,g=s,i||a.dispatchEvent(m))}function c(c){f=q;m=la(new P("ontouch"),c);g=new Q(c.changedTouches[0].clientX,c.changedTouches[0].clientY);var i=e("onclickex",c);setTimeout(function(){b();a.dispatchEvent(i)},a.K.RW);Vg||a.dispatchEvent(e("onclick",c))}function e(b,c){for(var e=new P(b),f=c.target,g=s,i=s;f&&f!==a.Wa;){if(f.aa){var k=z.lang.Nc(f.aa);k instanceof gb&&"canvas"!==f.nodeName.toLowerCase()&&(g=k);k instanceof tc&&(i=k)}f=f.offsetParent}for(var f=c.changedTouches[0].pageX, k=c.changedTouches[0].pageY,m=a.Wa;m&&m!=document.body;)f-=m.offsetLeft,k-=m.offsetTop,m=m.offsetParent;e.offsetX=f;e.offsetY=k;e.pixel=e.lb=new Q(e.offsetX,e.offsetY);e.point=e.point=a.xb(e.lb);e.overlay=e.fb=g;e.infoWindow=i;return e}a.K.RW=500;var f=t,g,i=t,k=t,m;z.M(a.La(),"touchmove",function(b){a.K.Wb&&b.preventDefault()});z.M(a.platform,"touchstart",function(b){Vg=t;Wg=setTimeout(function(){Vg=q;a.dispatchEvent(la(e("onlongpress",b),b))},1E3);a.K.Wb&&na(b);k=t;a.dispatchEvent(la(e("ontouchstart", b),b));a.K.Wb&&Cb(b)});z.M(a.platform,"touchmove",function(b){a.K.Wb&&na(b);k=q;a.dispatchEvent(la(e("ontouchmove",b),b))});z.M(a.platform,"touchend",function(m){clearTimeout(Wg);a.K.Wb&&Cb(m);k||(f?(i=q,Vg||a.dispatchEvent(e("onclick",m)),20>Fb(g,new Q(m.changedTouches[0].clientX,m.changedTouches[0].clientY))?(a.dispatchEvent(e("ondblclick",m)),a.dispatchEvent(la(new P("ondbltouch"),m))):a.dispatchEvent(la(e("ontouchend",m),m)),b(),i=t):c(m));a.dispatchEvent(la(e("ontouchend",m),m))});z.M(a.platform, "gesturestart",function(b){b.preventDefault();a.dispatchEvent(la(new P("ongesturestart"),b))});z.M(a.platform,"gesturechange",function(b){b.preventDefault();a.dispatchEvent(la(new P("ongesturechange"),b))});z.M(a.platform,"gestureend",function(b){b.preventDefault();a.dispatchEvent(la(new P("ongestureend"),b))})}); B.Ye(function(a){function b(a){a&&(D.WebkitTransform="");m=k=0;n=1;o[0].x=o[0].y=o[1].x=o[1].y=0;A=t}function c(){if(z.platform.Jm)if(/Nexus/.test(navigator.userAgent))a.K.Wb&&(D.left=a.offsetX+k+"px",D.top=a.offsetY+m+"px");else{if(a.K.Wb&&(D.left=a.offsetX+k*n+"px",D.top=a.offsetY+m*n+"px"),1!=n)D.WebkitTransform=a.K.Ns?"scale("+n+") ":""}else D.WebkitTransform=(a.K.Wb?"translate("+k*n+"px, "+m*n+"px) ":"")+(a.K.Ns?"scale("+n+") ":"")}function e(){var b=0,c=0;Xg&&(c=b=0);D.WebkitTransformOrigin= a.K.Wb?(o[0].x+o[1].x)/2-a.offsetX-b+"px "+((o[0].y+o[1].y)/2-a.offsetY-c)+"px":Math.round(a.width/2)+"px "+Math.round(a.height/2)+"px"}function f(b,c){var e=new P(b),f=g(c.changedTouches[0].pageX,c.changedTouches[0].pageY);e.offsetX=f.x;e.offsetY=f.y;e.lb=new Q(e.offsetX,e.offsetY);e.point=a.xb(e.lb);return e}function g(b,c){for(var e=a.Wa;e&&e!=document.body;)b-=e.offsetLeft,c-=e.offsetTop,e=e.offsetParent;return new Q(b,c)}function i(a){for(var b=[],c,e=0,f=a.touches.length;e<f;e++)c=a.touches[e], b.push({x:c.pageX,y:c.pageY});return b}var k=0,m=0,n=1,o=[{x:0,y:0},{x:0,y:0}],p=0,v=0,x=t,y=t,A=t,E=s,C,F,D=a.platform.style;a.addEventListener("touchstart",function(b){if(!(b.fb instanceof U)){C=i(b);var c=b.targetTouches.length;p+=c;2<p&&(p=2);var e=a.R;2==c&&(e.sI=q);e.ob&&e.ob.stop();1==p?(this.R.PU=k,this.R.QU=m,b=g(b.targetTouches[0].pageX,b.targetTouches[0].pageY),o[0].x=b.x,o[0].y=b.y):2==p&&(b=g(b.touches[c-1].pageX,b.touches[c-1].pageY),o[1].x=b.x,o[1].y=b.y)}});a.addEventListener("touchmove", function(b){if(4<=z.dK&&2<=b.changedTouches.length){F=i(b);var D=2==C.length&&2==F.length?Fb(F[0],F[1])/Fb(C[0],C[1]):1;0<Math.abs(1-D)&&(n=D,y=q,clearTimeout(Wg),e())}if(y&&!(2>b.changedTouches.length)){for(D=b.target;D&&D!=a.Wa;)D.Nx&&z.lang.Nc(D.Nx),D=D.offsetParent;for(var D=[],M=0;2>M;M++){for(var N=b.changedTouches[M].pageX,fa=b.changedTouches[M].pageY,pa=a.Wa;pa&&pa!=document.body;)N-=pa.offsetLeft,fa-=pa.offsetTop,pa=pa.offsetParent;D[M]={x:N,y:fa}}E=new Q((D[0].x+D[1].x)/2,(D[0].y+D[1].y)/ 2)}D=b.targetTouches.length;N=g(b.touches[0].pageX,b.touches[0].pageY);1==p&&2!=v&&(k+=N.x-o[0].x,m+=N.y-o[0].y,o[0].x=N.x,o[0].y=N.y,c(),A=q);if(2==p){var fa=o[0].x,pa=o[0].y,wa=o[1].x,Ma=o[1].y;if(A){for(N=0;N<D;N++)M=g(b.touches[N].pageX,b.touches[N].pageY),Fb(M,new Q(o[0].x,o[0].y))<Fb(M,new Q(o[1].x,o[1].y))?(o[0].x=M.x,o[0].y=M.y):(o[1].x=M.x,o[1].y=M.y);k+=(o[0].x-fa+o[1].x-wa)/2;m+=(o[0].y-pa+o[1].y-Ma)/2;c()}else if(M=new Q(o[0].x,o[0].y),1==D&&(30>Fb(M,N)?(o[0].x=N.x,o[0].y=N.y):(A=q,o[1].x= N.x,o[1].y=N.y)),2==D)fa=g(b.touches[1].pageX,b.touches[1].pageY),30>Fb(N,fa)?(o[0].x=fa.x,o[0].y=fa.y):(Fb(M,N)<Fb(M,fa)?(o[0].x=N.x,o[0].y=N.y,o[1].x=fa.x,o[1].y=fa.y):(o[1].x=N.x,o[1].y=N.y,o[0].x=fa.x,o[0].y=fa.y),A=q)}if(1==p&&2==v){for(N=0;N<D;N++)M=g(b.touches[N].pageX,b.touches[N].pageY),Fb(M,new Q(o[0].x,o[0].y))<Fb(M,new Q(o[1].x,o[1].y))?(k+=M.x-o[0].x,m+=M.y-o[0].y,o[0].x=M.x,o[0].y=M.y):(k+=M.x-o[1].x,m+=M.y-o[1].y,o[1].x=M.x,o[1].y=M.y);c()}1==p&&this.K.Wb&&(D=this.R,D.Yj||(D.Yj=q,b= f("ondragstart",b),a.dispatchEvent(b),a.dispatchEvent(new P("onmovestart")),D.ZC=b.lb,D.$C=bb(),x=q),D.Bt=bb(),a.dispatchEvent(new P("ondragging")))});a.addEventListener("touchend",function(c){e();v=p;this.R.sI?(p=c.targetTouches.length,delete this.R.sI):p--; -1==p&&(p=0);z.platform.Jm&&(p=0);if(0==p){if(y){var g=0,i=a.fa();a.Oc=a.Oa;this.K.Ns&&(g=Math.round(Math.log(n)/Math.log(2)),i=Math.max(Math.min(a.fa()+g,a.K.Yb),a.K.gc));var A=a.oa().dc(i),C=E;C||(C=new Q((o[0].x+o[1].x)/2,(o[0].y+o[1].y)/ 2));var D;D=C;var F=a.fc,Ma=a.oa().dc(a.Oc);D=new J(F.lng+Ma*(D.x-a.width/2),F.lat-Ma*(D.y-a.height/2));A=new J(D.lng+(a.width/2-C.x)*A,D.lat-(a.height/2-C.y)*A);A=S.Tb(A);A=a.$b(A);g=new Q(A.x-k*n/Math.pow(2,g),A.y-m*n/Math.pow(2,g));A=a.K.Wb?a.xb(g):a.Ka();if(g=a.hh())if(g=g.ga())g=a.$b(g,a.Oc),a.ik(a.width/2-g.x,a.height/2-g.y,a.xb(g,a.Oc),q);a.Fd(A,i)}if(k!=this.R.PU||m!=this.R.QU)x&&!y&&(c=f("ondragend",c),Yg(a,c,c.lb,{x:k,y:m}),x=t),y?b(q):b(t);y=a.R.Yj=t;v=0}});a.addEventListener("gesturechange", function(a){n=a.scale;y=q;clearTimeout(Wg);e()})}); function Yg(a,b,c,e){var f=a.platform.style;if(a.K.ox){var g=a.R,i=bb();if(140<i-g.Bt)setTimeout(function(){f.WebkitTransform=""},0),setTimeout(function(){a.Le(a.offsetX+e.x,a.offsetY+e.y)},0),window.setTimeout(function(){a.dispatchEvent(new P("onmoveend"));g.Yj=t},0),window.setTimeout(function(){a.dispatchEvent(b)},0);else{var k=g.ZC,m=[0<c.x-k.x?1:-1,0<c.y-k.y?1:-1],i=Fb(k,c)/((i-g.$C)/1E3)/2,n=i/1.6,o=0.5*n*i/1E3,p=Math.abs(k.x-c.x),v=0,x=0;0==Math.abs(k.y-c.y)?v=p:(c=Math.abs(k.x-c.x)/Math.abs(k.y- c.y),x=Math.round(Math.sqrt(o*o/(1+c*c))),v=Math.round(c*x));-1==m[0]&&(v=-v);-1==m[1]&&(x=-x);g.ob&&g.ob.stop();var y=i/1E3,A=a.offsetX,E=a.offsetY;g.ob=new tb({duration:n,Ic:25,kc:function(a){a=a*y/1.6;return y*a-0.8*a*a},va:function(b){b=b*3.2/(y*y);if(z.platform.Jm){f.left=a.offsetX+e.x+Math.round(b*v)+"px";f.top=a.offsetY+e.y+Math.round(b*x)+"px"}else f.WebkitTransform="translate("+(e.x+Math.round(b*v))+"px ,"+(e.y+Math.round(b*x))+"px)"},finish:function(){g.ob=s;setTimeout(function(){f.WebkitTransform= ""},0);setTimeout(function(){a.Le(A+e.x+Math.round(v),E+e.y+Math.round(x))},0);setTimeout(function(){a.dispatchEvent(new P("onmoveend"))},0);window.setTimeout(function(){a.dispatchEvent(b)},0)},Kt:function(b){g.ob=s;b=b*3.2/(y*y);setTimeout(function(){f.WebkitTransform=""},0);setTimeout(function(){a.Le(A+e.x+Math.round(b*v),E+e.y+Math.round(b*x))},0);setTimeout(function(){a.dispatchEvent(new P("onmoveend"))},0)}})}}else window.setTimeout(function(){f.WebkitTransform=""},0),window.setTimeout(function(){a.Le(a.offsetX+ e.x,a.offsetY+e.y)},0),window.setTimeout(function(){a.dispatchEvent(new P("onmoveend"))},0)}var Xg=-1<navigator.userAgent.indexOf("iPhone OS 5_")?q:t; ');