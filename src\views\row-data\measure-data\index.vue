<template>
  <div class="con">
    <div class="top-con">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="场强" name="fieldStrength">
          <div class="tabContent">
            <lineChart
              ref="fieldStrengthChart"
              class="chartArea"
              :data="measureData.fieldStrength.chartData"
            ></lineChart>
          </div>
        </el-tab-pane>
        <el-tab-pane label="频率偏差" name="frequencyTD">
          <div class="tabContent">
            <lineChart
              ref="frequencyTDChart"
              class="chartArea"
              :data="measureData.frequencyTD.chartData"
            ></lineChart>
          </div>
        </el-tab-pane>
        <el-tab-pane label="频率准确度" name="frequencyComparison">
          <div class="tabContent">
            <el-table
              :data="measureData.tableColumn || []"
              border
              max-height="2000"
              style="width: 100%; margin-top: 16px"
            >
              <el-table-column property="label" label="设备" />
              <el-table-column property="value" label="当前值" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import {
  onUnmounted,
  onMounted,
  reactive,
  ref,
  watch,
  getCurrentInstance,
  nextTick,
} from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import { initScocket, subscribe, unsubscribeAll } from "@/api/ws";
const { proxy } = getCurrentInstance();
const activeName = ref("fieldStrength");
// 初始化图标的数据
const setChartData = (unit, name) => {
  return {
    title: {
      text: `单位：${unit}`,
      left: "left",
      top: "15px",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "time",
      axisLabel: {
        interval: 0, // 显示所有标签
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false, // 隐藏 y 轴轴线
      },
      splitLine: {
        show: false, // 隐藏 y 轴网格线
      },
      min: 20.0,
      max: 20.2,
    },
    legend: {
      bottom: -4,
      itemHeight: 4,
      icon: "rect",
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: "#2B2E3F",
        lineHeight: 20,
        fontStyle: "normal",
      },
    },
    grid: {
      left: "60px",
      right: "35px",
      top: "60px",
      bottom: "50px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [
      {
        type: "line",
        data: [],
        name: name,
        showSymbol: false, // 默认情况下不显示标记
        symbolSize: 10, // 标记的大小
        itemStyle: {
          color: "#1d7bff",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(25, 120, 255, 0.2)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255, 255, 255, 0)", // 100% 处的颜色
              },
            ],
          },
          origin: "start", // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: "solid",
        },
      },
    ],
  };
};

const measureData = reactive({
  fieldStrength: {
    name: "场强",
    chartData: setChartData("dBμV/m", "场强"),
    chartDataList: [],
  },
  frequencyTD: {
    name: "频率偏差",
    chartData: setChartData("Hz", "频率偏差"),
    chartDataList: [],
  },
  tableColumn: [
    { property: "alnV1s", label: "1S阿伦方差", value: "" },
    { property: "alnV10s", label: "10S阿伦方差", value: "" },
    { property: "alnV100s", label: "100S阿伦方差", value: "" },
    { property: "alnV1000s", label: "1000S阿伦方差", value: "" },
    { property: "alnV10000s", label: "10000S阿伦方差", value: "" },
    { property: "alnV186400s", label: "86400S阿伦方差", value: "" },
  ],
});
const fieldStrengthChart = ref();
const frequencyTDChart = ref();

onMounted(async () => {
  await initScocket();
  subscribe("/topic/jsInfo/fc/FCR", setFcrList);
  subscribe("/topic/jsInfo/fc/FSR", setFcFsrChartData);
});
// 获取表单数据
const setFcrList = (data) => {
  console.log(data);
  const newData = data;
  Object.keys(newData).forEach((i) => {
    measureData.tableColumn.forEach((item) => {
      if (i == item.property) {
        item.value = newData[i];
      }
    });
  });
};
// 计算最大值最小值
const setMaxMin = (list) => {
  let max = Math.max(...list.map((i) => i.value[1]));
  let min = Math.min(...list.map((i) => i.value[1]));
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) * 0.1;
    min = min - (max - min) * 0.1;
  }
  let newMax = Math.ceil(max * 1000) / 1000;
  let newMin = Math.ceil(min * 1000) / 1000;
  return {
    max: newMax,
    min: newMin,
  };
};

// 场强的数据
const setFcFsrChartData = (data) => {
  console.log("data", data);
  const newData = data
  const dateTime = proxy.configure.setDate(newData.timeMark); // 预计算时间戳
  const date = new Date(dateTime);
  let fieldStrengthList = measureData.fieldStrength.chartDataList;
  let frequencyTDList = measureData.frequencyTD.chartDataList;
  fieldStrengthList.push({
    name: date.toString(),
    value: [date, newData.fieldStrength],
  });
  frequencyTDList.push({
    name: date.toString(),
    value: [date, newData.compFrequencyTD],
  });
  if (frequencyTDList.length > 1200) {
    fieldStrengthList.shift();
    frequencyTDList.shift();
  }

  fieldStrengthChart.value.setInfos();
  frequencyTDChart.value.setInfos();

  // measureData.fieldStrength.chartData.yAxis.max = setMaxMin(fieldStrengthList).max;
  // measureData.fieldStrength.chartData.yAxis.min = setMaxMin(fieldStrengthList).min;
  // measureData.frequencyTD.chartData.yAxis.max = setMaxMin(frequencyTDList).max;
  // measureData.frequencyTD.chartData.yAxis.min = setMaxMin(frequencyTDList).min;
  // measureData.fieldStrength.chartData.series[0].data = fieldStrengthList;
  // measureData.frequencyTD.chartData.series[0].data = frequencyTDList;

  nextTick(() => {
    fieldStrengthChart.value?.getIns().setOption({
      yAxis: setMaxMin(fieldStrengthList),
      series: [
        {
          data: fieldStrengthList,
        },
      ],
    });

    frequencyTDChart.value?.getIns().setOption({
      yAxis: setMaxMin(frequencyTDList),
      series: [
        {
          data: frequencyTDList,
        },
      ],
    });
  });

};

onUnmounted(() => {
  unsubscribeAll();
});
const handleClick = (tab, event) => {
  console.log(tab, event);
};
</script>

<style lang="scss" scoped>
.top-con {
  background: #fff;
  border-radius: 2px;
  height: 500px;
  padding: 8px 0;

  :deep(.el-tabs__header) {
    margin: 0 0 0 2px;
  }

  .tabContent {
    padding: 0 10px;
  }

  .chartArea {
    width: 100%;
    height: 420px;
  }

  .select {
    width: 162px;
    position: absolute;
    top: 8px;
    left: 30px;
    z-index: 1;
  }

  .select-label {
    position: absolute;
    top: 14px;
    left: 170px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    text-align: center;
    font-style: normal;
    z-index: 1;
  }

  .hide-select-label {
    display: none;
  }

  :deep(.el-tabs) {
    .el-tabs__item {
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      text-align: right;
      font-style: normal;
      padding: 12px 12px 14px 12px;
      height: 48px;
      margin-right: 16px;
    }

    .el-tabs__nav {
      padding: 0 10px;
    }
  }
}

.con {
  :deep(.el-table) {
    --el-table-border-color: #e7e7e7;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }
}
</style>
