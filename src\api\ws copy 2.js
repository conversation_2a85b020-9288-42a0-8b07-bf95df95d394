import '../../public/static/sockjs.js'
import '../../public/static/stomp.js'
import { useSetWsData } from "../store/ws";
import { ElNotification } from 'element-plus'
let sock, stompClient, wsData = null
let isConnected = false;
let subscribeMap = {};

// function topicNews(news) {
//   console.log("topicNews")
//   console.log("topicNews:" + news.body);
// }

// function topicTest(news) {
//   console.log("topictest")
//   console.log("topictest:" + news.body);
// }

const open = (title) => {
  ElNotification({
    title: title,
    message: '当前没有有效链接，请检查服务是否启动',
    type: 'warning',
    duration: 3000,
  })
}


export function init() {

  const wsUrl = import.meta.env.VITE_SERVICE_BASE_URL_WS;
  sock = new SockJS(wsUrl)
  stompClient = Stomp.over(sock)

  wsData = useSetWsData(); // 状态修改

  stompClient.connect({}, function () {
    isConnected = true;
    subscribeAll();
    // stompClient.subscribe('/topic/ferq', topicNews);
    // stompClient.subscribe('/topic/test', topicTest);
  }, function (error) {
    console.log('websocket error:', error);
    isConnected = false;
  });
}



/**
 * 添加订阅
 * @param {String} path 订阅路径字符串
 * @param {Function} [onmessage=null] 订阅消息接收回调函数，默认使用公共的
 */
export function subscribe(path, onmessage = null) {
  if (subscribeMap.hasOwnProperty(path) &&
    subscribeMap[path] !== null &&
    typeof subscribeMap[path] !== 'function') {
    return;
  }

  // 未连接成功时加入待订阅map
  if (!isConnected) {
    subscribeMap[path] = onmessage;
    return;
  }

  function onMessage(res) {
    let message;
    try {
      message = JSON.parse(res.body);
    } catch (e) {
      message = res.body;
    }
    wsData.websocketData[path] = message;
    console.log('websocket receive:', path, res);
  }
  // let timer = null
  // if (timer) { clearTimeout(timer) };
  // timer = setTimeout(() => {
  //   console.log("我等待了3秒了")
  //   open(path)
  // }, 3000);
  // 连接成功时直接建立订阅，并加入订阅map
  const subscription = stompClient.subscribe(path, onmessage || subscribeMap[path] || onMessage);
  subscribeMap[path] = subscription;
}

/**
 * 取消订阅
 * @param {String} path 要取消的订阅路径字符串
 */
export function unsubscribe(path) {
  ElNotification.closeAll()
  try {
    if (!subscribeMap.hasOwnProperty(path)) {
      return;
    }
    const subscription = subscribeMap[path];
    if (subscription) {
      subscription.unsubscribe();
    } else {
      return
    }
    delete subscribeMap[path];
    if (wsData.websocketData[path]) {
      delete wsData.websocketData[path];
    }
  }
  catch (e) {
    console.log(111, e)
  }

}

/**
 * 给所有订阅map中的记录建立订阅
 */
export function subscribeAll() {
  const paths = Object.keys(subscribeMap);
  paths.forEach(function (path) {
    subscribe(path);
  });
}

/**
 * 给所有订阅map中的记录取消订阅
 */
export function unsubscribeAll() {
  const paths = Object.keys(subscribeMap);
  paths.forEach(function (path) {
    unsubscribe(path);
  });
}

/**
 * 发送消息
 * @param {String} destination 订阅对应的path
 * @param {Object} header 消息头对象
 * @param {String} message 发送消息内容字符串
 */
export function send(destination, header = {}, message) {
  if (!stompClient || !isConnected) {
    return;
  }

  stompClient.send(destination, header, message);
}

/**
 * 控制台输出相关日志信息
 */
export function log() {
  console.log(`
    isConnected: ${isConnected},
    subscribeMap: ${subscribeMap},
  `);
}

/**
 * 销毁当前所有内容
 */
export function dispose() {
  if (!stompClient || !stompClient.connected) {
    subscribeMap = {};
    isConnected = false;
    return;
  }

  stompClient.disconnect(function () {
    unsubscribeAll();
    isConnected = false;
    console.log("disconnect stomp client.");
  });
}
