/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  "alarm-list": () => import("@/views/alarm-list/index.vue"),
  "asf-data": () => import("@/views/asf-data/index.vue"),
  "data-analysis_preprocessing": () => import("@/views/data-analysis/preprocessing/index.vue"),
  "data-analysis_synthesize": () => import("@/views/data-analysis/synthesize/index.vue"),
  "data-analysis_time-delay-and-field-strength-calc": () => import("@/views/data-analysis/time-delay-and-field-strength-calc/index.vue"),
  "data-analysis_waveform-comparison": () => import("@/views/data-analysis/waveform-comparison/index.vue"),
  "data-manage": () => import("@/views/data-manage/index.vue"),
  "data-statistics_analysis-data": () => import("@/views/data-statistics/analysis-data/index.vue"),
  "data-statistics_ori-data": () => import("@/views/data-statistics/ori-data/index.vue"),
  help: () => import("@/views/help/index.vue"),
  home: () => import("@/views/home/<USER>"),
  log: () => import("@/views/log/index.vue"),
  "params-manage": () => import("@/views/params-manage/index.vue"),
  "performance-assessment": () => import("@/views/performance-assessment/index.vue"),
  "remote-manage": () => import("@/views/remote-manage/index.vue"),
  "row-data_aggregate-data": () => import("@/views/row-data/aggregate-data/index.vue"),
  "row-data_external-data": () => import("@/views/row-data/external-data/index.vue"),
  "row-data_interfere-data": () => import("@/views/row-data/interfere-data/index.vue"),
  "row-data_measure-data": () => import("@/views/row-data/measure-data/index.vue"),
  "row-data_receive-data": () => import("@/views/row-data/receive-data/index.vue"),
  "row-data_time-diff-data": () => import("@/views/row-data/time-diff-data/index.vue"),
  "row-data_weather-data": () => import("@/views/row-data/weather-data/index.vue"),
  "work-condition": () => import("@/views/work-condition/index.vue"),
};
