import { ref, reactive } from "vue";
export let detectionInstrument = [
  {
    id: 1, title: "2024年07月16日00:00:01", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 2, title: "2024年07月16日00:00:02", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 3, title: "2024年07月16日00:00:03", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 4, title: "2024年07月16日00:00:04", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 5, title: "2024年07月16日00:00:05", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 6, title: "2024年07月16日00:00:06", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 7, title: "2024年07月16日00:00:07", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 8, title: "2024年07月16日00:00:08", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 9, title: "2024年07月16日00:00:09", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 10, title: "2024年07月16日00:00:10", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },

  {
    id: 11, title: "2024年07月16日00:00:11", dateDat: "#,0,0,0,NTP Up,Locked GPS", data: {
      "Client Tx (T1)": "1111",
      "Setver Rx (T2)": "222",
      "Setver Tx (T2)": "222",
      "Client Rx (T1)": "1111",
    }
  },
]
export let statistics = {
  frmHdr: { name: "样本个数", data: "53223" },
  curOfset: { name: "当前偏差", data: "11.0000ns" },
  fw: { name: "偏差范围", data: "11.0000-13.0000ns" }, //ntpTiMin  // ntpTiMax
  ntpTiAvg: { name: "平均偏差", data: "11.8661ns" },
  ntpTiStd: { name: "标准偏差", data: "0.6543ns" },
  ntpSenCur: { name: "秒偏差", data: "0sec" },
  ntpSenOfsetCount: { name: "秒偏差告警量", data: "0sec" },
  ntpSgnLossCount: { name: "型号丢失告警量", data: "0sec" },
}
export let statisticsChart = reactive({
  data: {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '01:15', '02:30', '03:45', '05:00', '06:15', '07:30', '08:45', '10:00', '11:15', '12:30', '13:45', '15:00', '16:15', '17:30', '18:45', '20:00', '21:15', '22:30', '23:45']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} W'
      },
      axisPointer: {
        snap: true
      }
    },
    visualMap: {
      show: false,
      dimension: 0,
      pieces: [
        {
          lte: 1,
          color: '#1e7bff'
        },
        {
          lte: 2,
          color: 'red'
        },
        {
          lte: 3,
          color: '#1e7bff'
        },
        {
          gt: 14,
          lte: 17,
          color: 'red'
        },
        {
          gt: 17,
          color: '#1e7bff'
        }
      ]
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "12%",
      top: "10%",

    },
    series: [
      {
        type: 'line',
        symbol: '', // 设置为空值，不显示符号
        symbolSize: 0, // 设置为0，不显示符号
        smooth: false,
        data: [300, 280, 250, 260, 270, 300, 0, 0, 0, 390, 380, 390, 400, 500, 600, 750, 800, 700, 600, 400],
        markArea: {
          itemStyle: {
            color: 'rgba(39, 128, 255,0.1)'
          },
          data: [
            [
              {
                xAxis: '00:00'
              },
              {
                xAxis: '01:15',
                itemStyle: {
                  color: 'rgba(0, 204, 68, 0.1)'  // 这里是异常线条（红色）的背景色，修改为绿色背景
                }
              }
            ],
            [
              {
                xAxis: '02:30'
              },
              {
                xAxis: '02:30',
                itemStyle: {
                  color: 'rgba(0, 204, 68, 0.1)'  // 这里是异常线条（红色）的背景色，修改为绿色背景
                }
              }
            ],
            [
              {
                xAxis: '03:45'
              },
              {
                xAxis: '03:45',
                itemStyle: {
                  color: 'rgba(0, 204, 68, 0.1)'  // 这里是异常线条（红色）的背景色，修改为绿色背景
                }
              }
            ],
            [
              {
                xAxis: '16:15'
              },
              {
                xAxis: '16:15',
                itemStyle: {
                  color: 'rgba(0, 204, 68, 0.1)'  // 这里是异常线条（红色）的背景色，修改为绿色背景
                }
              }
            ],
            [
              {
                xAxis: '07:30'
              },
              {
                xAxis: '07:30',
                itemStyle: {
                  color: 'rgba(0, 204, 68, 0.1)'  // 这里是异常线条（红色）的背景色，修改为绿色背景
                }
              }
            ],


            [
              {
                xAxis: '11:15'
              },
              {
                xAxis: '11:15',
                itemStyle: {
                  color: 'rgba(0, 204, 68, 0.1)'  // 这里是异常线条（红色）的背景色，修改为绿色背景
                }
              }
            ],
          ]
        }
      }
    ]
  }
});






