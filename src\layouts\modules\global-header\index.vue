<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useFullscreen } from "@vueuse/core";
import { useAppStore } from "@/store/modules/app";
import { useThemeStore } from "@/store/modules/theme";
import { useRouteStore } from "@/store/modules/route";
import HorizontalMenu from "../global-menu/base-menu.vue";
import GlobalLogo from "../global-logo/index.vue";
import GlobalSearch from "../global-search/index.vue";
import { useMixMenuContext } from "../../context";
import ThemeButton from "./components/theme-button.vue";
import UserAvatar from "./components/user-avatar.vue";
import { useTimer } from "@/store/timer";
defineOptions({
  name: "GlobalHeader",
});

interface Props {
  /** Whether to show the logo */
  showLogo?: App.Global.HeaderProps["showLogo"];
  /** Whether to show the menu toggler */
  showMenuToggler?: App.Global.HeaderProps["showMenuToggler"];
  /** Whether to show the menu */
  showMenu?: App.Global.HeaderProps["showMenu"];
}

defineProps<Props>();

const appStore = useAppStore();
const themeStore = useThemeStore();
const routeStore = useRouteStore();
const { isFullscreen, toggle } = useFullscreen();
const { menus } = useMixMenuContext();

const headerMenus = computed(() => {
  if (themeStore.layout.mode === "horizontal") {
    return routeStore.menus;
  }

  if (themeStore.layout.mode === "horizontal-mix") {
    return menus.value;
  }

  return [];
});

const timer = useTimer();
</script>

<template>
  <DarkModeContainer class="h-full flex-y-center top-bar">
    <GlobalLogo
      v-if="showLogo"
      class="h-full"
      :style="{ width: themeStore.sider.width + 'px' }"
    />
    <HorizontalMenu
      v-if="showMenu"
      mode="horizontal"
      :menus="headerMenus"
      class="px-12px"
    />
    <div v-else class="h-full flex-y-center flex-1-hidden">
      <MenuToggler
        v-if="showMenuToggler"
        :collapsed="appStore.siderCollapse"
        @click="appStore.toggleSiderCollapse"
      />
      <div class="table-name" v-show="appStore.siderCollapse">
        <span>单台站性能评估系统</span>
      </div>
      <div class="time-area">
        <div class="left-time">
          <img
            class="date"
            src="../../../assets/home/<USER>"
            alt=""
          />UTC时间：{{ timer.utcTimeValue }}
        </div>
        <div class="right-time">
          <img
            class="date"
            src="../../../assets/home/<USER>"
            alt=""
          />北京时间：{{ timer.beijingTimeValue }}
        </div>
      </div>
    </div>
    <div class="h-full flex-y-center justify-end">
      <FullScreen
        v-if="!appStore.isMobile"
        :full="isFullscreen"
        @click="toggle"
      />
      <UserAvatar />
    </div>
  </DarkModeContainer>
</template>

<style lang="scss" scoped>
.top-bar {
  padding-left: 24px;
  padding-right: 10px;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
  background: #1f87ff;
  color: #fff;
}
.time-area {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-content: center;

  font-weight: 600;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 22px;
  text-align: left;
  font-style: normal;

  .left-time {
    margin-left: 24px;
    margin-right: 48px;
    display: flex;
    align-items: center;
    color: #fff;
  }
  .right-time {
    display: flex;
    align-items: center;
    color: #fff;
  }
}
.date {
  width: 25px;
  height: 20px;
  margin-right: 5px;
}
.table-name {
  width: 250px;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
</style>
