// vite.config.ts
import process3 from "node:process";
import { URL, fileURLToPath } from "node:url";
import { defineConfig, loadEnv } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite@5.3.1_@types+node@20.14.8_sass@1.77.6/node_modules/vite/dist/node/index.js";

// build/plugins/index.ts
import vue from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import VueDevtools from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.4_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import progress from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-progress@0.0.7_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-progress/dist/index.mjs";

// build/plugins/router.ts
import ElegantVueRouter from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@elegant-router+vue@0.3.7/node_modules/@elegant-router/vue/dist/vite.mjs";
function setupElegantRouter() {
  return ElegantVueRouter({
    layouts: {
      base: "src/layouts/base-layout/index.vue",
      blank: "src/layouts/blank-layout/index.vue"
    },
    customRoutes: {
      names: [
        "exception_403",
        "exception_404",
        "exception_500",
        "document_project",
        "document_project-link",
        "document_vue",
        "document_vite",
        "document_naive",
        "document_antd"
      ]
    },
    routePathTransformer(routeName, routePath) {
      const key = routeName;
      if (key === "login") {
        const modules = ["pwd-login", "code-login", "register", "reset-pwd", "bind-wechat"];
        const moduleReg = modules.join("|");
        return `/login/:module(${moduleReg})?`;
      }
      return routePath;
    },
    onRouteMetaGen(routeName) {
      const key = routeName;
      const constantRoutes = ["login", "403", "404", "500"];
      const meta = {
        title: key
      };
      if (constantRoutes.includes(key)) {
        meta.constant = true;
      }
      return meta;
    }
  });
}

// build/plugins/unocss.ts
import process from "node:process";
import path from "node:path";
import unocss from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@unocss+vite@0.61.0_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/@unocss/vite/dist/index.mjs";
import presetIcons from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@unocss+preset-icons@0.61.0/node_modules/@unocss/preset-icons/dist/index.mjs";
import { FileSystemIconLoader } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@iconify+utils@2.1.25/node_modules/@iconify/utils/lib/loader/node-loaders.mjs";
function setupUnocss(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path.join(process.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  return unocss({
    presets: [
      presetIcons({
        prefix: `${VITE_ICON_PREFIX}-`,
        scale: 1,
        extraProperties: {
          display: "inline-block"
        },
        collections: {
          [collectionName]: FileSystemIconLoader(
            localIconPath,
            (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
          )
        },
        warn: true
      })
    ]
  });
}

// build/plugins/unplugin.ts
import process2 from "node:process";
import path2 from "node:path";
import Icons from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/resolver.js";
import Components from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/vite.js";
import { AntDesignVueResolver, NaiveUiResolver } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/resolvers.js";
import { FileSystemIconLoader as FileSystemIconLoader2 } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/loaders.js";
import { createSvgIconsPlugin } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function setupUnplugin(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path2.join(process2.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  const plugins = [
    Icons({
      compiler: "vue3",
      customCollections: {
        [collectionName]: FileSystemIconLoader2(
          localIconPath,
          (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        )
      },
      scale: 1,
      defaultClass: "inline-block"
    }),
    Components({
      dts: "src/typings/components.d.ts",
      types: [{ from: "vue-router", names: ["RouterLink", "RouterView"] }],
      resolvers: [
        AntDesignVueResolver({
          importStyle: false
        }),
        NaiveUiResolver(),
        IconsResolver({ customCollections: [collectionName], componentPrefix: VITE_ICON_PREFIX })
      ]
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: "body-last",
      customDomId: "__SVG_ICON_LOCAL__"
    })
  ];
  return plugins;
}

// build/plugins/html.ts
function setupHtmlPlugin(buildTime) {
  const plugin = {
    name: "html-plugin",
    apply: "build",
    transformIndexHtml(html) {
      return html.replace("<head>", `<head>
    <meta name="buildTime" content="${buildTime}">`);
    }
  };
  return plugin;
}

// build/plugins/index.ts
function setupVitePlugins(viteEnv, buildTime) {
  const plugins = [
    vue({
      script: {
        defineModel: true
      }
    }),
    vueJsx(),
    VueDevtools(),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime)
  ];
  return plugins;
}

// build/config/time.ts
import dayjs from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/dayjs.min.js";
import utc from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/utc.js";
import timezone from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/timezone.js";
function getBuildTime() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const buildTime = dayjs.tz(Date.now(), "Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");
  return buildTime;
}

// vite.config.ts
var __vite_injected_original_import_meta_url = "file:///D:/jnx/single-station-pc/vite.config.ts";
var vite_config_default = defineConfig((configEnv) => {
  const viteEnv = loadEnv(
    configEnv.mode,
    process3.cwd()
  );
  const agentInfo = process3.env.NODE_ENV === "production";
  const buildTime = getBuildTime();
  const baseUrl = viteEnv.VITE_SERVICE_BASE_URL;
  const logApi = viteEnv.VITE_SERVICE_LONGS_URL;
  const proxy = {
    "/Api": {
      target: `${baseUrl}`,
      changeOrigin: true,
      rewrite: (path3) => path3.replace("/Api", "")
    },
    "/logApi": {
      target: `${logApi}`,
      changeOrigin: true,
      rewrite: (path3) => path3.replace("/logApi", "")
    }
  };
  console.log(proxy, agentInfo);
  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        "~": fileURLToPath(new URL("./", __vite_injected_original_import_meta_url)),
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    plugins: [
      ...setupVitePlugins(viteEnv, buildTime)
      // strip({
      //   include: ['**/*.js', '**/*.ts', '**/*.vue'],
      //    functions: agentInfo ? ['console.*', 'debugger'] : [],
      // }),
    ],
    define: {
      BUILD_TIME: JSON.stringify(buildTime)
    },
    // server: {
    //   host: '0.0.0.0',
    //   port: 9527,
    //   open: true,
    //   proxy: createViteProxy(viteEnv, configEnv.command === 'serve'),
    //   fs: {
    //     cachedChecks: false
    //   }
    // },
    server: {
      host: "0.0.0.0",
      proxy,
      port: 3e3
    },
    preview: {
      port: 9725
    },
    build: {
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP === "Y",
      commonjsOptions: {
        ignoreTryCatch: false
      },
      minify: "esbuild",
      esbuild: {
        drop: ["console", "debugger"]
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
