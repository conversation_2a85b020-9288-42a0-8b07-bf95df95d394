_jsload2&&_jsload2('pservice', 'var ji=5;ec.Ou=B.vg("pano")[0];ec.el=ec.Ou+"?";ec.rG=B.vg("baidumap");ec.QP=B.vg("main_domain_nocdn");ec.Wd=new S;ec.lB=[]; z.extend(ec.prototype,{SQ:function(){var a=this,b;for(b in this.xd)if(0!==this.xd[b].length)switch(b){case "getPanoramaById":z.Fb(this.xd[b],function(b){a.ep.apply(a,b)});break;case "getPanoramaByLocation":z.Fb(this.xd[b],function(b){a.kj.apply(a,b)});break;case "getVisiblePOIs":z.Fb(this.xd[b],function(b){a.lE.apply(a,b)});break;case "getRecommendPanosById":z.Fb(this.xd[b],function(b){a.Kx.apply(a,b)});break;case "getPanoramaVersions":z.Fb(this.xd[b],function(b){a.Jx.apply(a,b)});break;case "checkPanoSupportByCityCode":z.Fb(this.xd[b], function(b){a.nC.apply(a,b)});break;case "getPanoramaByPOIId":z.Fb(this.xd[b],function(b){a.Ix.apply(a,b)});break;case "getCopyrightProviders":z.Fb(this.xd[b],function(b){a.o3.apply(a,b)})}},ep:function(a,b,c){this.NH(ec.el+"qt=sdata&l=17&sid="+a+"&fn=",b,c)},kj:function(a,b,c){"function"==typeof b&&(c=b,b=50);a=ec.Wd.kh(a);this.NH(ec.el+"qt=qsdata&x="+a.x+"&y="+a.y+"&r="+b+"&action=1&fn=",c)},lE:function(a,b,c,e){a=ec.Wd.kh(a);this.Tg(ec.el+"qt=search&x="+a.x+"&y="+a.y+"&radius="+b+"&tag="+c+"&fn=", function(a){for(var a=a.content,b=[],c=s,k=a.length-1;0<=k;k--)c=a[k],b.push({iconType:c.Type,title:c.name,altitude:c.Height,panoInfo:{panoId:c.PID,panoIId:c.IID,heading:c.Dir,pitch:c.Pitch},position:ec.Wd.xj(new Q(c.X,c.Y))});e(b)})},Kx:function(a,b){this.Tg(ec.el+"qt=guide&sid="+a+"&fn=",function(a){if(a.content){for(var a=a.content,e=[],f=s,g=0,i=a.length;g<i;g++)f=a[g],e.push({panoId:f.PID,heading:f.Dir,name:f.Info,recoType:f.Type,pitch:f.Pitch,catlog:f.Catalog,floor:f.Floor});b(e)}})},CL:function(a){this.Tg(ec.rG+ "?qt=panoCMS&file=pano_copyright&callback=",function(b){a(b)})},Jx:function(a){this.Tg(ec.rG+"?qt=pver&callback=",function(b){b?a&&a(b):a&&a(s)})},nC:function(a,b){function c(a){for(var b=ec.lB,c=0,i=b.length;c<i;c++)if(b[c]==a)return q;return t}0===ec.lB.length?this.Tg(ec.QP+"?qt=panoCityList&t="+(new Date).getTime()+"&callback=",function(e){e?(ec.lB=e,b(c(a))):b(t)}):b(c(a))},Ix:function(a,b){var c=this;this.Jx(function(e){e&&e.panoUdt&&c.Tg(ec.el+"qt=poi&udt="+e.panoUdt.version+"&uid="+a+"&fn=", function(a){if(a&&a.content&&a.content[0]&&a.content[0].poiinfo){var a=a.content[0].poiinfo,e={id:a.IID||a.PID,pid:a.PID,type:1==a.hasinter?"inter":"street",description:a.name,links:s,position:ec.Wd.xj(new Q(a.X,a.Y)),tiles:s,pov:1==a.hasinter?s:{heading:a.Dir,pitch:a.Pitch}};"inter"===e.type?c.CS(a.IID,function(a){if(a){for(var c=a.Defaultfloor,f=s,n=0,o=a.Floors.length;n<o;n++)if(a.Floors[n].Floor===c){f=a.Floors[n];break}f&&(e.interID=f.StartID)}b(e)}):b(e)}else b(s)})})},NH:function(a,b,c){var e= this;this.Tg(a,function(a){a&&a.result&&0==a.result.error?b&&b(e.sT(a,c)):b&&b(s)})},Tg:function(a,b){var c=(1E5*Math.random()).toFixed(0);B._rd=B._rd||{};B._rd["_cbk"+c]=function(a){b&&b(a);delete B._rd["_cbk"+c]};oa(a+("BMap._rd._cbk"+c))},sT:function(a,b){var c={},e=a.content[0];c.description=e.Rname||e.Info||"";c.id=e.ID;c.rh=e.X/100;c.th=e.Y/100;c.position=ec.Wd.xj(new Q(c.rh,c.th));var f=this.tT(e,c.id,c.rh,c.th,e.NorthDir);c.links=f[0];c.roads=f[1];c.links.sort(function(a,b){return a.vh-b.vh}); c.mode=e.Mode;c.relevants=[];if(e.SwitchID)for(f=0;f<e.SwitchID.length;f++)c.relevants[f]={id:e.SwitchID[f].ID,mode:e.SwitchID[f].Time.toLowerCase()};c.tiles=new ki({dirNorth:e.NorthDir,centerHeading:(180+e.NorthDir)%360,pitch:e.Pitch});if(e.Enters&&0<e.Enters.length){c.indoorPois=[];for(var f=0,g=e.Enters.length;f<g;f++)c.indoorPois.push({panoIId:e.Enters[f].IID,panoId:e.Enters[f].BreakID,title:e.Enters[f].Name,pointX:e.Enters[f].X/100,pointY:e.Enters[f].Y/100})}var g=[],i=e.VPoint;if(i)for(var k= i.length,f=0;f<k;f++){var m={};m.PID=i[f].PID;var n=ec.Wd.xj(new Q(i[f].X/100,i[f].Y/100));m.X=6378137*-(n.lat-c.position.lat)/180*Math.PI;m.Z=6378137*(n.lng-c.position.lng)/180*Math.PI;g.push(m)}c.VPoint=g;e.Inters&&0<e.Inters.length&&(c.bm=e.Inters[0].BreakID,c.NV=e.Inters[0].IID,c.heading=e.MoveDir,c.pitch=e.Pitch);b&&(c.bm=b.bm);c.copyright={};c.copyright.admission=e.Admission;c.copyright.dataProviderIndex=e.Provider;c.copyright.photoDate=e.Date;c.copyright.roadName=e.Rname;c.copyright.username= e.Username||"";return c},tT:function(a,b,c,e,f){var g=[],i={};if(a.Links)for(var k=0;k<a.Links.length;k++)g.push({id:a.Links[k].PID,dir:a.Links[k].DIR,x:a.Links[k].X/100,y:a.Links[k].Y/100,heading:a.Links[k].DIR,vh:this.vA(a.Links[k].DIR,f)});if(!a.Roads)return[g,i];for(k=0;k<a.Roads.length;k++)if(a.Roads[k].Panos)for(var m=0;m<a.Roads[k].Panos.length;m++){if(a.Roads[k].Panos[m].PID==b){var n=a.Roads[k].Name;""==n&&(n=a.Rname||"\\u672a\\u77e5");for(var o=s,p=s,v,x,y=m-1;0<=y;y--){a.Roads[k].Panos[y]&& o===s&&(o=a.Roads[k].Panos[y],v=(o.DIR+180)%360,i[v]=[]);var A=a.Roads[k].Panos[y];i[v]&&i[v].push({id:A.PID,x:A.X/100,y:A.Y/100,distanceToCurrent:this.Xn(A.X/100,A.Y/100,c,e)})}o&&g.push({id:o.PID,dir:v,heading:v,description:n,x:o.X/100,y:o.Y/100,vh:this.vA(v,f)});for(y=m+1;y<a.Roads[k].Panos.length;y++)a.Roads[k].Panos[y]&&p===s&&(p=a.Roads[k].Panos[y],x=p.DIR,0==x&&(x=a.Roads[k].Panos[m].DIR),i[x]=[]),A=a.Roads[k].Panos[y],i[x]&&i[x].push({id:A.PID,x:A.X/100,y:A.Y/100,distanceToCurrent:this.Xn(A.X/ 100,A.Y/100,c,e)});p!=s&&g.push({id:p.PID,dir:x,heading:x,description:n,x:p.X/100,y:p.Y/100,vh:this.vA(x,f)})}for(y=0;y<g.length;y++)a.Roads[k].Panos[m].PID==g[y].id&&(g[y].description=a.Roads[k].Name,""==g[y].description&&(g[y].description=a.Rname||"\\u672a\\u77e5"))}for(k=0;k<g.length;k++){var a=g[k].dir,b=t,E;for(E in i)if(E==a){b=q;break}if(b)break;i[a]=[{id:g[k].id,x:g[k].x,y:g[k].y,distanceToCurrent:this.Xn(g[k].x,g[k].y,c,e)}]}return[g,i]},vA:function(a,b){var c=a+b;360<c&&(c%=360);return c= Math.round(100*c)/100},Xn:function(a,b,c,e){return Math.round(Math.sqrt(Math.pow(a-c,2)+Math.pow(b-e,2)))},CS:function(a,b){this.Tg(ec.el+"qt=idata&l=17&iid="+a+"&fn=",function(a){a&&a.result&&0===a.result.error?b(a.content[0].interinfo):b(s)})}});ne=ec.prototype;T(ne,{getPanoramaById:ne.ep,getPanoramaByLocation:ne.kj,getPanoramaByPOIId:ne.Ix});function ki(a){this.tileSize=new O(512,512);this.worldSize=new O(512*this.hp(ji),512*this.Lx(ji));this.centerHeading=180;var a=a||{},b;for(b in a)this[b]=a[b]}var li=B.vg("pano","scape/");z.extend(ki.prototype,{getTilesUrl:function(a,b,c){return li[(b.x+b.y)%li.length]+"?qt=pdata&sid={sid}&pos={y}_{x}&z={zoom}".replace("{sid}",a).replace("{x}",b.x).replace("{y}",b.y).replace("{zoom}",c)},Lx:function(a){return Math.pow(2,a-2)},hp:function(a){return 2*this.Lx(a)}}); ');