_jsload2&&_jsload2('vector', 'z.extend(Tc.prototype,{na:function(a,b){Mc.prototype.na.call(this,a,b);this.Eg={};this.ir(a);this.vq();this.Pq()},remove:function(){var a=this.B;Mc.prototype.remove.call(this);a&&(a.removeEventListener("zoomend",this.bl),a.removeEventListener("moveend",this.Gk),a.removeEventListener("resize",this.Ok),a.removeEventListener("hotspotclick",this.jM),a.dm(this.j.Fk))},ir:function(a){this.B=a;this.Vh=this.Jb;this.qc=this.B.K.devicePixelRatio;this.mn=62;a.dm(this.j.Fk);for(var b in this.lp)delete this.lp[b]}, vq:function(){var a=this,b=this.B;a.bl=function(b){a.Pq(b)};a.Gk=function(b){a.Pq(b)};a.Ok=function(b){a.Pq(b)};a.jM=function(b){0<b.spots.length&&a.pU(b.spots[0].getUserData().uid)};b.addEventListener("zoomend",a.bl);b.addEventListener("moveend",a.Gk);b.addEventListener("resize",a.Ok);b.addEventListener("hotspotclick",a.jM)},pU:function(a){var b=this,c=(1E5*Math.random()).toFixed(0);!B._rd&&(B._rd={});B._rd["_cbk"+c]=function(a){b.xR(a);delete B._rd["_cbk"+c]};var e=b.GW;this.j.eh&&(e=b.HW);a=e+ a+"?scope=2&ak="+this.j.Vl+"&callback=BMap._rd._cbk"+c;this.j.eh&&(a+="&geotable_id="+this.j.eh);oa(a)},xR:function(a){var b=a.content;this.j.eh&&(b=a.contents[0]);if(a&&0==a.status&&b){var a={poiId:b.uid||"",databoxId:b.databox_id||"",title:this.j.eh?b.title:b.name,address:this.j.eh?b.address:b.addr,phoneNumber:b.tel||"",postcode:b.zip||"",provinceCode:1*b.province_id||-1,province:b.province||"",cityCode:1*b.city_id||-1,city:b.city||"",districtCode:1*b.district_id||-1,district:b.district||"",point:this.j.eh? new J(b.location[0],b.location[1]):new J(b.longitude,b.latitude),tags:b.tag&&b.tag.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g,"").split(/\\s+/),typeId:1*b.cla||-1,extendedData:b.ext||{}},c=new P("onhotspotclick");c.customPoi=a;c.content=b;this.$y&&this.$y.dispatchEvent(c);this.dispatchEvent(c)}},Pq:function(){var a=this.B,b=a.fa(),c=a.Va.pj(this.Vh,"yun",this.mn);if(a.Sb()){this.Km||(this.Km=q,0<c.length&&(this.le=a.oa().$c()));this.$m=Math.pow(2,18-b);this.ph=c.length;a=0;for(b=c.length;a<b;a++)this.vr(c[a][0], c[a][1],c[a][2])}},vr:function(a,b,c){var e=this,f=e.map,g=f.Ka(),i=new J(g.lng,g.lat),k=f.fa(),f=Wc[Math.abs(a+b)%Wc.length]+"/data?grids="+a+"_"+b+"_"+k+"&q="+this.j.Wm+"&tags="+this.j.pu+"&filter="+this.j.filter+"&sortby="+this.j.Vy+"&ak="+this.j.Vl+"&age="+this.j.Gw,f=this.j.eh?f+("&geotable_id="+this.j.eh):f+("&databox_id="+this.j.OK);c.yd=t;oa(f,function(f){var f=0==f.status?f.content[0].data:[],g=e.map,o=g.Ka(),g=g.fa();o.pb(i)&&g==k&&(e.Sn(f,c,a,b,k),e.ph--,0==e.ph&&e.$y.dispatchEvent("oncustomlayerloaded"))})}, Sn:function(a,b,c,e,f){var g=b.getContext("2d");1<this.qc&&!b.og&&(g.scale(this.qc,this.qc),b.og=q);b.TB||(b.TB=q,g.translate(this.mn/2,this.mn/2));for(var f="hotSpotTile_"+c+"_"+e+"_"+f,i=0,k=a.length;i<k;i++)this.XW(g,a[i],c,e,f,b)},XW:function(a,b,c,e,f,g){var i=this,k=b[0],m=b[1],n=b[2],o=b[3],i=this,p=this.le,b=this.$m,c=c*b*p,e=(e+1)*b*p;/^POINT\\((.*)\\)$/.test(n);var n=RegExp.$1.split(","),v=n[0],x=n[1],y=(v-c)/b,A=(e-x)/b,E=new Image;E.onload=function(){var b=this.width,c=this.height;a.drawImage(this, y-b/2,A-c);i.lp[f]||(b={userData:{name:k,uid:m},offsets:[c,b/2,0,b/2]},b=new ib(S.Tb(new J(v,x)),b),i.B.Cw(b,i.j.Fk));setTimeout(function(){i.lp[f]=q;g.yd=q},1E3);delete this.onload};this.lY(o,function(a){""==o&&(o="sid1");E.src=a&&a.content&&a.content[o]&&""!=a.content[o]?"data:image/png;base64,"+a.content[o]:G.qa+"madian.png"})},c5:function(){if(this.Jb){for(var a=this.Jb,b=0,c=a.childNodes.length;b<c;b++)a.childNodes[b].yd=t;this.B.dm(this.j.Fk);for(var e in this.lp)delete this.lp[e];this.Pq()}}, lY:function(a,b){var c=this,e=this.j.OK,f=this.j.Y_,g=this.j.Vl;""==a&&(a="sid1");if(this.Eg[a]&&this.Eg[a].data)b(this.Eg[a].data);else{this.Eg[a]||(this.Eg[a]={},this.Eg[a].kC=[]);this.Eg[a].kC.push(b);var i=B.Wc+"style/poi/rangestyle?method=getstyle";this.j.eh&&(e=this.j.eh);i+="&databox="+e+"&sid="+a+"&ak="+g;f&&(i+="&self_id="+f);this.Eg[a].YY||(setTimeout(function(){oa(i,function(b){var e=c.Eg[a].kC;c.Eg[a].data=b;for(var f=0;f<e.length;f++)e[f](b);c.Eg[a].kC.length=0})},10),c.Eg[a].YY=q)}}});z.extend(Uc.prototype,{na:function(a,b){Mc.prototype.na.call(this,a,b);this.ir(a);this.vq();this.jv()},remove:function(){var a=this.B;Mc.prototype.remove.call(this);a.removeEventListener("zoomend",this.bl);a.removeEventListener("moveend",this.Gk);a.removeEventListener("resize",this.Ok)},ir:function(a){this.B=a;this.Vh=this.Jb;this.qc=this.B.K.devicePixelRatio;this.mn=0},vq:function(){var a=this,b=this.B;a.bl=function(b){a.jv(b)};a.Gk=function(b){a.jv(b)};a.Ok=function(b){a.jv(b)};b.addEventListener("zoomend", a.bl);b.addEventListener("moveend",a.Gk);b.addEventListener("resize",a.Ok)},jv:function(){var a=this.B,b=a.Va.pj(this.Vh,"traffic",this.mn);this.Km||(this.Km=q,0<b.length&&(this.le=a.oa().$c()));for(var a=0,c=b.length;a<c;a++)this.vr(b[a][0],b[a][1],b[a][2])},vr:function(a,b,c){var e=this,f=e.map,g=f.Ka(),i=new J(g.lng,g.lat),k=f.fa(),m="_t"+parseInt(a+""+b+""+k).toString(36),f=this.x0+"&x="+a+"&y="+b+"&z="+k+"&fn=BMap."+m+"&t="+(new Date).getTime();c.yd=t;B[m]=function(f){var g=e.map,p=g.Ka(),g= g.fa();p.pb(i)&&g==k&&f.content&&f.content.tf&&e.Sn(f.content.tf,c,a,b);delete B[m]};oa(f,t)},Sn:function(a,b){var c=b.getContext("2d"),e=this.we,f=this.Bx,g=this.Cx;1<this.qc&&!b.og&&(c.scale(this.qc,this.qc),b.og=q);b.TB||(b.TB=q,c.translate(this.mn/2,this.mn/2));for(var i=0,k=a.length;i<k;i++){var m=a[i],n=m[1],o=this.Db[m[3]],m=this.Db[m[4]],p=n[0]/10,v=n[1]/10;c.beginPath();c.moveTo(p,v);for(var x=2,y=n.length;x<y;x+=2)p+=n[x]/10,v+=n[x+1]/10,c.lineTo(p,v);c.strokeStyle=e(o[1]);c.lineWidth=o[2]; c.lineCap=f(o[3]);c.lineJoin=g(o[4]);c.stroke();c.strokeStyle=e(m[1]);c.lineWidth=m[2];c.lineCap=f(m[3]);c.lineJoin=g(m[4]);c.stroke()}b.yd=q},we:function(a){a>>>=0;return"rgba("+(a>>24&255)+","+(a>>16&255)+","+(a>>8&255)+","+(a&255)/256+")"},Bx:function(a){return["butt","square","round"][a]},Cx:function(a){return["miter","bevel","round"][a]}});z.extend(db.prototype,{De:function(){this.na(this.B,this.C)},na:function(a,b){this.B=a;this.C=b;this.B&&this.C&&(this.ir(),this.$S(),this.vq(),this.Oq())},remove:function(){var a=this.B;this.C.removeChild(this.Ya);a.removeEventListener("zoomend",this.bl);a.removeEventListener("moving",this.mN);a.removeEventListener("moveend",this.Gk);a.removeEventListener("resize",this.Ok)},ir:function(){var a=this.B,b=a.K.devicePixelRatio,c=a.oa().$c(),e=a.cb(),a=e.width,e=e.height;this.Th={};this.le=c;this.qc=b; this.QE=a;this.PE=e;this.CC=a*b;this.BC=e*b},$S:function(){var a=this.B,b=this.C,c=document.createElement("canvas"),e=c.style;e.position="absolute";e.zIndex=5;e.left=-a.offsetX+"px";e.top=-a.offsetY+"px";e.width=this.QE+"px";e.height=this.PE+"px";c.setAttribute("width",this.CC);c.setAttribute("height",this.BC);b.appendChild(c);this.Ya=c;this.Fh=c.getContext("2d");a=this.qc;1<a&&!c.og&&(this.Fh.scale(a,a),c.og=q)},vq:function(){function a(){b.Ya.style.left=-c.offsetX+"px";b.Ya.style.top=-c.offsetY+ "px"}var b=this,c=b.B,e=b.qc;b.bl=function(c){b.Ya.style.display=18>this.fa()?"none":"block";a();b.Oq(c)};b.mN=function(c){var e=(new Date).getTime();100>e-b.LM||(b.LM=e,a(),b.Oq(c))};b.Gk=function(c){a();b.Oq(c)};b.Ok=function(f){var g=c.cb(),i=g.width,g=g.height,k=i*e,m=g*e;b.Ya.style.width=i+"px";b.Ya.style.height=g+"px";b.Ya.setAttribute("width",k);b.Ya.setAttribute("height",m);b.QE=i;b.PE=g;b.CC=k;b.BC=m;a();b.Oq(f)};c.addEventListener("zoomend",b.bl);c.addEventListener("moving",b.mN);c.addEventListener("moveend", b.Gk);c.addEventListener("resize",b.Ok)},Oq:function(a){var b=this.B,c=b.fa();if(!(18>c)){var e=b.Va.DL(),f=e[0],b=e[1],g=e[2],e=e[3],a=a?t:q,i;for(i in this.Th)for(var k=f;k<g;k++)for(var m=b;m<e;m++)i==k+"_"+m+"_"+c&&(this.Th[i].jI=q);for(i in this.Th)this.Th[i].jI?delete this.Th[i].jI:(this.Th[i]=s,delete this.Th[i]);this.Fh.clearRect(0,0,this.CC,this.BC);for(k=f;k<g;k++)for(m=b;m<e;m++)(i=this.Th[k+"_"+m+"_"+c])?this.Sn(i,k,m,c,a):this.vr(k,m,c,a)}},vr:function(a,b,c,e){var f=this,g=f.B,i=f.PV, k=(a+b)%i.length,m="x="+a+"&y="+b+"&z="+c,n=g.aa.replace(/^TANGRAM_/,"")+"building"+parseInt(a+""+b+""+c).toString(36),i=i[k]+"qt=hjpgvd&"+m+"&styles=pl&layers=bg&features=bd&f=mwebapp&v=001&udt=20130501&fn=BMap."+n,k=g.Ka(),o=new J(k.lng,k.lat),p=g.fa();B[n]=function(i){if(i=i.content){var k=g.Ka(),m=g.fa();if(!k.pb(o)||m!=p){delete B[n];return}if(i=window.IZ(i))f.Sn(i,a,b,c,e),f.Th[a+"_"+b+"_"+c]=i}delete B[n]};oa(i)},Sn:function(a,b,c,e,f){f&&this.j.pL?this.ER(a,b,c,e):this.zH(a,b,c,e,1)},ER:function(a, b,c,e){var f=this;a.pQ=new tb({Ic:50,duration:200,va:function(g){f.zH(a,b,c,e,g)},finish:function(){a.pQ=s}})},zH:function(a,b,c,e,f){var g=this.B,i=this.QE,k=this.PE,e=g.oa().dc(e),g=g.fc,m=this.le,b=b*m-g.lng/e,c=(-1-c)*m+g.lat/e,e=this.Fh,g=a.length,m=0,n=window.Db;e.save();for(e.translate(b+i/2,c+k/2);m<g;m++){var o=a[m],p=n[o[3]];if(4==p[0]){var v=o[7];window.VectorDrawUtils.eD(e,o[1],6*(o[2]*p[3])*f,90*((b+(v[0]+v[2])/2)/(i/2)),90*((c+(v[1]+v[3])/2)/(k/2)),"rgba(212,205,197,1)","rgba(235,233,228,1)", "rgba(170,170,170,1)",1)}}e.restore()}}); ');