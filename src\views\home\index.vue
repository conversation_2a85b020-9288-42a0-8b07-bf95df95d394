<template>
  <div class="home">
    <div class="comparison">
      <div class="home_dtz">
        <img
          src="../../assets/home/<USER>"
          class="home_dtz_icon"
          alt=""
        />
        <div class="names">{{ transmitters }}</div>
      </div>
      <div
        class="comparisonItem"
        v-for="key in Object.keys(comparisonData)"
        :key="key"
        :class="key"
      >
        <div class="comparison_nav">
          <div class="comparison_name">
            <span>{{ comparisonData[key].name }}</span>
            <span
              :class="setYS(key, comparisonData[key].state, 'color')"
              class="devicestate"
            >
              {{ setYS(key, comparisonData[key].state, "value") }}
            </span>
          </div>
        </div>
        <div class="comparison_food">
          <div :class="key + 'bd'" v-if="key == 'toc'">
            <div class="bodysd">
              <span style="width: 56px">GTP</span>:
              <span
                class="value"
                :class="setYS(key, comparisonData[key].state, 'color')"
                >{{ comparisonData.toc.value }}ns</span
              >
            </div>
            <div>
              <span>符合时间:</span>
              <span :class="setYS(key, comparisonData[key].state, 'color')">{{
                comparisonData.toc.tocTimeMark
              }}</span>
            </div>
          </div>
          <div :class="key + 'bd'" v-if="key == 'timeFrames'">
            <div class="flex_value">
              <span class="name">参考值:</span>
              <span class="value">{{
                comparisonData.timeFrames.timeMarkckdq || "--"
              }}</span>
            </div>
            <div class="flex_value">
              <span class="name">采集值:</span>
              <span
                class="value"
                :class="
                  comparisonData[key].state
                    ? 'deviceItem_normal'
                    : 'deviceItem_abnormal'
                "
                >{{ comparisonData.timeFrames.timeMarkcjyb || "--" }}</span
              >
            </div>
          </div>
          <div :class="key + 'bd'" v-if="key == 'differential'">
            <div class="flex_value">
              <span class="name">差分站ID</span>
              <span
                class="value"
                :class="
                  comparisonData[key].state
                    ? 'deviceItem_normal'
                    : 'deviceItem_abnormal'
                "
                @click="showDiffDetails()"
                >{{ comparisonData.differential.diffId || "--" }}</span
              >
            </div>
          </div>
          <div :class="key + 'bd'" v-if="key == 'dut'">
            <div class="flex_value">
              <span>参考:当日</span>
              <span
                class="value"
                :class="
                  comparisonData[key].state
                    ? 'deviceItem_normal'
                    : 'deviceItem_abnormal'
                "
                >{{
                  (comparisonData.dut.dut1ckdq - 0).toFixed(5) || "--"
                }}</span
              >
              <span class="name">次日:</span>
              <span class="value">{{
                (comparisonData.dut.dut1ckyb - 0).toFixed(5) || "--"
              }}</span>
            </div>
            <div class="flex_value">
              <span>采集:当日</span>
              <span
                class="value"
                :class="
                  comparisonData[key].state
                    ? 'deviceItem_normal'
                    : 'deviceItem_abnormal'
                "
                >{{
                  (comparisonData.dut.dut1cjdq - 0).toFixed(5) || "--"
                }}</span
              >
              <span class="name">次日:</span>
              <span class="value">{{
                (comparisonData.dut.dut1cjyb - 0).toFixed(5) || "--"
              }}</span>
            </div>
          </div>

          <div :class="key + 'bd'" v-if="key == 'ssdw'">
            <div class="titleName">
              <span class="titleItem shi">时</span>
              <span class="titleItem fen">分</span>
              <span class="titleItem miao">秒</span>
              <span class="titleItem weim">毫秒</span>
              <span class="titleItem haom">微秒</span>
              <span class="titleItem nami">十纳秒</span>
            </div>
            <div class="flex_value">
              <span>参考值:</span>
              <span class="name">{{ comparisonData.ssdw.dwdckdq }}</span>
            </div>
            <div class="flex_value">
              <span>采集值:</span>
              <span
                class="name"
                :class="
                  comparisonData[key].state
                    ? 'deviceItem_normal'
                    : 'deviceItem_abnormal'
                "
                >{{ comparisonData.ssdw.dwdcjyb }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="chartListBox">
      <div class="chartList">
        <el-tabs class="demo-tabs" v-model="activeName1">
          <el-tab-pane label="授时偏差" name="deviation">
            <div class="chartData">
              <lineChart
                class="chartArea"
                :data="deviationObj.chartData"
                ref="deviationRef"
              /></div
          ></el-tab-pane>
        </el-tabs>
        <el-tabs
          class="demo-tabs"
          v-model="activeName"
          @tab-change="switchChart"
        >
          <el-tab-pane
            v-for="(value, key) in diagramData"
            :key="key"
            :label="value.name"
            :name="key"
          >
            <div class="chartData">
              <lineChart
                class="chartArea"
                :data="value.chartData"
                :ref="(el) => (boxP[key] = el)"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-button class="seeTable1" @click="seeTable(false)">查看</el-button>
        <el-button class="seeTable2" @click="seeTable(true)">查看</el-button>
      </div>
      <el-table
        class="lists"
        :data="tableData"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection" />
        <el-table-column align="center" width="80" type="index" label="序号" />
        <el-table-column align="center" prop="date" label="时间">
          <template #default="scope">
            {{ proxy.configure.formatTimestamp(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="alarmFrom" label="发波台" />
        <el-table-column align="center" prop="alarmField" label="告警类型">
          <template #default="scope">
            {{ setAlarmList(scope.row.alarmField) }}
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="操作">
            <template #default="scope">
              <el-button @click="onSeeTable(scope.row)"> 查看 </el-button>
            </template>
          </el-table-column> -->
      </el-table>
      <div class="clean" v-show="showClean" @click="onCleaList">清除</div>
    </div>
    <div class="foodDatas">
      <div class="listDatas">
        <div class="listCard listCard1">
          <div class="listCardItem">
            <div class="listCardItem_nav">
              <div>阻断率</div>
              <div class="listCardItem_value">
                {{ chartListState.blockingRate.value }}
              </div>
            </div>
            <img
              class="listCardItem_img"
              src="../../assets/home/<USER>"
              alt=""
            />
          </div>
          <div class="listCardItem">
            <div class="listCardItem_nav">
              <div style="width: 160px">峰值有效功率</div>
              <div class="listCardItem_value">
                {{ chartListState.peakPowerRate.value }}
              </div>
            </div>
            <img
              class="listCardItem_img"
              src="../../assets/home/<USER>"
              alt=""
            />
          </div>
        </div>
        <div class="listCard listCard1 listCard2">
          <div class="listCardItem">
            <div class="listCardItem_nav">
              <div>
                连续性
                <span class="company">（24）</span>
              </div>
              <div class="listCardItem_value">
                {{ chartListState.continuity.value }}
              </div>
            </div>
            <lineCharts
              class="chartstate_chart"
              :data="chartListState.continuity.chartData"
            />
          </div>
          <div class="listCardItem">
            <div class="listCardItem_nav">
              <div>可用性 <span class="company">（24）</span></div>
              <div class="listCardItem_value">
                {{ chartListState.usability.value }}
              </div>
            </div>
            <div class="usability_box">
              <!-- <div class="usability"></div> -->
              <lineCharts
                class="chartstate_chart"
                :data="chartListState.usability.chartData"
              />
            </div>
          </div>
        </div>
        <div class="listCard listCard1">
          <div class="listCardItem">
            <div class="listCardItem_nav">
              <div>
                完好性
                <span class="company" style="position: absolute; left: 80px"
                  >（24）</span
                >
              </div>
            </div>
            <div class="integrity-list">
              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">总告警数 产生:</span>
                  <span class="value">{{
                    chartListState.integrity.value.sj_totalAlarms
                  }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">发送:</span>
                  <span class="value">{{
                    chartListState.integrity.value.totalAlarms
                  }}</span>
                </div>
              </div>
              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">授时偏差 产生:</span>
                  <span class="value">{{
                    chartListState.integrity.value.sj_tser
                  }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">发送:</span>
                  <span class="value">{{
                    chartListState.integrity.value.tserAlarms
                  }}</span>
                </div>
              </div>
              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">场强 产生:</span>
                  <span class="value">{{
                    chartListState.integrity.value.sj_fieldStrength
                  }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">发送:</span>
                  <span class="value">{{
                    chartListState.integrity.value.fieldStrengthAlarms
                  }}</span>
                </div>
              </div>

              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">一级告警 产生:</span>
                  <span class="value">{{
                    chartListState.integrity.value.type1Alarms
                  }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">发送:</span>
                  <span class="value">{{
                    chartListState.integrity.value.type1Alarms
                  }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="listCardItem">
            <div class="listCardItem_nav">
              <div>准确性 <span class="company">（24）</span></div>
              <div class="listCardItem_value">
                {{ chartListState.accuracy.value }}
              </div>
            </div>
            <accurate :accuracy="chartListState.accuracy.value || 0" />
          </div>
        </div>
      </div>
      <div class="newDatas">
        <el-card class="listCard listCard2">
          <template #header>
            <div class="card-header listCardHeader">
              <div>实时数据</div>

              <n-select
              v-model:value="realTime.realTimeIndex"
              filterable
              tag
              placeholder="请选择设备"
              class="select"
              :options="realTime.options.map(item => ({ label: item, value: item }))"
            />
            </div>
          </template>
          <div class="realTimeBox">
            <div
              class="realTimeItem"
              v-for="item in realTime.data[realTime.realTimeIndex]"
              :key="item"
            >
              <span class="realtime-content">{{ item }}</span>
            </div>
            <div class="connection"></div>
          </div>
        </el-card>
      </div>
    </div>
  </div>

  <el-dialog v-model="dialogTableVisible" :title="gridData.title" width="1000">
    <el-table class="tableHome" :data="gridData.data">
      <el-table-column property="startTime" label="开始时间" />
      <el-table-column property="endTime" label="结束时间" />
      <el-table-column property="max" label="最大值" />
      <el-table-column property="min" label="最小值" />
      <el-table-column property="avg" label="均值" />
      <el-table-column property="stdDev" label="标准差" />
      <el-table-column property="rms" label="RMS" />
      <el-table-column property="percentile95" width="90" label="95%分位数" />
    </el-table>
  </el-dialog>

  <el-dialog
    title="报警信息"
    v-model="alarmMessage.info"
    width="30%"
    class="dialog"
    center
  >
    <div style="padding: 20px">
      <el-row>
        <el-col :span="6">
          <div class="label">报警对象:</div>
        </el-col>
        <el-col :span="18">
          <div class="value">设备一</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <div class="label">报警内容:</div>
        </el-col>
        <el-col :span="18">
          <div class="value">超过阈值</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <div class="label">去处理:</div>
        </el-col>
        <el-col :span="18">
          <el-button type="text" @click="ondesade">点击处理</el-button>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="alarmMessage.info = false">取 消</el-button>
        <el-button type="primary" @click="alarmMessage.info = false"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>

  <el-dialog
    title="差分站详情"
    v-model="diffDetailsVisible"
    width="800px"
    class="dialog diff-details-dialog"
    center
  >
    <div class="diff-details-container">
      <el-table :data="diffTableData" border style="width: 100%">
        <el-table-column
          prop="paramName"
          label="参数名称"
          width="200"
          align="center"
        />
        <el-table-column prop="referenceData" label="参考数据" align="center" />
        <el-table-column
          prop="collectionData"
          label="采集数据"
          align="center"
        />
      </el-table>
    </div>
  </el-dialog>
</template>

<script setup>
import { deviationObj } from "./data";
import {
  comparisonData,
  diagramData,
  chartListState,
  getTransmitters,
  homeNavScoket,
  transmitters,
  sethomeNavScoket,
  setAlarmList,
  switchChart,
  getTableData,
} from "./data";
import lineChart from "../../components/lineChart/lineChart.vue";
import lineCharts from "../../components/lineChart/lineCharts.vue";
import accurate from "../../components/accurate/index.vue";
import apiAjax from "@/api/index";
import {
  ref,
  reactive,
  watch,
  onMounted,
  onUnmounted,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
import {
  subscribe,
  unsubscribe,
  unsubscribeAll,
  initScocket,
} from "../../api/ws";
import { useRouterPush } from "@/hooks/common/router";
import { DataLine } from "@element-plus/icons-vue";
const { proxy } = getCurrentInstance();
const { routerPushByKey } = useRouterPush();
const dialogTableVisible = ref(false); // 查看数据表格
const activeName = ref("fieldStrength"); // 场强ref
const activeName1 = ref("deviation"); // 授时偏差ref
const deviationRef = ref(); // 授时偏差
const showClean = ref(false); // 下面的清空按钮
const cleaList = ref([]); // 需要这次清理的数组
const alarmMessage = reactive({
  info: false,
}); // 告警信息
const boxP = ref({}); // 图表数组
const diffDetailsVisible = ref(false);

// 这次验收 差分修  和 dut1 状态都显示 --
const setYS = (key, value, type) => {
  const obj = {
    color: () => {
      if (key == "toc") {
        return value == 2
          ? "deviceItem_login"
          : value == 0
            ? "deviceItem_normal"
            : "deviceItem_abnormal";
      } else {
        return value ? "deviceItem_normal" : "deviceItem_abnormal";
      }
    },
    value: () => {
      if (key == "differential" || key == "dut") {
        return "--";
      } else if (key == "toc") {
        return value == 2 ? "比对中" : value == 0 ? "正常" : "异常";
      } else {
        return value ? "正常" : "异常";
      }
    },
  };
  return obj[type]();
};

let realTime = reactive({
  data: {},
  realTimeIndex: "",
  options: [],
});
//获取实时数据的地方
let setRealItme = (data) => {
  let newData = JSON.parse(data);
  let realTimeItem = realTime.data;
  if (!realTimeItem[newData.title]) {
    realTimeItem[newData.title] = [];
  }
  if (realTimeItem[newData.title].length > 10) {
    realTimeItem[newData.title].pop();
  }
  nextTick(() => {
    realTimeItem[newData.title].unshift(newData.data);
    Object.keys(realTimeItem).forEach((i) => {
      realTime.realTimeIndex = realTime.realTimeIndex || i;
      if (realTime.options.indexOf(i) === -1) {
        realTime.options.push(i);
      }
    });
  });
};

const ondesade = () => {
  alarmMessage.info = false;
  routerPushByKey("work-condition", { params: { id: "123" } });
};

const diffTableData = ref([
  {
    paramName: "ASF",
    referenceData: computed(() =>
      comparisonData.differential.asfck
        ? comparisonData.differential.asfck + "ns"
        : "--",
    ),
    collectionData: computed(() => comparisonData.differential.asfyb || "--"),
  },
  {
    paramName: "差分模型起点时刻",
    referenceData: computed(
      () => comparisonData.differential.diffForecastck || "--",
    ),
    collectionData: computed(
      () => comparisonData.differential.diffForecastyb || "--",
    ),
  },
  {
    paramName: "差分改正参数a0",
    referenceData: computed(() => comparisonData.differential.diffA0ck || "--"),
    collectionData: computed(
      () => comparisonData.differential.diffA0yb || "--",
    ),
  },
  {
    paramName: "差分改正参数a1",
    referenceData: computed(() => comparisonData.differential.diffA1ck || "--"),
    collectionData: computed(
      () => comparisonData.differential.diffA1yb || "--",
    ),
  },
]);

const showDiffDetails = () => {
  diffDetailsVisible.value = true;
};

onMounted(async () => {
  await initScocket();
  sethomeNavScoket();
  // 获取主台站 并且订阅一些消息和 获取历史图形数据
  await getTransmitters({ ...boxP.value, deviationRef: deviationRef.value });
  // 获取历史告警数据
  await getRealtimeData();

  // 获取实时数据
  subscribe("/topic/jsInfo/realtimedata/REALTIME-DATA", (data) => {
    nextTick(() => {
      setRealItme(data);
    });
  });
});
onUnmounted(() => {
  unsubscribeAll();
});
// 图标展开的数据
const gridData = ref({
  title: "",
  data: [],
});
// 找到选中的数据
const seeTable = async (info) => {
  const data = await getTableData(info, activeName.value);

  gridData.value.data = data;
  gridData.value.title = info ? diagramData[activeName.value].name : "授时偏差";
  dialogTableVisible.value = true;
};

const onJump = () => {};

const tableData = ref([]);
const selectionChange = (val) => {
  val.length > 0 ? (showClean.value = true) : (showClean.value = false);
  cleaList.value = val;
};

// 获取实时告警数据
const getRealtimeData = async () => {
  const data = await apiAjax.post("/api/jnx/dataAlarm/getAlarmRealInfo", {
    page: 1,
    size: 1000,
  });
  tableData.value = data[0].content;
  subscribe("/topic/jsInfo/jsgjrealtimedata/ELORAN-ALARM-INFO", (val) => {
    let data = JSON.parse(val);
    if (data.showDownType) {
      tableData.value = tableData.value.filter(
        (item) => item.alarmContent !== data.showDownType,
      );
    } else {
      // 检查是否已存在
      const index = tableData.value.findIndex(
        (item) => item.alarmContent === data.alarmContent,
      );
      if (index !== -1) {
        tableData.value[index] = data;
      } else {
        tableData.value.unshift(data);
      }
    }
  });
};

const onCleaList = () => {
  cleaList.value.forEach((i) => {
    tableData.value.forEach((item, index) => {
      if (item.id == i.id) {
        tableData.value.splice(index, 1);
      }
    });
  });
};
const onSeeTable = () => {
  alarmMessage.info = true;
};
</script>

<style lang="scss" scoped>
.home {
  width: 100%;
  display: flex;
  flex-direction: column;
  .comparison {
    display: flex;
    width: 100%;
    .home_dtz {
      width: 206px;
      background: linear-gradient(180deg, #ddefff 0%, #ffffff 100%) no-repeat
        #fff;
      background-size: 100% 100px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .home_dtz_icon {
        width: 66px;
        margin-bottom: 19px;
      }
      .names {
        font-weight: 600;
        font-size: 19px;
        color: rgba(0, 0, 0, 0.9);
      }
    }
    .comparisonItem {
      width: 22%;
      height: 130px;
      display: flex;
      margin-left: 16px;
      border-radius: 2px;
      background-size: 100% 100px;
      display: flex;
      flex-direction: column;
      padding: 24px;
      padding-right: 0;
      padding-bottom: 0;
      overflow: hidden;
      .comparison_nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        .comparison_name {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding-right: 15px;
        }
        .comparison_state {
          width: 40px;
          height: 24px;
          border-radius: 3px;
          border: 1px solid #00a870;
          line-height: 20px;
          text-align: center;
          font-weight: 400;
          font-size: 12px;
          color: #00a870;
        }
        .yic {
          border: 1px solid #e34d59;
          color: #e34d59;
        }
      }
      .comparison_food {
        flex: 1;
        display: flex;
        align-items: center;
        .tocbd {
          .bodysd {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 16px;
            color: #2b2e3f;
            .value {
              margin-left: 8px;
              color: #266fe8;
            }
          }
        }
        .timeFramesbd,
        .differentialbd,
        .dutbd,
        .ssdwbd {
          display: flex;
          flex-direction: column;
          .flex_value {
            display: flex;
            align-items: center;
            font-size: 14px;
            .name {
              color: #2b2e3f;
              margin-left: 10px;
            }
            .value {
              margin-left: 5px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.9);
              font-size: 16px;
            }
          }
        }
      }
      .blue {
        color: #266fe8 !important;
      }
    }
  }
  .chartListBox {
    width: 100%;
    height: 680px;
    display: flex;
    margin: 16px 0;
    overflow: hidden;
    position: relative;
    .chartList {
      // flex: 1;
      position: relative;
      width: 70%;
      background-color: #fff;
      padding: 18px;
      margin-right: 16px;
      :deep(.el-tabs__item) {
        font-size: 16px;
        font-weight: 600;
      }
      .chartData {
        width: 100%;
        display: flex;
        flex-direction: column;
        .chartArea {
          width: 100%;
          // width: 830px;
          height: 283px;
        }
        .tableArea {
          width: 100%;
          // width: 830px;
          padding-top: 17px;
        }
      }
    }
    .seeTable1 {
      position: absolute;
      right: 25px;
      top: 16px;
    }
    .seeTable2 {
      position: absolute;
      right: 25px;
      top: 350px;
    }
  }

  .card-header {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: space-between;
  }
  .listCardHeader {
    display: flex;
    justify-content: space-between;
    .select {
      width: 250px;
    }
  }
}
.toc {
  background: linear-gradient(180deg, #ddefff 0%, #ffffff 100%) no-repeat #fff;
}
.timeFrames {
  background: linear-gradient(180deg, #fff1ec 0%, #ffffff 100%) no-repeat #fff;
}
.differential {
  background: linear-gradient(180deg, #f2ffe2 0%, #ffffff 100%) no-repeat #fff;
}
.dut {
  background: linear-gradient(180deg, #fff7e2 0%, #ffffff 100%) no-repeat #fff;
}
.ssdw {
  background: linear-gradient(180deg, #f2ffe2 0%, #ffffff 100%) no-repeat #fff;
  .name {
    font-size: 16px;
  }
}
.mySwiper {
  overflow: hidden;
  :deep(.swiper-wrapper) {
    height: 230px;
  }
  .swiperSlide {
    // height: 25px !important;
    width: 100%;
    .swiperSlideItem {
      width: 100%;
      display: flex;
      align-content: center;
      justify-content: space-between;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      // margin-bottom: 13px;
    }
    .zc_swiperSlideItem_state {
      width: 40px;
      height: 24px;
      border-radius: 3px;
      border: 1px solid #00a870;
      font-size: 12px;
      color: #00a870;
      text-align: center;
      line-height: 24px;
    }
  }
}
.all {
  font-weight: 400;
  font-size: 12px;
  color: #2b2e3e;
  cursor: pointer;
  line-height: 20px;
}
.realTimeBox {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 230px;
  overflow-y: scroll;
  scrollbar-width: thin;
  padding-right: 5px;

  &::-webkit-scrollbar {
    width: 4px;
    background-color: #f5f5f5;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #266fe8;
  }

  .realTimeItem {
    font-size: 14px;
    margin-bottom: 10px;
    word-break: break-all;
    padding: 10px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border-left: 3px solid #266fe8;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    :deep(.el-icon) {
      color: #266fe8;
      font-size: 16px;
      margin-right: 10px;
    }

    .realtime-content {
      flex: 1;
      line-height: 1.4;
    }

    &:last-child {
      margin-bottom: 5px;
    }
  }
}
.deviceItem_normal {
  color: #078d5c !important;
}
.deviceItem_abnormal {
  color: #e34d59 !important;
}
.deviceItem_login {
  margin-left: 8px;
  color: #b8c6dd !important;
}
.titleName {
  margin-left: 60px;
  font-size: 10px;
  .titleItem {
    margin-left: 10px;
  }
  .yue {
    margin-left: 8px;
  }
  .ri {
    margin-left: 10px;
  }
  .shi {
    margin-left: 5px;
  }
  .fen {
    margin-left: 10px;
  }
  .miao {
    margin-left: 15px;
    margin-right: 10px;
  }
  .nami,
  .haom,
  .weim {
    margin-left: 7px;
  }
}
.company {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.26);
  line-height: 35px;
  position: absolute;
  left: 84px;
}
.lists {
  background: #fff;
  flex: 1;
  height: 686px;
}
.clean {
  height: 30px;
  line-height: 40px;
  text-align: center;
  color: #e34d59;
  font-size: 18px;
  cursor: pointer;
  position: absolute;
  bottom: 10px;
  right: 240px;
}
.tableHome {
  height: 540px;
  :deep(.is-leaf) {
    background: #fafafa;
  }
  :deep(.el-table__cell) {
    padding: 10px 0;
  }
}
.chartstate_chart {
  width: 226px;
}
.usability_box {
  width: 226px;
  height: 100%;
  position: relative;
  .usability {
    width: 98px;
    height: 100px;
    border-radius: 50%;
    border: 2px solid #266fe8;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.foodDatas {
  width: 100%;
  display: flex;
  overflow: hidden;
  .listDatas {
    margin-right: 12px;
    width: 70%;
    display: flex;
    .listCard {
      flex: 1;
      border-radius: 2px;
    }
    .listCard1 {
      border-radius: 0px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .listCardItem {
        display: flex;
        width: 100%;
        flex: 1;
        margin-top: 12px;
        background: #fff;
        border-radius: 2px;
        justify-content: space-between;
        padding: 24px 18px;
        position: relative;
        .listCardItem_nav {
          display: flex;
          height: 100%;
          flex-direction: column;
          justify-content: space-between;
          font-weight: 600;
          font-size: 22px;
          color: rgba(0, 0, 0, 0.85);
          width: 120px;
          .listCardItem_value {
            font-size: 34px;
          }
        }
        .listCardItem_img {
          width: 208px;
          height: 96px;
          margin: auto 0;
        }
      }
      .listCardItem:nth-child(1) {
        margin-top: 0;
      }
    }
    .listCard2 {
      margin: 0 12px;
    }
  }
  .newDatas {
    flex: 1;
    .listCard {
      width: 100%;
      height: 100%;
      :deep(.el-card__header) {
        padding: 8px 12px;
      }
    }
  }
}
.dialog {
  .el-row {
    margin-bottom: 20px;
    align-items: center;
  }
  .label {
    font-weight: bold;
    text-align: right;
    padding-right: 10px;
  }
  .value {
    text-align: left;
  }
}
.diff-details-container {
  padding: 20px;
  .diff-row {
    margin-bottom: 15px;
  }
  .diff-card {
    background: #f7f9fc;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    .diff-card-title {
      font-weight: 600;
      font-size: 16px;
      color: #266fe8;
      margin-bottom: 15px;
      position: relative;
      padding-left: 10px;
      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: #266fe8;
        border-radius: 2px;
      }
    }
  }
  .diff-item {
    display: flex;
    padding: 8px 0;
    border-bottom: 1px dashed #ebeef5;
    &:last-child {
      border-bottom: none;
    }
    .diff-label {
      width: 50%;
      font-size: 14px;
      color: #606266;
      padding-right: 10px;
    }
    .diff-value {
      width: 50%;
      font-weight: 500;
      font-size: 14px;
      color: #333;
    }
  }
  .el-divider {
    margin: 20px 0;
    &::before,
    &::after {
      border-color: #dcdfe6;
    }
    .el-divider__text {
      background-color: #f5f7fa;
      color: #266fe8;
      font-weight: 600;
      padding: 0 20px;
    }
  }
}
.diff-details-dialog {
  :deep(.el-dialog__header) {
    padding: 15px 20px;
    margin-right: 0;
    background: #266fe8;
    border-radius: 8px 8px 0 0;
    .el-dialog__title {
      color: #fff;
      font-weight: 600;
      font-size: 18px;
    }
    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #fff;
      }
    }
  }
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  :deep(.el-table) {
    --el-table-header-bg-color: #f2f6fc;
    --el-table-header-text-color: #606266;
    --el-table-row-hover-bg-color: #f5f7fa;

    th {
      font-weight: bold;
    }
    .el-table__cell {
      padding: 12px 0;
    }
  }
}
.integrity-list {
  position: absolute;
  width: 90%;
  top: 57px;
  left: 10px;
  .integrity-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
    padding: 0 10px;
    &:last-child {
      margin-bottom: 0;
    }
    &.integrity-row-single {
      justify-content: center;
      .integrity-item {
        width: 100%;
        text-align: center;
        justify-content: center;
      }
    }
    .integrity-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .label {
        color: #606266;
        margin-right: 5px;
        width: 108px;
        text-align: right;
      }
      .value {
        color: #266fe8;
        font-weight: 500;
        min-width: 30px;
      }
    }
  }
}
</style>
