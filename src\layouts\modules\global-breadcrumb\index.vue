<script setup lang="ts">
import { useThemeStore } from '@/store/modules/theme';
import { useRouteStore } from '@/store/modules/route';

defineOptions({
  name: 'GlobalBreadcrumb'
});

const themeStore = useThemeStore();
const routeStore = useRouteStore();

</script>

<template>
  <el-breadcrumb v-if="themeStore.header.breadcrumb.visible" separator="/">
    <el-breadcrumb-item 
      v-for="item in routeStore.breadcrumbs" 
      :key="item.key" 
      :to="{ path: item.routePath }"
    >
      {{ item.label }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<style scoped></style>
