<template>
  <lineChart class="chartstate_chart" ref="mainChart" :data="chartData" />
  <div class="con">
    <el-button type="primary" class="btn" @click="downloadImage"
      >下载文档</el-button
    >
    <!-- <img class="file-con" :src="HelpImage" /> -->
  </div>
</template>

<script setup >
import HelpImage from "@/assets/imgs/help.png";
import lineChart from "@/components/lineChart/lineChart.vue";
import { ref, nextTick } from "vue";
const mainChart = ref();
let datas = [];
function getRandomIntUpTo(max) {
  return Math.floor(Math.random() * (max + 1));
}
const now = new Date();
function getTimeAfterSeconds(seconds) {
  // 计算指定秒数后的时间
  const futureTime = new Date(now.getTime() + seconds * 1000);
  return futureTime;
}

for (let i = 0; i < 3080; i++) {
  let date = getTimeAfterSeconds(i);
  datas.push({
    name: date.toString(),
    value: [date, getRandomIntUpTo(100)],
  });
}

// 添加下载图片的方法
function downloadImage() {
  let myChart = mainChart.value.getIns();
  const link = document.createElement("a");
  link.href = myChart.getDataURL({
    type: "png", // 可以改为 "jpeg" 或其他格式
    pixelRatio: 2, // 图像质量
  });
  console.log("link.href", link.href);
  link.download = "chart.png"; // 下载的文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

const chartData = {
  toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: "none",
      },
      restore: {},
      saveAsImage: {},
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      animation: false,
      label: {
        backgroundColor: "#505765",
      },
    },
  },
  dataZoom: [
    {
      show: true,
      realtime: true,
      start: 0,
      end: 20,
    },
  ],
  xAxis: {
    type: "time",
  },
  yAxis: {
    type: "value",
    axisLine: {
      show: false, // 隐藏 y 轴轴线
    },
    splitLine: {
      show: false, // 隐藏 y 轴网格线
    },
  },
  series: [
    {
      type: "line",
      data: datas,
      showSymbol: false, // 默认情况下不显示标记
      symbolSize: 10, // 标记的大小
      itemStyle: {
        color: "#1d7bff",
      },
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(25, 120, 255, 0.2)", // 0% 处的颜色
            },
            {
              offset: 1,
              color: "rgba(255, 255, 255, 0)", // 100% 处的颜色
            },
          ],
        },
        origin: "start", // 从起点开始显示阴影
      },
      lineStyle: {
        width: 3, // Set the line width to 4 (or any other desired value)
        type: "solid",
      },
    },
  ],
};

nextTick(() => {
  // 假设你的 ECharts 实例为 myChart
  let myChart = mainChart.value.getIns();

  myChart.on("datazoom", function (event) {
    var startTime,
      endTime = "";
    console.log("event", event);
    // 获取 dataZoom 事件的相关信息
    if (event.batch) {
      startTime = new Date(event.batch[0].startValue);
      endTime = new Date(event.batch[0].endValue);
    } else if (event.end) {
      let startValue = datas[Math.ceil(datas.length * (event.start / 100))];
      let endValue = datas[Math.ceil(datas.length * (event.end / 100))];
      startTime = new Date(startValue.value[0]);
      endTime = new Date(endValue.value[0]);
    }

    console.log("开始时间:", startTime);
    console.log("结束时间:", endTime);
  });
});
</script>

<style lang="scss" scoped>
.con {
  background: #fff;
  border-radius: 2px;
  width: 100%;
  padding: 20px 38px;

  .btn {
    float: right;
    margin-top: 20px;
    margin-right: 38px;
  }
}
.file-con {
  padding: 0 120px;
  width: calc(100% - 40px);
  height: calc(100% - 40px);
  object-fit: contain;
}
.chartstate_chart {
  width: 100%;
  height: 500px;
}
</style>
