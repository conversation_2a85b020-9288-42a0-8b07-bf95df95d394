import { ref, reactive } from "vue";
import { chartColors } from "@/constants/chart";

const getRandomWaveList = () => {
  let list = [];
  for (let i = 0; i < 6; i++) {
    list.push(30 + Math.floor(Math.random() * 350));
  }
  return list;
};
let setchartData = (name) => {
  return {
    xAxis: {
      type: "category",
      data: ["11h", "12h", "13h", "14h", "15h", "16h"],
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,   // 显示 y 轴的分割线
        lineStyle: {
          color: '#f0f0f0',  // 设置分割线颜色为红色
          type: 'dashed'  // 设置分割线为虚线
        }
      },
      axisLabel: {
        formatter: "{value} ms", // Change '单位' to your desired unit name
      },
    },
    legend: {
      bottom: -10,
      itemStyle: { opacity: 0 },
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: '#2B2E3F',
        lineHeight: 20,
        fontStyle: 'normal',
      },
    },
    grid: {
      left: "1%",
      right: "1%",
      bottom: 20,
      top: "20%",
      containLabel: true,
    },
    series: [
      {
        data: getRandomWaveList(),
        name: '当前值',
        type: "line",
        showSymbol: false, // 默认情况下不显示标记
        symbolSize: 10, // 标记的大小
        itemStyle: {
          color: chartColors[0]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(25, 120, 255, 0.2)'  // 0% 处的颜色
            }, {
              offset: 1, color: 'rgba(255, 255, 255, 0)'  // 100% 处的颜色
            }],
          },
          origin: 'start'  // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: 'solid'
        },
      },
      {
        data: getRandomWaveList(),
        name: '预测值',
        type: "line",

        showSymbol: false, // 默认情况下不显示标记
        symbolSize: 10, // 标记的大小
        itemStyle: {
          color: chartColors[1]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(15, 197, 150, 0.2)'  // 0% 处的颜色
            }, {
              offset: 1, color: 'rgba(255, 255, 255, 0)'  // 100% 处的颜色
            }],
          },
          origin: 'start'  // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: 'solid'
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let result = params[0].name + '<br/>'; // X轴的内容（例如：'Mon', 'Tue', ...）
        params.forEach((param, index) => {
          result += param.seriesName + ': ' + `<span style='color:${chartColors[index]}'>${param.value}</span>` + ' %<br/>'; // 每个系列的值和单位
        });
        return result;
      }
    },
  }
}
export let diagramData = reactive({
  deviation: {
    name: "授时偏差",
    data: {
      ChartData: setchartData('授时偏差'),
      tableData: [
        {
          date: '2016-05-03',
          name: 'Tom',
        },
        {
          date: '2016-05-02',
          name: 'Tom',
        },
        {
          date: '2016-05-04',
          name: 'Tom',
        },
        {
          date: '2016-05-01',
          name: 'Tom',
        },
        {
          date: '2016-05-01',
          name: 'Tom',
        },
      ]
    }
  },
  strength: {
    name: "场强",
    data: {
      ChartData: {
        xAxis: {
          data: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00'],
          splitLine: {
            show: false  // 不显示 x 轴的分割线
          }
        },
        yAxis: {
          splitLine: {
            show: true,   // 显示 y 轴的分割线
            lineStyle: {
              color: '#f0f0f0',  // 设置分割线颜色为红色
              type: 'dashed'  // 设置分割线为虚线
            }
          }
        },
        legend: {
          bottom: 0,
          icon: 'rect',
          itemHeight: 2,
          itemStyle: { opacity: 1 },
          textStyle: {
            fontWeight: 400,
            fontSize: 12,
            color: '#2B2E3F',
            lineHeight: 20,
            fontStyle: 'normal',
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // // params 是鼠标悬停时的参数数组，每个元素代表一个数据项
            // var data = params[0].data; // 获取悬停时的数据项
            // console.log(params)
            // return ''
            //   + '' + params[0].axisValueLabel + '<br>'  // 自定义显示 X 轴数值
            //   + '场强:' + `<span class='synthesize_tooltip'>${data[1]}</span>` + 'dBm';
            // let result = params[0].name + '<br/>'; // X轴的内容（例如：'Mon', 'Tue', ...）
            let result = params[0].axisValueLabel + '<br>'; // X轴的内容（例如：'Mon', 'Tue', ...）
            params.forEach((param, index) => {
              if(index > 1) {
                return;
              }
              result += param.seriesName + ': ' + `<span style='color:${chartColors[index]}'>${param.data[1]}</span> dBm<br/>`; // 每个系列的值和单位
            });
            return result;
          }
        },
        series: [
          {
            symbolSize: 20,
            name: '当前值',
            color: chartColors[0],
            data: [
              [10.0, 8.04], [8.07, 6.95], [13.0, 7.58], [9.05, 8.81], [11.0, 8.33],
              [14.0, 7.66], [13.4, 6.81], [10.0, 6.33], [14.0, 8.96], [12.5, 6.82],
              [9.15, 7.2], [11.5, 7.2], [3.03, 4.23], [12.2, 7.83], [2.02, 4.47],
              [1.05, 3.33], [4.05, 4.96], [6.03, 7.24], [12.0, 6.26], [12.0, 8.84],
              [7.08, 5.82], [5.02, 5.68]
            ],
            type: 'scatter'
          },
          {
            symbolSize: 20,
            name: '预测值',
            color: chartColors[1],
            data: [
              [11.0, 9.04], [8.07, 6.95], [14.0, 6.58], [9.05, 8.81], [11.0, 8.33],
              [14.0, 7.66], [13.4, 6.81], [10.0, 6.33], [14.0, 8.96], [12.5, 6.82],
              [9.15, 7.2], [11.5, 7.2], [3.03, 4.23], [11.2, 8.83], [2.02, 4.47],
              [1.05, 3.33], [4.05, 4.96], [6.03, 7.24], [12.0, 6.26], [12.0, 8.84],
              [7.08, 5.82], [5.02, 5.68]
            ],
            type: 'scatter'
          }
        ]
      },
      tableData: [
        {
          date: '2016-05-03',
          name: 'Tom',
        },
        {
          date: '2016-05-02',
          name: 'Tom',
        },
        {
          date: '2016-05-04',
          name: 'Tom',
        },
        {
          date: '2016-05-01',
          name: 'Tom',
        },
      ]
    }
  },
  // signalNoise: {
  //   name: "信噪比",
  //   data: {
  //     ChartData: setchartData('信噪比'),
  //     tableData: [
  //       {
  //         date: '2016-05-03',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-02',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-04',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-01',
  //         name: 'Tom',
  //       },
  //     ]
  //   }
  // },
  // difference: {
  //   name: "包周差",
  //   data: {
  //     ChartData: setchartData('包周差'),
  //     tableData: [
  //       {
  //         date: '2016-05-03',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-02',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-04',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-01',
  //         name: 'Tom',
  //       },
  //     ]
  //   }
  // },
  // frequency: {
  //   name: "频率偏差",
  //   data: {
  //     ChartData: setchartData('频率偏差'),
  //     tableData: [
  //       {
  //         date: '2016-05-03',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-02',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-04',
  //         name: 'Tom',
  //       },
  //       {
  //         date: '2016-05-01',
  //         name: 'Tom',
  //       },
  //     ]
  //   }
  // }
})
