<template>
  <div class="con">
    <div class="filter-con">
      <el-form inline :model="form">
        <el-form-item
          :label="props.devList.tltle"
          v-if="props.isShowDevList"
          :class="props.isDevMulti ? 'item-multi-size' : 'item-size'"
        >
          <el-select
            v-model="form.devName"
            placeholder="请选择"
            :multiple="props.isDevMulti"
          >
            <el-option
              v-for="item in props.devList.data"
              :key="item[props.devList.type.key]"
              :label="item[props.devList.type.label]"
              :value="item[props.devList.type.value]"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="通道号"
          v-if="props.isThoroughfare"
          :class="props.isDevMulti ? 'item-multi-size' : 'item-size'"
        >
          <el-select v-model="form.channel" placeholder="请选择">
            <el-option
              v-for="item in props.thoroughfareList.data"
              :key="item[props.thoroughfareList.type.key]"
              :label="item[props.thoroughfareList.type.label]"
              :value="item[props.thoroughfareList.type.value]"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="props.channelList.tltle"
          v-if="props.isShowChannelList"
          :class="props.isChannelMulti ? 'item-multi-size' : 'item-size'"
        >
          <el-select
            v-model="form.transmitterId"
            placeholder="请选择"
            :multiple="props.isChannelMulti"
          >
            <el-option label="全部" value="all"></el-option>
            <el-option
              v-for="item in props.channelList.data"
              :key="item[props.channelList.type.key]"
              :label="item[props.channelList.type.label]"
              :value="item[props.channelList.type.value]"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="props.isShowTypeList"
          class="item-size"
          :label="props.typeList.tltle"
        >
          <el-select v-model="form.type" placeholder="请选择">
            <el-option
              v-for="item in props.typeList.data"
              :key="item[props.typeList.type.key]"
              :label="item[props.typeList.type.label]"
              :value="item[props.typeList.type.value]"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="date-item" label="时间选择">
          <el-date-picker
            v-model="form.date"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :disabled-date="disabledDate"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="search-btn"
            @click="
              emit('onSearch', {
                newType: props.filterConfigName,
                newVal: form,
                loopType: 'initChart',
              })
            "
            >查询</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button
            v-if="props.isSetComparison"
            type="primary"
            @click="
              emit('onSearch', {
                newType: props.filterConfigName,
                newVal: form,
                loopType: 'setComparison',
              })
            "
            >对比</el-button
          >
          <!-- <el-button type="primary" @click="onExport">查看对比数据</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <div class="bottom-con">
      <EChartComponent
        ref="mainChart"
        @onSearchEchart="onSearchEchart"
        :isdataZoom="props.isdataZoom"
      />
      <el-table
        v-if="props.istableColumnTow && tableName.tableData"
        :data="tableName.tableData || []"
        border
        style="width: 100%"
      >
        <el-table-column
          v-for="col in tableName.tableColumn || []"
          :key="col.name"
          v-bind="col"
        />
      </el-table>

      <el-table
        v-if="props.istableColumn"
        :data="data.tableData || []"
        border
        style="width: 100%"
      >
        <el-table-column
          v-for="col in data.tableColumn || []"
          :key="col.name"
          v-bind="col"
        />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import {
  reactive,
  ref,
  watch,
  defineProps,
  onMounted,
  nextTick,
  getCurrentInstance,
} from "vue";
import { chartColors } from "@/constants/chart";
import EChartComponent from "./EChartComponent.vue";
const emit = defineEmits(["onSearch"]);
const { proxy } = getCurrentInstance();
const mainChart = ref();

onMounted(() => {
  onSearch();
});

const onSearch = (newType, newVal, info = false) => {
  emit("onSearch", {
    newType: props.filterConfigName,
    newVal: { ...form },
    loopType: "initChart",
  });
  lodefrom = { ...form };
};

const props = defineProps({
  isShowDevList: {
    type: Boolean,
    required: false,
    default: false,
  },
  isDevMulti: {
    type: Boolean,
    required: false,
    default: false,
  },
  devList: {
    type: Object,
    required: false,
    default: () => {
      return {
        data: [],
        type: {},
        default: "",
      };
    },
  },
  isShowChannelList: {
    type: Boolean,
    required: false,
    default: false,
  },
  isChannelMulti: {
    type: Boolean,
    required: false,
    default: false,
  },
  channelList: {
    type: Object,
    required: false,
    default: () => {
      return {
        data: [],
        type: {},
        default: "",
      };
    },
  },
  isShowTypeList: {
    type: Boolean,
    required: false,
    default: false,
  },
  typeList: {
    type: Object,
    required: false,
    default: () => {
      return {
        data: [],
        type: {},
        default: "",
      };
    },
  },
  lineName: {
    type: String,
    required: false,
    default: "",
  },
  isWeather: {
    type: Boolean,
    required: false,
    default: false,
  },
  filterConfigName: {
    type: String,
    required: false,
    default: "",
  },
  istableColumn: {
    type: Boolean,
    required: false,
    default: false,
  },
  tableColumn: {
    type: Object,
    required: false,
    default: () => {
      return {};
    },
  },
  tableNameColumn: {
    type: Object,
    required: false,
    default: () => {
      return {};
    },
  },
  isThoroughfare: {
    type: Boolean,
    required: false,
    default: false,
  },
  thoroughfareList: {
    type: Object,
    required: false,
    default: () => {
      return {
        data: [],
        type: {},
        default: "",
      };
    },
  },
  isSetComparison: {
    type: Boolean,
    required: false,
    default: false,
  },
  isdataZoom: {
    type: Boolean,
    required: false,
    default: false,
  },
  istableColumnTow: {
    type: Boolean,
    required: false,
    default: false,
  },
});
const form = reactive({
  date: proxy.configure.setDefaultDates(),
  devName: props.devList.default,
  transmitterId: props.channelList.default,
  type: props.typeList.default,
  channel: props.thoroughfareList.default,
});
let lodefrom = reactive({ ...form });
// 设置from 值为多少
const setForm = (key, val) => {
  form[key] = val;
};
const disabledDate = (time) => {
  const currentDate = new Date();
  return time.getTime() > currentDate.getTime();
};

const onSearchEchart = (date) => {
  emit("onSearch", {
    newType: props.filterConfigName,
    newVal: { ...date },
    loopType: "onSearchEchart",
  });
};

// 数据
const data = reactive({
  tableColumn: props.tableColumn,
  tableData: [],
});

// 第二个表格
const tableName = reactive({
  tableColumn: props.tableNameColumn,
  tableData: [],
});
const setTableName = (data) => {
  tableName.tableData = data;
};

const onExport = () => {
  console.log("onExport", form.date);
};
const initChart = (dataChria, tlabtData) => {
  mainChart.value.initChart(dataChria);
  data.tableData = [];
  data.tableData = tlabtData;
};

const setSeres = (dataChria, tlabtData) => {
  mainChart.value.initChart(dataChria);
  data.tableData.push(tlabtData[0]);
};

defineExpose({
  setForm,
  initChart,
  setSeres,
  setTableName
});
</script>

<style lang="scss" scoped>
.con {
  :deep(.el-table) {
    margin-top: 10px;
    --el-table-border-color: #e8e8e8;

    .cell {
      padding: 0 38px;
    }

    .el-table__cell {
      padding: 8px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__cell {
      border-right: none;
    }

    .el-table__header {
      height: 46px;

      .cell {
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #fafafa;
      }
    }
  }

  .filter-con {
    height: 80px;
    background: #fff;
    border-radius: 2px;
    padding: 0 24px;
    display: flex;
    align-items: center;

    .item-size {
      width: 180px;
    }

    .item-multi-size {
      width: 440px;
    }

    .date-item {
      width: 467px;
    }

    .select-label {
      display: inline;
      margin: 0 10px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }

    .hide-select-label {
      display: none;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }

    .date-picker {
      width: 325px;
    }

    .date-type {
      width: 325px;
    }

    .search-btn {
      margin-left: 12px;
      margin-right: 4px;
    }
  }

  .card {
    margin-top: 16px;
    background: #ffffff;
    border-radius: 2px;

    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .weather-table {
      :deep(.el-table__header-wrapper) {
        display: none;
      }

      :deep(.el-table-fixed-column--left) {
        background-color: #fafafa;
      }

      :deep(.cell) {
        padding: 0 19px;
      }
    }
  }

  .bottom-con {
    width: 100%;
    margin-top: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 2px;

    .chartArea {
      width: 100%;
      height: 340px;
    }
  }
}
</style>
