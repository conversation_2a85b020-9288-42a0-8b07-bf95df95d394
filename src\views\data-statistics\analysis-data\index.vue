<template>
  <div class="oriData">
    <div class="oriData_nav">
      <div
        @click="setoriData('3')"
        :class="{
          oriData_nav_item: true,
          oriData_nav_info: mode.businessApp == '3',
        }"
      >
        <el-icon><Tickets /></el-icon><span>eLORAN参数测量软件</span>
      </div>
      <div
        @click="setoriData('4')"
        :class="{
          oriData_nav_item: true,
          oriData_nav_info: mode.businessApp == '4',
        }"
      >
        <el-icon><Tickets /></el-icon><span>多通道时差测量软件</span>
      </div>
    </div>
    <eloranChannel
      @onSeaechData="onSeaechData"
      @onFilePath="onFilePath"
      :configObj="configObj"
      v-if="mode.businessApp == '3'"
    />
    <multiChannel
      @onSeaechData="onSeaechData"
      @onFilePath="onFilePath"
      :configObj="configObj"
      v-else
    />
    <div class="oriData_data">
      <div class="data">
        <el-table
          :data="tableData"
          style="width: 100%"
          @selection-change="selectionChange"
        >
          <el-table-column align="center" type="selection" width="55" />
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="55"
          />
          <el-table-column
            align="center"
            property="displayName"
            label="报表名称"
            width="380"
          />
          <el-table-column
            align="center"
            property="dateType"
            label="报表类型"
          />
          <el-table-column align="center" label="统计周期">
            <template #default="scope">
              <span>{{
                scope.row.dateType == "日报"
                  ? scope.row.cycleEnd.split(" ")[0]
                  : scope.row.cycleStart.split(" ")[0] +
                    " 至 " +
                    scope.row.cycleEnd.split(" ")[0]
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            property="transmitterId"
            label="发播台"
          >
            <template #default="scope">
              <span>{{
                proxy.configure.thoroughfare([scope.row.transmitterId])
              }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleDelete(scope.row)"
              >
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          class="paginations"
          v-model:current-page="mode.page"
          v-model:page-size="mode.size"
          :page-sizes="[10, 20, 30, 40]"
          size="default"
          layout="total,sizes, prev, pager, next"
          :total="mode.total"
          @size-change="handlePageChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
    <el-dialog v-model="gridData.info" :title="gridData.title" width="1000">
      <iframe
        frameborder="0"
        class="iframeBox"
        width="900px"
        height="700px"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, getCurrentInstance, nextTick } from "vue";
import eloranChannel from "./_search/eloranChannel.vue";
import multiChannel from "./_search/multiChannel.vue";
import apiAjax from "@/api/index";
import dayjs from "dayjs";
import config from "@/api/config/index.js";
import { useDataManage } from "@/store/modules/data-manage";
import { localStg } from "@/utils/storage";
const store = useDataManage();
const { proxy } = getCurrentInstance();
//列表
const tableData = ref([]);
const mode = reactive({
  size: 10, // 条数
  page: 1, // 页数
  total: 0,
  businessApp: "3", //业务应用
});

const gridData = reactive({
  info: false,
  title: "",
  src: "",
});
const loderpoms = ref({}); // 记录上次搜索的参数
const filePathListId = ref([]); // 下载列表
//配置项
let configObj = reactive({});
onMounted(async () => {
  configObj = await store.init();
  getTableData({ ...mode, dateType: "日报" });
});
const dateType = {
  1: "年报",
  2: "季报",
  3: "月报",
  4: "周报",
  5: "日报",
};
// 去请求数据
const getTableData = async (modeData) => {
  loderpoms.value = modeData;
  let proms = "";
  Object.keys(modeData).forEach((i) => {
    if (
      modeData[i] &&
      i !== "placeholderDates" &&
      i !== "total" &&
      modeData[i] !== "all"
    ) {
      proms += `${i}=${modeData[i]}&`;
    }
  });
  proms = proms.slice(0, -1);
  let data = await apiAjax.get(`api/jnx/statistics/findPage?` + proms);
  tableData.value = data.content;
  mode.total = data.totalElements;
};

// 准备去查询数据
const onSeaechData = (val) => {
  const proms = { ...mode, ...val };
  getTableData(proms);
};

const disabledDate = (time) => {
  const currentDate = new Date();
  return time.getTime() > currentDate.getTime();
};
const setoriData = (name) => {
  mode.businessApp = name;
  // 重新搜索
  mode.page = 1;
  mode.size = 10;
  mode.total = 0;
  mode.dateType = "日报";
  getTableData(mode);
};
// 处理分页
const handlePageChange = () => {
  const proms = { ...loderpoms.value, ...mode };
  getTableData(proms);
};
const selectionChange = (val) => {
  filePathListId.value = [];
  val.forEach((i) => {
    filePathListId.value.push(i.id);
  });
};
//导出文件
const onFilePath = async () => {
  let idList = filePathListId.value.join();
  proxy.configure.download(
    config.proxySource["/Api"] + "/api/jnx/statistics/export?ids=" + idList
  );
};
// 查看文件
const handleDelete = (item) => {
  gridData.title = item.dataType;
  gridData.info = true;
  nextTick(() => {
    const iframe = document.querySelector(".iframeBox");
    var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    var xhr = new iframeDoc.defaultView.XMLHttpRequest(); //new iframeDoc.XMLHttpRequest();
    xhr.responseType = "blob";
    // 打开一个请求到你的服务器端点
    xhr.open(
      "GET",
      config.proxySource["/Api"] + "/api/jnx/statistics/export?ids=" + item.id
    );
    const token = localStg.get("token") || false;
    xhr.setRequestHeader("Authorization", token);
    xhr.onload = () => {
      const url = window.URL.createObjectURL(xhr.response); //将后端返回的blob文件读取出url
      iframe.src = url;
    };
    // 发送请求
    xhr.send();
  });
};
</script>

<style lang="scss" scoped>
.oriData {
  display: flex;
  flex-direction: column;
  .oriData_nav {
    background: #ffffff;
    display: flex;
    padding: 12px 16px;
    .oriData_nav_item {
      margin-right: 20px;
      display: flex;
      align-items: center;
      padding: 5px 12px;
      cursor: pointer;
    }
    .oriData_nav_info {
      background: #ecf2fe;
      border-radius: 3px;
      color: #0052d9;
    }
  }
  .oriData_select {
    background: #ffffff;
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    padding: 24px;
    .oriData_select_flex {
      display: flex;
      .oriData_select_item {
        display: flex;
        align-items: center;
        margin-left: 26px;
        .select {
          width: 200px;
          margin-left: 8px;
        }
      }
    }
    .btnList {
      display: flex;
      margin-left: auto;
      margin-top: 20px;
      .btn {
        width: 60px;
        height: 32px;
        background: #0052d9;
        border-radius: 3px;
        text-align: center;
        line-height: 32px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        margin-left: 16px;
        cursor: pointer;
      }
    }
  }
  .oriData_data {
    background: #ffffff;
  }
}
.state_mode {
  margin-left: auto;
  display: flex;
  border: 1px solid #0052d9;
  border-radius: 6px;
  overflow: hidden;
  .hand,
  .automatic {
    width: 88px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #0052d9;
    cursor: pointer;
  }
  .stateInfo {
    background: #0052d9;
    color: rgba(255, 255, 255, 0.9);
  }
}
.chartArea {
  width: 100%;
  height: 300px;
  margin-bottom: 16px;
}
.tableHome {
  .view {
    font-size: 14px;
    color: #0052d9;
  }
  :deep(.is-leaf) {
    background: #fafafa;
  }
  :deep(.el-table__cell) {
    padding: 10px 0;
  }
}
.paginations {
  margin: 16px;
  justify-content: end;
}
</style>
