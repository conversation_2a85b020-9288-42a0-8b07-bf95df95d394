<script setup lang="ts">
import { computed, getCurrentInstance } from "vue";
import { useThemeStore } from "@/store/modules/theme";
import { loginModuleRecord } from "@/constants/app";
import PwdLogin from "./modules/pwd-login.vue";
import CodeLogin from "./modules/code-login.vue";
import Register from "./modules/register.vue";
import ResetPwd from "./modules/reset-pwd.vue";
import BindWechat from "./modules/bind-wechat.vue";
const { proxy } = getCurrentInstance();
interface Props {
  /** The login module */
  module?: UnionKey.LoginModule;
}

const props = defineProps<Props>();

const themeStore = useThemeStore();

const moduleMap = {
  "pwd-login": { label: loginModuleRecord["pwd-login"], component: PwdLogin },
  "code-login": {
    label: loginModuleRecord["code-login"],
    component: CodeLogin,
  },
  register: { label: loginModuleRecord.register, component: Register },
  "reset-pwd": { label: loginModuleRecord["reset-pwd"], component: ResetPwd },
  "bind-wechat": {
    label: loginModuleRecord["bind-wechat"],
    component: BindWechat,
  },
};

const activeModule = computed(() => moduleMap[props.module || "pwd-login"]);
</script>

<template>
  <div class="relative size-full overflow-hidden bg">
    <NCard :bordered="false" class="relative box">
      <div class="box-con">
        <div class="header">
          <span class="big-title"> 单台站性能评估系统系统 </span>
          <span class="small-title">
            {{ proxy.configure.stationAllLoginLong }}
          </span>
        </div>
        <div class="form-con">
          <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
            <component :is="activeModule.component" />
          </Transition>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style lang="scss" scoped>
/* 当屏幕宽度大于1600px时，应用以下样式 */
.box {
  max-width: 695px;
  max-height: 627px;
  width: 36.2%;
  height: 58.06%;
  margin-right: 13.6%;
  background: #ffffff;
  box-shadow: 3px 20px 40px 0px rgba(0, 32, 122, 0.15);
  border-radius: 8px;

  .box-con {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 46px;

    .header {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .big-title {
        font-weight: 600;
        font-size: 48px;
        color: #0c1e51;
        line-height: 67px;
        text-align: left;
        font-style: normal;
        margin-bottom: 10px;
        text-align: center;
      }

      .small-title {
        font-weight: 400;
        font-size: 27px;
        color: #0c1e51;
        line-height: 38px;
        text-align: left;
        font-style: normal;
        text-align: center;
      }
    }

    .form-con {
      margin-top: 72px;
      max-width: 506px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }
  }
}
.bg {
  background-image: url(/src/assets/login/login_bg.png);
  background-size: cover;
  background-repeat: no-repeat;

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

/* 当屏幕宽度大于1000px，小于或等于1600px时，应用以下样式 */
@media (max-width: 1600px) {
  .box {
    max-width: 695px;
    width: 36.2%;
    height: 380px;
    margin-right: 13.6%;
    background: #ffffff;
    box-shadow: 3px 20px 40px 0px rgba(0, 32, 122, 0.15);
    border-radius: 8px;

    .box-con {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding-top: 10px;

      .header {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .big-title {
          font-weight: 600;
          font-size: 28px;
          color: #0c1e51;
          line-height: 33px;
          text-align: left;
          font-style: normal;
          margin-bottom: 10px;
          text-align: center;
        }

        .small-title {
          font-weight: 400;
          font-size: 18px;
          color: #0c1e51;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-align: center;
        }
      }

      .form-con {
        margin-top: 36px;
        max-width: 506px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
      }
    }
  }
}

/* 当屏幕宽度小于或等于1000px时，应用以下样式 */
@media (max-width: 1000px) {
  .bg {
    justify-content: center;
  }
  .box {
    max-width: 695px;
    max-height: 627px;
    width: 36.2%;
    height: 370px;
    min-width: 360px;
    margin: 0;
    background: #ffffff;
    box-shadow: 3px 20px 40px 0px rgba(0, 32, 122, 0.15);
    border-radius: 8px;

    .box-con {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding-top: 10px;

      .header {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .big-title {
          font-weight: 600;
          font-size: 28px;
          color: #0c1e51;
          line-height: 33px;
          text-align: left;
          font-style: normal;
          margin-bottom: 10px;
          text-align: center;
        }

        .small-title {
          font-weight: 400;
          font-size: 18px;
          color: #0c1e51;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-align: center;
        }
      }

      .form-con {
        margin-top: 36px;
        max-width: 506px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
      }
    }
  }
}
</style>
