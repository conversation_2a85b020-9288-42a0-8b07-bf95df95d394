import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { SetupStoreId } from '@/enum';
import { useRouterPush } from '@/hooks/common/router';
// import { fetchGetUserInfo, fetchLogin } from '@/service/api';
import { localStg } from '@/utils/storage';
import { $t } from '@/locales';
import { useRouteStore } from '../route';
import { useTabStore } from '../tab';
import { clearAuthStorage, getToken } from './shared';
import { useRouter } from 'vue-router';
import { router as globalRouter } from '@/router';
import { userLogin } from '@/service/api';
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    userId: '',
    userName: '',
    roles: [],
    buttons: []
  });

  const isMockLogin = false;  //在测试没有网络的时候  使用mock数据  不发请求走mock数据

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore();

    clearAuthStorage();

    authStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, redirect = true) {
    if (isMockLogin) {
      if (userName == 'admin' && password == 'admin') {
        startLoading();
        const loginToken: any = {
          token: 'test',
          refreshToken: 'test'
        }
        await loginByToken(loginToken);
        await routeStore.initAuthRoute();
        if (redirect) {
          await redirectFromLogin();
        }

        endLoading();
        return;
      } else {
        ElMessage.error('账号密码错误')
      }

    }

    startLoading();
    const res = await userLogin(userName, password);

    const loginToken: any = {
      token: res,
      "refreshToken": res
    }
    let error = null;
    if (!error) {
      const pass = await loginByToken(loginToken);
      if (pass) {
        await routeStore.initAuthRoute();
        if (redirect) {
          await redirectFromLogin();
        }
        // if (routeStore.isInitAuthRoute) {
        //   window.$notification?.success({
        //     title: $t('page.login.common.loginSuccess'),
        //     content: $t('page.login.common.welcomeBack', { userName: userInfo.userName }),
        //     duration: 4500
        //   });
        // }
      }
    } else {
      resetStore();
    }
    endLoading();
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    localStg.set('refreshToken', loginToken.refreshToken);


    // 2. get user info
    const pass = await getUserInfo();

    if (pass) {
      token.value = loginToken.token;

      return true;
    }

    return true;
  }

  async function getUserInfo() {
    // 这里是获取 用户信息的 权限
    // lizheng todo
    // const { data: info, error } = await fetchGetUserInfo();
    let info = {
      "userId": "0", // 用户id
      "userName": "admin",
      "roles": [
        "R_SUPER" //用户的权限等级
      ],
      "buttons": [
        //import { useAuth } from '@/hooks/business/auth';
        //const { hasAuth } = useAuth();
        // "B_CODE1", v-if="hasAuth('B_CODE1')"  按钮的等级
        "B_CODE2",
        "B_CODE3"
      ]
    }
    let error = null;
    if (!error) {
      // update store
      Object.assign(userInfo, info);

      return true;
    }

    return false;
  }

  async function initUserInfo() {
    const hasToken = getToken();

    if (hasToken) {
      const pass = await getUserInfo();

      if (!pass) {
        resetStore();
      }
    }
  }

  return {
    token,
    userInfo,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    initUserInfo
  };
});
