_jsload2&&_jsload2('drawbysvg', 'function Cg(a){this.B=a;this.NG="http://www.w3.org/2000/svg";this.TU={strokeweight:"stroke-width",strokecolor:"stroke",fillcolor:"fill",strokeopacity:"stroke-opacity",fillopacity:"fill-opacity",strokestyle:"stroke-dasharray"};this.hf=this.Zz();this.Mb="svg"}Cg.prototype=new B.jz;var Dg=Cg.prototype; Dg.Zz=function(){var a=this.hf=L("svg",{},this.NG);a.setAttribute("version","1.1");a.setAttribute("type","system");a.style.position="absolute";this.IF();this.B.Tf().Et.appendChild(a);z.M(a,H()?"touchstart":"mousedown",Cb);return a}; Dg.IF=function(){if(this.hf){var a=this.hf,b=this.B,c=b.K.lx,e=b.width+2*c,f=b.height+2*c,g=-b.offsetX-c,b=-b.offsetY-c;this.setAttribute(a,"x",e+"px");this.setAttribute(a,"y",f+"px");this.setAttribute(a,"viewBox",g+" "+b+" "+e+" "+f);a=a.style;a.top=b+"px";a.left=g+"px";a.width=e+"px";a.height=f+"px"}};Dg.H3=w("hf");Dg.setAttribute=function(a,b,c,e){if(a)return"strokestyle"==b&&(c="solid"==c?0:2*e),a.setAttributeNS(s,this.Wq(b),c||"none"),a};Dg.Wq=function(a){return this.TU[a]||a}; Dg.Ao=function(){var a=L("path",{},this.NG);this.setAttribute(a,"stroke-linejoin","round");this.setAttribute(a,"stroke-linecap","round");this.setAttribute(a,"fill-rule","evenodd");this.hf.appendChild(a);return a};Dg.ke=function(a,b){var c=this.tA(b)||"M -9999,-9999";this.setAttribute(a,"d",c)};Dg.tA=function(a){if(0==a.length)return"";var b=[];z.mc.Fb(a,function(a){if(!(2>a.length)){b.push("M "+a[0].x+" "+a[0].y+" L");for(var e=1,f=a.length;e<f;e++)b.push(a[e].x),b.push(a[e].y)}});return b.join(" ")}; B.ZP=Cg; ');