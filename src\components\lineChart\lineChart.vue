<template>
  <div class="con">
    <div v-show="infos" style="width: 100%; height: 100%">
      <v-chart
        ref="chartRef"
        autoresize
        class="chart"
        :option="props.data"
      ></v-chart>
    </div>
    <div v-show="!infos" class="loading-state">
      <span class="loading-text">图形数据正在加载中</span
      ><span class="dots"></span>
    </div>
  </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref, nextTick } from "vue";

const props = defineProps({
  data: {
    type: Object,
    required: false,
    default: {},
  },
  objectSpanMethod: {
    type: Function,
    required: false,
    default: null,
  },
});
const chartRef = ref(null);
let chartInstance = null;
const initAllListeners = () => {
  chartInstance.on("legendselectchanged", (event) => {
    const selectedData = event.selected;
    const tableData = props.data.tableData || [];
    for (let key in selectedData) {
      const selected = selectedData[key];
      const tableItemList = tableData.filter((item) => item.name === key);
      if (tableItemList.length) {
        tableItemList.forEach((tableItem) => {
          tableItem.show = !selected;
        });
      }
    }
  });

  window.addEventListener("resize", resize);
};

const removeAllListeners = () => {
  if (chartInstance) chartInstance.off("legendselectchanged");
  window.removeEventListener("resize", resize);
};

const resize = () => {
  if (chartInstance) chartInstance.resize();
};

const getIns = () => {
  return chartInstance;
};

const clear = () => {
  removeAllListeners();
  if (chartInstance && !chartInstance.isDisposed) {
    chartInstance.dispose();
    chartInstance = null;
  }
};

const infos = ref(false);
const setInfos = () => {
  infos.value = true;
};

// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  clear,
  resize,
  getIns,
  initAllListeners,
  setInfos,
});

onMounted(() => {
  chartInstance = chartRef.value.chart;
  initAllListeners();
});

onBeforeUnmount(() => {
  clear();
});
</script>

<style lang="scss" scoped>
.con {
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 100%;
}
:global(.el-table .hidden-row) {
  display: none;
}
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #888;
  font-size: 16px;
  font-weight: 500;
}

.dots::after {
  content: ".";
  animation: dots 1.4s linear infinite;
  display: inline-block;
  width: 3em;
  text-align: left;
}

@keyframes dots {
  0%,
  20% {
    content: ".";
  }
  40% {
    content: "..";
  }
  60%,
  100% {
    content: "...";
  }
}
</style>
