<template>
  <div class="con">
    <el-card class="card">
      <template #header>
        <div class="card-header">
          <span>实时气象数据</span>
        </div>
      </template>

      <div class="weather-card-con">
        <FlipClock />

        <div class="list-con">
          <div
            v-for="item in data.weatherData || []"
            :key="item.name"
            class="card"
          >
            <div :class="`title ${getTitleByName(item.name)}`">
              {{ item.name }}
            </div>
            <div class="content">
              <div class="img-con">
                <img class="img" :src="item.img" />
              </div>
              <div class="text">{{ item.value || "--" }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="card bottom-card">
      <div class="con">
        <img class="map" :src="mapIcon" />

        <el-table
          :data="data.tableData || []"
          border
          max-height="449"
          style="width: calc(100% - 670px)"
        >
          <el-table-column
            v-for="col in data.tableColumn || []"
            :key="col.name"
            :width="col.width"
            v-bind="col"
          >
            <template #default="scope">
              <span v-if="col.prop == 'avgWindDirection'">
                <span>{{ dirctionObj[scope.row[col.prop]] }}</span>
              </span>
              <span v-else>{{ scope.row[col.prop] }} {{ col.unit }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import FlipClock from "@/components/clock/index.vue";
import {
  onMounted,
  onUnmounted,
  reactive,
  watch,
  getCurrentInstance,
} from "vue";
import { initScocket, subscribe, unsubscribe, unsubscribeAll } from "@/api/ws";
import weatherIcon1 from "@/assets/row-data/weather-data/weather1.png";
import weatherIcon2 from "@/assets/row-data/weather-data/weather2.png";
import weatherIcon3 from "@/assets/row-data/weather-data/weather3.png";
import weatherIcon4 from "@/assets/row-data/weather-data/weather4.png";
import weatherIcon5 from "@/assets/row-data/weather-data/weather5.png";
import weatherIcon6 from "@/assets/row-data/weather-data/weather6.png";
import mapIcon from "@/assets/row-data/weather-data/map.png";
import dayjs from "dayjs";
import apiAjax from "@/api/index";
const { proxy } = getCurrentInstance();
//获取天气的历史数据
const getLodeData = async () => {
  let data = await apiAjax.get(
    `/api/jnx/data/findHistory?startTime=${proxy.configure.getTimestampTwoMinutesAgo(
      -1400,
    )}&endTime=${proxy.configure.getUtcDate()}&dataType=aws_form&value=1&twSize=10&page=1&pageNum=300`,
  );
  if (data) {
    oriData.tableData = data;
  }
};
let oriData = reactive({
  weatherData: [
    {
      unit: "°C",
      tag: "temperature",
      name: "温度",
      value: "--",
      img: weatherIcon1,
    },
    { unit: " %", tag: "humidity", name: "湿度", value: "", img: weatherIcon2 },
    {
      unit: " m/s",
      tag: "windSpeed",
      name: "风速",
      value: "--",
      img: weatherIcon3,
    },
    {
      unit: "",
      tag: "windDirection",
      name: "风向",
      value: "--",
      img: weatherIcon4,
    },
    {
      unit: "mm",
      tag: "rainfall",
      name: "降雨量",
      value: "--",
      img: weatherIcon5,
    },
    {
      tag: "airPressure",
      name: "大气压",
      value: "--",
      img: weatherIcon6,
      unit: " kPa",
    },
  ],
  tableColumn: [
    {
      prop: "timeRangeKey",
      align: "left",
      label: "时间",
      width: 180,
      unit: "",
    },
    {
      prop: "avgTemperature",
      align: "center",
      label: "温度",
      unit: "°C",
      width: 130,
    },
    {
      prop: "avgHumidity",
      align: "center",
      label: "湿度",
      unit: "%",
      width: 130,
    },
    {
      prop: "avgWindSpeed",
      align: "center",
      label: "风速",
      unit: " m/s",
      width: 100,
    },
    {
      prop: "avgWindDirection",
      align: "center",
      label: "风向",
      unit: "",
      width: 90,
    },
    {
      prop: "avgRainfall",
      align: "center",
      label: "降雨量",
      unit: "mm",
      width: 135,
    },
    {
      prop: "avgAirPressure",
      align: "center",
      label: "大气压",
      unit: " kPa",
    },
  ],
  tableData: [],
});
let dirctionObj = {
  0: "北",
  1: "东北",
  2: "东",
  3: "东南",
  4: "南",
  5: "西南",
  6: "西",
  7: "西北",
};
const windList = [
  "东风",
  "南风",
  "西风",
  "北风",
  "东南风",
  "东北风",
  "西南风",
  "西北风",
];
const data = reactive(oriData);

const getTitleByName = (name) => {
  switch (name) {
    case "温度":
      return "title-color1";
    case "湿度":
      return "title-color2";
    case "风速":
      return "title-color3";
    case "风向":
      return "title-color4";
    case "降雨量":
      return "title-color5";
    case "大气压":
      return "title-color6";
    default:
      return "title-color1";
  }
};

// 获取当前的温度等数据
const getLodeDataone = async () => {
  let data = await apiAjax.get(
    "/api/jnx/data/findHistory?dataType=aws_real&endTime=2024-08-07T13%3A34%3A56Z&offset=1&page=1&pageNum=10&startTime=2024-08-07T13%3A33%3A56Z&twSize=1920&value=1",
  );
  if (data[0]) {
    oriData.weatherData.forEach((i) => {
      if (data[0][i.tag]) {
        i.value = data[0][i.tag] + i.unit;
      }
    });
    oriData.weatherData[3].value =
      dirctionObj[data[0]["windDirection"].split(".")[0]];
  }
};

onMounted(async () => {
  await initScocket();
  subscribe("/topic/jsInfo/tcpaws/TCP-DTZ-AWS", setoriData);
  // 获取当前的温度等数据
  getLodeDataone();
  getLodeData();
});
const setoriData = (obj) => {
  let newData = JSON.parse(obj);
  Object.keys(newData).forEach((i) => {
    let newDatas = data.weatherData.find((item) => item.tag == i);
    if (newDatas) {
      newDatas.value = newData[i] + newDatas.unit;
    }
  });
  data.weatherData[3].value =
    dirctionObj[data.weatherData[3].value.split(".")[0]];
};
onUnmounted(() => {
  unsubscribeAll();
});
</script>

<style lang="scss" scoped>
.con {
  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
  }

  .card {
    margin-bottom: 16px;
    height: 396px;
    background: #ffffff;
    border-radius: 2px;
  }

  .bottom-card {
    height: 545px;

    .con {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-around;

      .map {
        width: 607px;
        height: 449px;
      }

      :deep(.el-table) {
        --el-table-border-color: #e8e8e8;
        .el-table__cell {
          padding: 16px 0;
          font-weight: 600;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          line-height: 22px;
          font-style: normal;
        }

        .el-table__cell {
          border-right: none;
        }

        .el-table__header {
          height: 54px;

          .cell {
            font-weight: 600;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 22px;
            font-style: normal;
          }

          th.el-table__cell {
            background-color: #fafafa;
          }
        }
      }
    }
  }

  .card-header {
    font-weight: 600;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }

  .weather-card-con {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .list-con {
      margin-top: 18px;
      width: 100%;
      display: flex;
      justify-content: space-between;

      .card {
        width: 261px;
        height: 207px;
        background: #f5fafd;
        box-shadow: 0px 1px 6px 3px rgba(0, 56, 135, 0.14);
        border-radius: 10px;
        border: 3px solid #ffffff;

        .title {
          width: 185px;
          height: 41px;
          border-radius: 20px 0px 100px 20px;
          padding: 4px 15px;
          font-weight: 600;
          font-size: 23px;
          color: #fff;
          line-height: 32px;
          text-align: left;
          font-style: normal;
        }

        .title-color1 {
          background: linear-gradient(270deg, #ffffff 0%, #fcba04 100%);
        }

        .title-color2 {
          background: linear-gradient(270deg, #ffffff 0%, #3edf78 100%);
        }

        .title-color3 {
          background: linear-gradient(270deg, #ffffff 0%, #649eff 100%);
        }

        .title-color4 {
          background: linear-gradient(270deg, #ffffff 0%, #ff9784 100%);
        }

        .title-color5 {
          background: linear-gradient(270deg, #ffffff 0%, #54ccbd 100%);
        }

        .title-color6 {
          background: linear-gradient(270deg, #ffffff 0%, #69c5e0 100%);
        }

        .content {
          margin-top: 9px;
          margin-left: 4px;
          margin-right: 4px;
          display: flex;
          align-items: center;

          .img-con {
            width: 130px;
            height: 132px;
            margin-right: 4px;
            // border: 1px solid #bbb;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .text {
            font-weight: 600;
            font-size: 26px;
            color: #4b5860;
            line-height: 37px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }
}
</style>
