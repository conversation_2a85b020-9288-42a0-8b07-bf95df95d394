import { transformRecordToOption } from '@/utils/common';

export const yesOrNoRecord: Record<CommonType.YesOrNo, App.I18n.I18nKey> = {
  Y: 'common.yesOrNo.yes',
  N: 'common.yesOrNo.no'
};

export const yesOrNoOptions = transformRecordToOption(yesOrNoRecord);


export const devNameMap: {[key: string]: string} = {
  '6000M': '6000M 蒲城',
  '6250M': '那曲授时台(6250M)',
  '7600M': '库尔勒授时台(7600M)',
  '8750M': '敦煌授时台(8750M)',
  '7430M': '7430M 荣成',
  '7430X': '7430X 宣城',
  '7430Y': '7430Y 和龙',
  '8390M': '8390M 宣城',
  '8390X': '8390X 饶平',
  '8390Y': '8390Y 荣成',
  '6780M': '6780M 贺州',
  '6780X': '6780X 饶平',
  '6780Y': '6780Y 崇左',
};

// key值'CH1'|'CH2'
export const getDevMapName = (key: string) => devNameMap[key] || key || '';
