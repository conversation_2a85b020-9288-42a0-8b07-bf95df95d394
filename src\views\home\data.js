import { ref, reactive } from "vue";
import dayjs from 'dayjs';
import configure from '@/utils/configure.js'
import apiAjax from "@/api/index";
import TopoIcon1 from "@/assets/topo/receiver.png";
import TopoIcon2 from "@/assets/topo/eq.png";
import { usabilityChart, continuityChart } from '@/utils/chart.js';
import {
  subscribe,
  unsubscribe,
} from "@/api/ws";
export let transmitters = ref('') // 站台主台

const objRef = ref({})  // 存储图表的数据ref
const loodkey = ref("fieldstrength") // 存储图表的key

// 获取主台
export const getTransmitters = async (ObjRef) => {
  objRef.value = ObjRef;
  const data = await apiAjax.get(`/api/jnx/data/findHistory?startTime=${configure.getTimestampTwoMinutesAgo(1440)}&endTime=${configure.getUtcDate()}&dataType=receiverId_channel1&value=1&twSize=3600&page=1&pageNum=300`)
  if (data) {
    transmitters.value = data[0].channel1;
  }
  homeNavScoket(transmitters.value);
  // 获取授时偏差 历史数据
  await getChartData({ dataType: "fbtyxd_real", value: "tser", chartData: deviationObj, domRef: objRef.value.deviationRef });
  subscribeChartData({
    url: "/topic/jsInfo/syrealtimedatadtd/DTD-DTZ-FBTYXD:", key: "tser", chartData: deviationObj, domRef: objRef.value.deviationRef, fn: seToc
  })
  // 获取场强 历史数据
  loodkey.value = "fieldStrength"
  await getChartData({ dataType: "yxjcd_real", value: "fieldstrength", chartData: diagramData.fieldStrength, domRef: objRef.value.fieldStrength });
  subscribeChartData({ url: "/topic/jsInfo/syrealtimedataeloran/ELORAN-DTZ-YXJCD:", key: "fieldStrength", chartData: diagramData.fieldStrength, domRef: objRef.value.fieldStrength })
}
// 第一列 里面的 5个数据
export let comparisonData = reactive({
  timeFrames: {
    className: 'timeFramesbd',
    name: "时码比对",
    referenceName: "参考值",
    actualName: "接收值",
    state: true,
  },
  ssdw: {
    className: 'ssdwbd',
    name: "授时电文比对",
    referenceName: "参考值",
    actualName: "接收值",
    state: true,
  },
  toc: {
    className: 'tocdb',
    name: "TOC比对",
    content: "",
    value: 0,
    state: 0,
    number: 0,
    tocTimeMark: ''
  },
  differential: {
    className: 'differentialbd',
    name: "差分修正量比对",
    referenceName: "参考值",
    actualName: "接收值",
    state: false,
    diffId: "",//差分站Id
    diffCorStatus: "",//差分修正状态
    asfck: "",//ASF(参考)
    diffStatusck: "",//差分状态 (参考)
    diffForecastck: "",//差分模型起点时刻 (参考)
    diffA0ck: "",//差分改正参数a0(参考)
    diffA1ck: "",//差分改正参数a1(参考)
    dataAgeck: "",//差分改正参数a1
    asfyb: "",//ASF(预报)
    diffStatusyb: "",//差分站状态(预报)
    diffForecastyb: "",//差分模型起点时刻(预报)
    diffA0yb: "",//差分改正参数a0(预报)
    diffA1yb: "",//差分改正参数a1(预报)
  },
  dut: {
    className: 'dutbd',
    name: "DUT1比对",
    referenceName: "参考值",
    actualName: "接收值",
    state: true,
  },
})

// 初始化图标的数据
export const setChartData = (unit) => {
  return {
    title: {
      text: `单位：${unit}`,
      left: "left",
      top: "-5px",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    xAxis: {
      type: "time",
      axisLabel: {
        interval: 0, // 显示所有标签
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false, // 隐藏 y 轴轴线
      },
      splitLine: {
        show: false, // 隐藏 y 轴网格线
      },
      min: 20.0,
      max: 20.2,
    },
    legend: {
      bottom: 4,
      itemHeight: 4,
      icon: "rect",
      textStyle: {
        fontWeight: 400,
        fontSize: 12,
        color: "#2B2E3F",
        lineHeight: 20,
        fontStyle: "normal",
      },
    },
    grid: {
      left: "60px",
      right: "35px",
      top: "30px",
      bottom: "50px",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    series: [
      {
        type: "line",
        data: [],
        showSymbol: false, // 默认情况下不显示标记
        symbolSize: 10, // 标记的大小
        itemStyle: {
          color: "#1d7bff",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(25, 120, 255, 0.2)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(255, 255, 255, 0)", // 100% 处的颜色
              },
            ],
          },
          origin: "start", // 从起点开始显示阴影
        },
        lineStyle: {
          width: 3, // Set the line width to 4 (or any other desired value)
          type: "solid",
        },
        // notMerge: true, // 重新设置整个图表的配置
        // animation: false, // 禁用动画
      },
    ],
  }
}

export let diagramData = reactive({
  fieldStrength: {
    name: "场强",
    chartDataList: [],// 数据纯在的地方
    tableData: [],
    max: 20,
    min: 0,
    chartData: setChartData('dBμV/m'),
    key: "fieldstrength",
  },
  snr: {
    name: "信噪比",
    chartDataList: [],// 数据纯在的地方
    max: 20,
    min: 0,
    tableData: [],
    chartData: setChartData('dB'),
    key: "snr",
  },
  packageTD: {
    name: "包周差",
    chartDataList: [],// 数据纯在的地方
    max: 20,
    min: 0,
    tableData: [],
    chartData: setChartData('μs'),
    key: "packagetd",
  },
  frequencyTD: {
    name: "频率偏差",
    chartDataList: [],// 数据纯在的地方
    max: 20,
    min: 0,
    tableData: [],
    chartData: setChartData('Hz'),
    key: "frequencytd",
  },
})
export let deviationObj = reactive({
  name: "授时偏差",
  chartDataList: [],// 数据纯在的地方
  max: 20,
  min: 0,
  chartData: setChartData('ns'),
  tableData: [],
})
export let chartListState = reactive({
  continuity: {
    name: "连续性",
    chartData: continuityChart(0),
    state: true,
    value: "0%"
  },
  usability: {
    name: "可用性",
    chartData: usabilityChart(0),
    state: true,
    value: "0%"
  },
  integrity: {
    name: "完好性",
    value: {
      tserAlarms: "--",// 授门
      sj_tser: "--",// 授告
      fieldStrengthAlarms: "--",// 场门
      sj_fieldStrength: "--",// 场告
      totalAlarms: "--",// 总门
      sj_totalAlarms: "--",// 总告
      type1Alarms: "--",// 一级告警
    }
  },
  accuracy: {
    name: "准确性",
    state: true,
    value: "0"
  },
  blockingRate: {
    name: "阻断率",
    state: true,
    value: "0"
  },
  peakPowerRate: {
    name: "峰值有效功率",
    state: true,
    value: "0"
  }
})
// 设置当前的toc 的值
let seToc = (value) => {
  console.log("seToc", value);
  comparisonData.toc.content = configure.thoroughfare(value.transmitter);
  comparisonData.toc.state = value.tocResult;
  comparisonData.toc.value = value.toc;
  comparisonData.toc.tocTimeMark = dayjs(configure.setDate(value.tocTimeMark)).format('YYYY-MM-DD HH:mm:ss');
}
// 计算最大最小值
const setMaxMin = (list) => {
  let max = Math.max(...list.map((i) => i.value[1]));
  let min = Math.min(...list.map((i) => i.value[1]));
  if (max == min) {
    max = max + max * 0.2;
    min = 0;
  } else {
    max = max + (max - min) + 5;
    min = min - (max - min) - 5;
  }
  max = Math.ceil(max * 1000) / 1000;
  min = Math.ceil(min * 1000) / 1000;
  return { max, min }
}

// 订阅图形实时数据
export const subscribeChartData = (obj) => {
  subscribe(obj.url + obj.key + ":" + transmitters.value, (data) => {
    // 先计算最值
    let { max, min } = obj.chartData;
    let value = data.value - 0;
    if (value > max) {
      max = value + (value - min) + 5;
    }
    if (value < min) {
      min = value - (max - value) - 5;
    }
    obj.chartData.max = max;
    obj.chartData.min = min;

    // 再计算图表数据
    if (obj.chartData.chartDataList.length > 3600) {
      obj.chartData.chartDataList.shift()
    }
    const date = new Date(data.timeSeconds * 1000);
    obj.chartData.chartDataList.push({
      name: date.toString(),
      value: [date, value],
    })
    obj.domRef.setInfos()
    obj.domRef.getIns().setOption({
      yAxis: {
        max: max,
        min: min,
      },
      grid: { left: (max + "").length * 7 + 25 + "px" },
      series: [{ data: obj.chartData.chartDataList, name: obj.chartData.name }],
    });
    if (obj.fn) {
      obj.fn(data)
    }
  });
}

// 获取图表的历史数据
export const getChartData = async (obj) => {
  const newData = await apiAjax.get(`/api/jnx/data/findHistory?startTime=${configure.getTimestampTwoMinutesAgo(1400)}&endTime=${configure.getUtcDate()}&dataType=${obj.dataType}&value=${obj.value}&offset=1&twSize=10&page=1&pageNum=300&transmitter=${transmitters.value}`);
  const data = newData[0];
  if (Object.keys(data).length > 0 && data[obj.dataType] && data[obj.dataType].length > 0) {
    obj.chartData.chartDataList = [];
    obj.chartData.chartDataList = data[obj.dataType].map((i) => {
      const date = new Date(i.timeSeconds * 1000);
      return {
        name: date.toString(),
        value: [date, i.value],
      }
    })
    let { max, min } = setMaxMin(obj.chartData.chartDataList);
    obj.chartData.max = max;
    obj.chartData.min = min;
    obj.domRef.setInfos()
    obj.domRef.getIns().setOption({
      yAxis: {
        max: max,
        min: min,
      },
      grid: { left: (max + "").length * 7 + 25 + "px" },
      series: [{ data: obj.chartData.chartDataList, name: obj.chartData.name }],
    });

  }
}

// 切换图表
export const switchChart = async (key) => {
  diagramData[loodkey.value].chartDataList = [];
  diagramData[loodkey.value].max = 20;
  diagramData[loodkey.value].min = 0;
  unsubscribe("/topic/jsInfo/syrealtimedataeloran/ELORAN-DTZ-YXJCD:" + loodkey.value + ":" + transmitters.value);
  loodkey.value = key
  await getChartData({ dataType: "yxjcd_real", value: diagramData[key].key, chartData: diagramData[key], domRef: objRef.value[key] });
  subscribeChartData({ url: "/topic/jsInfo/syrealtimedataeloran/ELORAN-DTZ-YXJCD:", key: key, chartData: diagramData[key], domRef: objRef.value[key] })
}

// 获取表格的历史数据
export const getTableData = async (info, key) => {
  console.log("getTableData", info, key);
  let dataType = info ? "yxjcd_form" : "fbtyxd_form"
  let keys = info ? diagramData[key].key : "tser"
  const data = await apiAjax.get(`/api/jnx/data/findHistory?startTime=${configure.getTimestampTwoMinutesAgo(1440)}&endTime=${configure.getUtcDate()}&dataType=${dataType}&value=${keys}&offset=1&twSize=10&page=1&pageNum=300&transmitter=${transmitters.value}`);
  return data
};


// 处理comparisonData 这五个数据地方 都是ws 数据 得到主台就在这里来处理
export const homeNavScoket = async () => {
  // 时码比对
  subscribe("/topic/syInfo/SY_TIME_MARK:" + transmitters.value, (data) => {
    console.log("data", data);
    comparisonData.timeFrames = { ...comparisonData.timeFrames, ...data, timeMarkcjyb: handleTime(data.timeMarkcjyb), timeMarkckdq: handleTime(data.timeMarkckdq), state: data.timeMarkbd == 0 ? true : false };
  })
  // DUT1
  subscribe("/topic/syInfo/SY_DUT1:" + transmitters.value, (data) => {
    comparisonData.dut = { ...comparisonData.dut, ...data, state: data.dut1bd == 0 ? true : false }
  })
  // 差分修正量
  subscribe("/topic/syInfo/SY_DIFF_BD:" + transmitters.value, (data) => {
    comparisonData.differential = { ...comparisonData.differential, ...data, state: data.diffCorStatus == 0 ? true : false }
  })
  // 授时电文比对
  subscribe("/topic/syInfo/SY_DWD_RS:" + transmitters.value, (data) => {
    comparisonData.ssdw = { ...comparisonData.ssdw, ...data, state: data.dwdbd == 0 ? true : false, dwdcjyb: handleTime2(data.dwdcjyb), dwdckdq: handleTime2(data.dwdckdq) }
  })

}

// 处理时间  20240530112233 转换为 2024-05-30 11:22:33
const handleTime = (time) => {
  let year = time.slice(0, 4);
  let month = time.slice(4, 6);
  let day = time.slice(6, 8);
  let hour = time.slice(8, 10);
  let minute = time.slice(10, 12);
  let second = time.slice(12, 14);
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
// 处理时间 "12113344422211" 转换为 12 11 33 444 222 111
const handleTime2 = (time) => {
  let year = time.slice(0, 2);
  let month = time.slice(2, 4);
  let day = time.slice(4, 6);
  let hour = time.slice(6, 9);
  let minute = time.slice(9, 12);
  let second = time.slice(12, 14);
  return `${year} ${month} ${day} ${hour} ${minute} ${second}`
}


// 处理五性数据的实时更新
export const sethomeNavScoket = async () => {
  function setValue(wsData) {
    // 更新连续性
    if (wsData.cof !== undefined) {
      const continuityValue = (wsData.cof.toFixed(4) * 100).toFixed(1);
      chartListState.continuity.value = `${continuityValue}%`;
      chartListState.continuity.chartData = continuityChart(continuityValue);
    }

    // 更新可用性
    if (wsData.avail !== undefined) {
      const usabilityValue = (wsData.avail.toFixed(4) * 100).toFixed(1);
      chartListState.usability.value = `${usabilityValue}%`;
      chartListState.usability.chartData = usabilityChart(usabilityValue);
    }

    // 更新准确性
    if (wsData.accuracy !== undefined) {
      chartListState.accuracy.value = wsData.accuracy.toFixed(2);
    }

    // 更新阻断率
    if (wsData.blockingRate !== undefined) {
      const blockingRate = (wsData.blockingRate.toFixed(4) * 1000).toFixed(1)
      console.log("blockingRate", blockingRate);
      chartListState.blockingRate.value = `${blockingRate}‰`;
    }

    // 更新峰值有效功率
    if (wsData.peakPowerRate !== undefined) {
      const powerValue = Math.round(wsData.peakPowerRate * 100);
      chartListState.peakPowerRate.value = `${powerValue}%`;
    }
    if (wsData.complete !== undefined) {
      chartListState.integrity.value = {
        ...wsData.complete,
      }
    }
  }
  let data = await apiAjax.post("/api/jnx/fx/getFxSixData");
  setValue(data[0]);
  subscribe("/topic/fxInfo/FX_SIX_DATA", (wsData) => {
    setValue(wsData);
  });
};

export const setAlarmList = (type) => {
  const obj = {
    FieldStrength: "场强",
    Snr: "信噪比",
    PackageTD: "包周差",
    Tser: "授时偏差",
    Toc: "TOC",
    Toa: "TOA",
    FrequencyTD: "频率偏差",
    Dut1dq: "DUT1当前",
    Dut1yb: "DUT1预报",
    Dwd1: "授时电文1型",
    Dwd2: "授时电文2型",
  }
  return obj[type]
}
