<template>
  <div class="interference">
    <div class="interference_chart">
      <div
        class="chartItem"
        :class="{ noMargin: item == 'cfreq' }"
        v-for="item in Object.keys(chartData)"
        :key="item"
      >
        <lineCharts class="chartAreas" :data="chartData[item].data" />
        <span class="chartNanme">{{ chartData[item].name }}</span>
      </div>
    </div>
    <div class="details">
      <el-card class="card">
        <template #header>
          <div class="card-header">
            <span class="names">干扰数据详情</span>
          </div>
        </template>
        <div class="details_card">
          <lineChart
            class="chartArea"
            ref="interferenceDetailsChart"
            :data="detailsData.chart"
          />
          <div class="tableArea">
            <el-table class="tableHome" :data="detailsData.tableData">
              <el-table-column
                align="center"
                property="monTime"
                label="监测开始时刻"
              />
              <el-table-column
                align="center"
                property="intStart"
                label="干扰起始时刻"
              />
              <el-table-column
                align="center"
                property="intEnd"
                label="干扰结束时刻"
              />
              <el-table-column
                align="center"
                property="upTime"
                label="监测结束时刻"
              />
              <el-table-column
                align="center"
                property="cfreq"
                label="干扰中心频谱"
              />
              <el-table-column
                align="center"
                property="avgPwr"
                label="干扰平均功率"
              />
              <el-table-column
                align="center"
                property="maxPwr"
                label="干扰最大功率"
              />
              <el-table-column
                align="center"
                property="pwrVar"
                label="干扰功率方差"
              />
              <el-table-column
                align="center"
                property="intDur"
                label="干扰时长"
              />
            </el-table>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import lineChart from "@/components/lineChart/lineChart.vue";
import lineCharts from "@/components/lineChart/lineCharts.vue";
import { chartData, detailsData } from "./data";
import { ref, onMounted, onUnmounted } from "vue";
import { subscribe, unsubscribeAll, initScocket } from "@/api/ws";
const interferenceDetailsChart = ref();
onMounted(async () => {
  await initScocket();
  subscribe("/topic/jsInfo/rfirep01/RFI-REP01", setInterfere);
});
onUnmounted(() => {
  unsubscribeAll();
});
function convertTimestampToDate(timestamp, infos = false) {
  // 从时间戳中提取年、月、日、时、分、秒
  const year = parseInt(timestamp.slice(0, 4), 10);
  const month = parseInt(timestamp.slice(4, 6), 10); // 月份是从0开始的，所以要减去1
  const day = parseInt(timestamp.slice(6, 8), 10);
  const hours = parseInt(timestamp.slice(8, 10), 10);
  const minutes = parseInt(timestamp.slice(10, 12), 10);
  const seconds = parseInt(timestamp.slice(12, 14), 10);
  // 创建 Date 对象
  if (infos) {
    return (
      month +
      "月" +
      day +
      "日" +
      " " +
      hours +
      "时" +
      minutes +
      "分" +
      seconds +
      "秒"
    );
  } else {
    return new Date(year, month, day, hours, minutes, seconds);
  }
}

const setInterfere = (data) => {
  let newDatas = JSON.parse(data);
  console.log("newDatas", newDatas);
  if (!Array.isArray(newDatas) || newDatas.length === 0) return;

  const items = newDatas[0];
  console.log("items", items);
  Object.keys(chartData).forEach((i) => {
    chartData[i].data.series[0].data[0].value = Number(items[i]);
  });

  let date = convertTimestampToDate(items.intStart);
  detailsData.chartList.cfreq.push({
    name: convertTimestampToDate(items.intStart, true),
    value: [date, Number(items.cfreq)],
  });
  // detailsData.chartList.avgPwr.push({
  //   name: convertTimestampToDate(items.intStart, true),
  //   value: [date, -Number(items.avgPwr)],
  // });

  if (detailsData.chartList.cfreq.length > 25) {
    // detailsData.chartList.avgPwr.shift();
    detailsData.chartList.cfreq.shift();
  }
  detailsData.chart.series = [
    {
      name: "干扰中心频谱",
      type: "bar",
      stack: "total",
      label: {
        show: true,
        position: "top",
      },
      itemStyle: {
        color: "#1978ff",
      },
      emphasis: {
        focus: "series",
      },
      data: detailsData.chartList.cfreq,
    },
    // {
    //   name: "干扰平均功率",
    //   type: "bar",
    //   stack: "total",
    //   label: {
    //     show: true,
    //     position: "bottom",
    //     formatter: function (params) {
    //       return Math.abs(params.value[1]);
    //     },
    //   },
    //   emphasis: {
    //     focus: "series",
    //   },
    //   itemStyle: {
    //     color: "#0fc596",
    //   },
    //   data: detailsData.chartList.avgPwr,
    // },
  ];
  interferenceDetailsChart.value.setInfos();
  interferenceDetailsChart.value.getIns().setOption(detailsData.chart);
  // interferenceDetailsChart.value.getIns().setOption({
  //   series: [
  //     {
  //       name: "干扰中心频谱",
  //       type: "bar",
  //       stack: "total",
  //       label: {
  //         show: true,
  //         position: "top",
  //       },
  //       itemStyle: {
  //         color: "#1978ff",
  //       },
  //       emphasis: {
  //         focus: "series",
  //       },
  //       data: detailsData.chartList.cfreq,
  //     },
  //     {
  //       name: "干扰平均功率",
  //       type: "bar",
  //       stack: "total",
  //       label: {
  //         show: true,
  //         position: "bottom",
  //         formatter: function (params) {
  //           return Math.abs(params.value[1]);
  //         },
  //       },
  //       emphasis: {
  //         focus: "series",
  //       },
  //       itemStyle: {
  //         color: "#0fc596",
  //       },
  //       data: detailsData.chartList.avgPwr,
  //     },
  //   ],
  // });

  // 处理表格数据
  const processedItem = {
    ...items,
    monTime: convertTimestampToDate(items.monTime, true),
    intStart: convertTimestampToDate(items.intStart, true),
    intEnd: convertTimestampToDate(items.intEnd, true),
    upTime: convertTimestampToDate(items.upTime, true),
  };

  detailsData.tableData = [processedItem, ...detailsData.tableData];
  if (detailsData.tableData.length > 24) {
    detailsData.tableData = detailsData.tableData.slice(0, 24);
  }
};
</script>

<style lang="scss" scoped>
.interference {
  display: flex;
  width: 100%;
  flex-direction: column;
  .interference_chart {
    display: flex;
    width: 100%;
    height: 360px;
    .chartItem {
      flex: 1;
      background: #ffffff;
      border-radius: 2px;
      margin-left: 16px;
      position: relative;
    }
    .chartAreas {
      width: 90%;
      margin: 0 auto;
    }
    .chartNanme {
      position: absolute;
      text-align: center;
      bottom: 35px;
      font-weight: 400;
      font-size: 30px;
      color: rgba(0, 0, 0, 0.9);
      width: 100%;
    }
  }
  .details {
    display: flex;
    flex: 1;
    margin-top: 16px;
    .card {
      width: 100%;
    }
    .details_card {
      .chartArea {
        height: 325px;
      }
      .tableHome {
        height: 300px;
      }
    }
    .chartArea {
      flex: 1.2;
    }
    .tableArea {
      flex: 2;
      margin-left: 32px;
      padding-top: 17px;
      .tableHome {
        :deep(.is-leaf) {
          background: #fafafa;
        }
        :deep(.el-table__cell) {
          padding: 10px 0;
        }
      }
    }
  }
}
.noMargin {
  margin: 0 !important;
}
.card-header {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
