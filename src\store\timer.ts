import { dayjs } from 'element-plus';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs } from 'vue';
import utc from 'dayjs/plugin/utc';
import 'dayjs/locale/zh-cn';
import { SetupStoreId } from '@/enum';
import { getTableData } from '../views/home/<USER>'
let timer: number;
dayjs.locale('zh-cn');
dayjs.extend(utc)

export const useTimer = defineStore(
  SetupStoreId.Timer,
  () => {
    // states
    const data = reactive<any>({
      utcTime: 0,
    });

    // 初始化计时器
    if (!isNaN(timer)) {
      clearInterval(timer);
    }
    data.utcTime = new Date().getTime();
    /* 每秒都必须重新获取最小时间，因为在浏览器非激活状态，setInterval调用间隔会自动变长，
    导致时间不准确 */
    timer = setInterval(() => {
      data.utcTime = new Date().getTime();
      isFullHour()
    }, 1000);

    // getters

    function isFullHour() {
      // 将秒级时间戳转换为毫秒级时间戳
      const millisecondsTimestamp = data.utcTime * 1000;

      // 创建一个 Date 对象
      const date = new Date(millisecondsTimestamp);

      // 获取分钟和秒
      const minutes = date.getMinutes();
      const seconds = date.getSeconds();

      if (minutes === 0 && seconds === 0) {
        // 触发首页的函数
        getTableData()
      }
    }

    const utcTimeValue = computed(() => {
      return dayjs.utc(data.utcTime).format('YYYY年MM月DD日 HH:mm:ss');
    });

    const beijingTimeValue = computed(() => {
      return dayjs(data.utcTime).format('YYYY年MM月DD日 HH:mm:ss dddd');
    });

    const beijingTime1Value = computed(() => {
      return dayjs(data.utcTime).format('HH:mm:ss');
    });

    // actions
    return {
      ...toRefs(data),
      utcTimeValue,
      beijingTimeValue,
      beijingTime1Value,
    };
  }
);
