// echarts-init.ts

import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON>auge<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'echarts/charts';
import {
  DataZoomComponent,
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  VisualMapComponent,
  MarkAreaComponent
} from 'echarts/components';
import <PERSON><PERSON>, { THEME_KEY } from 'vue-echarts';
import { type App, type Plugin } from 'vue';

export const echartsInitPlugin: Plugin = (app: App, ...options: any[]) => {
  use([
    Canvas<PERSON><PERSON>er,
    DatasetComponent,
    Pie<PERSON>hart,
    Lines<PERSON>hart,
    LineChart,
    BarChart,
    TitleComponent,
    Tooltip<PERSON>omponent,
    LegendComponent,
    Grid<PERSON>omponent,
    DataZoomComponent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>lbox<PERSON><PERSON>ponent,
    VisualMap<PERSON><PERSON>ponent,
    MarkAreaComponent
  ]);

  app.provide(THEME_KEY, '');
  // 把echart 组成到
  app.component('v-chart', <PERSON><PERSON>);
};
