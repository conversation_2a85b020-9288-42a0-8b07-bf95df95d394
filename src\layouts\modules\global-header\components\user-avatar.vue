<script setup lang="ts">
import { computed } from "vue";
import type { VNode } from "vue";
import { useAuthStore } from "@/store/modules/auth";
import { useRouterPush } from "@/hooks/common/router";
import { useSvgIcon } from "@/hooks/common/icon";
import { $t } from "@/locales";
import { dispose } from "../../../../api/ws";
defineOptions({
  name: "UserAvatar",
});

const authStore = useAuthStore();
const { routerPushByKey, toLogin } = useRouterPush();
const { SvgIconVNode } = useSvgIcon();

function loginOrRegister() {
  toLogin();
}

type DropdownKey = "user-center" | "logout";

type DropdownOption =
  | {
      key: DropdownKey;
      label: string;
      icon?: () => VNode;
    }
  | {
      type: "divider";
      key: string;
    };

const options = computed(() => {
  const opts: DropdownOption[] = [
    // {
    //   label: $t("common.userCenter"),
    //   key: "user-center",
    //   icon: SvgIconVNode({ icon: "ph:user-circle", fontSize: 18 }),
    // },
    // {
    //   type: "divider",
    //   key: "divider",
    // },
    {
      label: "退出登录",
      key: "logout",
      icon: SvgIconVNode({ localIcon: "SignOut20Regular", fontSize: 18 }),
    },
  ];

  return opts;
});

function logout() {
  window.$dialog?.info({
    title: "提示",
    content: "确认退出登录？",
    positiveText: "确认",
    negativeText: "取消",
    onPositiveClick: () => {
      authStore.resetStore();
      dispose();
    },
  });
}

function handleDropdown(key: any) {
  if (key === "logout") {
    logout();
  } else {
    routerPushByKey(key);
  }
}
</script>

<template>
  <NButton
    class="font-style"
    v-if="!authStore.isLogin"
    quaternary
    @click="loginOrRegister"
  >
    {{ $t("page.login.common.loginOrRegister") }}
  </NButton>
  <NDropdown
    v-else
    placement="bottom"
    trigger="click"
    :options="options"
    @select="handleDropdown"
  >
    <div>
      <ButtonIcon>
        <SvgIcon icon="ph:user-circle" class="text-icon-large" />
        <span class="text-16px font-medium font-style">{{
          authStore.userInfo.userName
        }}</span>
      </ButtonIcon>
    </div>
  </NDropdown>
</template>

<style lang="scss" scoped>
.font-style {
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255);
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
</style>
