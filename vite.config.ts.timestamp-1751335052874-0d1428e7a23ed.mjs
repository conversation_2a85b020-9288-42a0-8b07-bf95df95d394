// vite.config.ts
import process3 from "node:process";
import { URL, fileURLToPath } from "node:url";
import { defineConfig, loadEnv } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite@5.3.1_@types+node@20.14.8_sass@1.77.6/node_modules/vite/dist/node/index.js";

// build/plugins/index.ts
import vue from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import VueDevtools from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.4_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import progress from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-progress@0.0.7_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-progress/dist/index.mjs";

// build/plugins/router.ts
import ElegantVueRouter from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@elegant-router+vue@0.3.7/node_modules/@elegant-router/vue/dist/vite.mjs";
function setupElegantRouter() {
  return ElegantVueRouter({
    layouts: {
      base: "src/layouts/base-layout/index.vue",
      blank: "src/layouts/blank-layout/index.vue"
    },
    customRoutes: {
      names: [
        "exception_403",
        "exception_404",
        "exception_500",
        "document_project",
        "document_project-link",
        "document_vue",
        "document_vite",
        "document_naive",
        "document_antd"
      ]
    },
    routePathTransformer(routeName, routePath) {
      const key = routeName;
      if (key === "login") {
        const modules = ["pwd-login", "code-login", "register", "reset-pwd", "bind-wechat"];
        const moduleReg = modules.join("|");
        return `/login/:module(${moduleReg})?`;
      }
      return routePath;
    },
    onRouteMetaGen(routeName) {
      const key = routeName;
      const constantRoutes = ["login", "403", "404", "500"];
      const meta = {
        title: key
      };
      if (constantRoutes.includes(key)) {
        meta.constant = true;
      }
      return meta;
    }
  });
}

// build/plugins/unocss.ts
import process from "node:process";
import path from "node:path";
import unocss from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@unocss+vite@0.61.0_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/@unocss/vite/dist/index.mjs";
import presetIcons from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@unocss+preset-icons@0.61.0/node_modules/@unocss/preset-icons/dist/index.mjs";
import { FileSystemIconLoader } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/@iconify+utils@2.1.25/node_modules/@iconify/utils/lib/loader/node-loaders.mjs";
function setupUnocss(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path.join(process.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  return unocss({
    presets: [
      presetIcons({
        prefix: `${VITE_ICON_PREFIX}-`,
        scale: 1,
        extraProperties: {
          display: "inline-block"
        },
        collections: {
          [collectionName]: FileSystemIconLoader(
            localIconPath,
            (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
          )
        },
        warn: true
      })
    ]
  });
}

// build/plugins/unplugin.ts
import process2 from "node:process";
import path2 from "node:path";
import Icons from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/resolver.js";
import Components from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/vite.js";
import { AntDesignVueResolver, NaiveUiResolver } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/resolvers.js";
import { FileSystemIconLoader as FileSystemIconLoader2 } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/loaders.js";
import { createSvgIconsPlugin } from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function setupUnplugin(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path2.join(process2.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  const plugins = [
    Icons({
      compiler: "vue3",
      customCollections: {
        [collectionName]: FileSystemIconLoader2(
          localIconPath,
          (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        )
      },
      scale: 1,
      defaultClass: "inline-block"
    }),
    Components({
      dts: "src/typings/components.d.ts",
      types: [{ from: "vue-router", names: ["RouterLink", "RouterView"] }],
      resolvers: [
        AntDesignVueResolver({
          importStyle: false
        }),
        NaiveUiResolver(),
        IconsResolver({ customCollections: [collectionName], componentPrefix: VITE_ICON_PREFIX })
      ]
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: "body-last",
      customDomId: "__SVG_ICON_LOCAL__"
    })
  ];
  return plugins;
}

// build/plugins/html.ts
function setupHtmlPlugin(buildTime) {
  const plugin = {
    name: "html-plugin",
    apply: "build",
    transformIndexHtml(html) {
      return html.replace("<head>", `<head>
    <meta name="buildTime" content="${buildTime}">`);
    }
  };
  return plugin;
}

// build/plugins/index.ts
function setupVitePlugins(viteEnv, buildTime) {
  const plugins = [
    vue({
      script: {
        defineModel: true
      }
    }),
    vueJsx(),
    VueDevtools(),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime)
  ];
  return plugins;
}

// build/config/time.ts
import dayjs from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/dayjs.min.js";
import utc from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/utc.js";
import timezone from "file:///D:/jnx/single-station-pc/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/timezone.js";
function getBuildTime() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const buildTime = dayjs.tz(Date.now(), "Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");
  return buildTime;
}

// vite.config.ts
var __vite_injected_original_import_meta_url = "file:///D:/jnx/single-station-pc/vite.config.ts";
var vite_config_default = defineConfig((configEnv) => {
  const viteEnv = loadEnv(
    configEnv.mode,
    process3.cwd()
  );
  const buildTime = getBuildTime();
  const baseUrl = viteEnv.VITE_SERVICE_BASE_URL;
  const logApi = viteEnv.VITE_SERVICE_LONGS_URL;
  const proxy = {
    "/Api": {
      target: `${baseUrl}`,
      changeOrigin: true,
      rewrite: (path3) => path3.replace("/Api", "")
    },
    "/logApi": {
      target: `${logApi}`,
      changeOrigin: true,
      rewrite: (path3) => path3.replace("/logApi", "")
    }
  };
  console.log(proxy);
  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        "~": fileURLToPath(new URL("./", __vite_injected_original_import_meta_url)),
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    plugins: setupVitePlugins(viteEnv, buildTime),
    define: {
      BUILD_TIME: JSON.stringify(buildTime)
    },
    // server: {
    //   host: '0.0.0.0',
    //   port: 9527,
    //   open: true,
    //   proxy: createViteProxy(viteEnv, configEnv.command === 'serve'),
    //   fs: {
    //     cachedChecks: false
    //   }
    // },
    server: {
      host: "0.0.0.0",
      proxy,
      port: 3e3
    },
    preview: {
      port: 9725
    },
    build: {
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP === "Y",
      commonjsOptions: {
        ignoreTryCatch: false
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
