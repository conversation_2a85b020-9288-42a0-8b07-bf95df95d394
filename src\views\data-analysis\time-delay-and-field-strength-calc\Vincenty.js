// 将角度转换为弧度
function toRadians(degrees) {
  return degrees * Math.PI / 180;
}

// 计算两点之间的大圆距离和方位角
export function calcDistanceBearing(p1, p2) {
  const a = 6378137; // 长半轴 (WGS84)
  const b = 6356752.314245; // 短半轴 (WGS84)
  const f = 1 - b / a; // 扁率

  // 将经纬度转换为弧度
  const X1 = toRadians(p1.y); // 纬度
  const Y1 = toRadians(p1.x); // 经度
  const X2 = toRadians(p2.y); // 纬度
  const Y2 = toRadians(p2.x); // 经度

  // 计算辅助变量
  const tan_U1 = (1 - f) * Math.tan(X1);
  const cos_U1 = 1 / Math.sqrt(1 + tan_U1 * tan_U1);
  const sin_U1 = tan_U1 * cos_U1;

  const tan_U2 = (1 - f) * Math.tan(X2);
  const cos_U2 = 1 / Math.sqrt(1 + tan_U2 * tan_U2);
  const sin_U2 = tan_U2 * cos_U2;

  // 初始化变量
  const threshold = 1e-12;
  let L = Y2 - Y1;
  let lamda = L;
  let lamda1 = 0;
  let diff = Math.abs(lamda - lamda1);

  // 迭代计算 lambda
  let sin_sigma, cos_sigma, sigma, sin_gama, cos_gama2, cos_2sigmam;
  while (diff > threshold) {
    sin_sigma = Math.sqrt(Math.pow(cos_U2 * Math.sin(lamda), 2) + Math.pow(cos_U1 * sin_U2 - sin_U1 * cos_U2 * Math.cos(lamda), 2));
    cos_sigma = sin_U1 * sin_U2 + cos_U1 * cos_U2 * Math.cos(lamda);
    sigma = Math.atan2(sin_sigma, cos_sigma);
    sin_gama = cos_U1 * cos_U2 * Math.sin(lamda) / sin_sigma;
    cos_gama2 = 1 - Math.pow(sin_gama, 2);
    cos_2sigmam = cos_sigma - 2*sin_U1 * sin_U2 / cos_gama2;
    const C = (f / 16) * cos_gama2 * (4 + f * (4 - 3 * cos_gama2));
    lamda1 = lamda;
    lamda = L + f * (1 - C) * sin_gama * (sigma + C * sin_sigma * (cos_2sigmam + C * cos_sigma * (2 * Math.pow(cos_2sigmam, 2) - 1)));
    diff = Math.abs(lamda - lamda1);
  }

  // 计算大圆距离
  const e2 = 1 - Math.pow(b / a, 2);
  const u2 = e2 * cos_gama2 / (1 - e2);
  const A = 1 + (u2 / 16384) * (4096 + u2 * (-768 + u2 * (320 - 175 * u2)));
  const B = (u2 / 1024) * (256 + u2 * (-128 + u2 * (74 - 47 * u2)));
  const sigma1 = B * sin_sigma * (cos_2sigmam + B / 4 * (cos_sigma * (-1 + 2 * Math.pow(cos_2sigmam, 2)) - B / 6 * cos_2sigmam * (-3 + 4 * Math.pow(sin_sigma, 2)) * (-3 + 4 * Math.pow(cos_2sigmam, 2))));
  const s = b * A * (sigma - sigma1);

  // 计算方位角
  const a12 = Math.atan2(cos_U2 * Math.sin(lamda), cos_U1 * sin_U2 - sin_U1 * cos_U2 * Math.cos(lamda));
  const a21 = Math.atan2(cos_U1 * Math.sin(lamda), -sin_U1 * cos_U2 + cos_U1 * sin_U2 * Math.cos(lamda));

  // // 返回结果
  // return {
  //   distance: s, // 大圆距离（米）
  //   bearing12: toRadians(a12), // 从 p1 到 p2 的方位角（弧度）
  //   bearing21: toRadians(a21), // 从 p2 到 p1 的方位角（弧度）
  // };
  return s / 1000 + "KM";
}

// // 示例调用
// let p1 = { x: 109.2216666667, y: 34.36833333333 };
// let p2 = { x: 129.10756, y: 42.71988 };
// const result = calcDistanceBearing(p1, p2);

// console.log('两点之间的大圆距离为：', result.distance, '米');
// console.log('从 p1 到 p2 的方位角为：', result.bearing12, '弧度');
// console.log('从 p2 到 p1 的方位角为：', result.bearing21, '弧度');
