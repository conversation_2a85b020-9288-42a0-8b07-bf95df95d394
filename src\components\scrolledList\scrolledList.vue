<template>
  <vue3-seamless-scroll
    class="v-s-s"
    :list="list"
    :step="1.1"
    :singleLine="false"
    :singleHeight="21"
    :singleWaitTime="1000"
    :hover="true"
    :count="1"
  >
    <div class="v-s-s_item" v-for="(item, index) in list" :key="index">
      <a>{{ item.title }}</a>
      <span>{{ item.date }}</span>
    </div>
  </vue3-seamless-scroll>
</template>

<script>
import { ref, reactive, toRefs, onMounted } from "vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";

export default {
  components: {
    Vue3SeamlessScroll,
  },
  setup() {
    const state = reactive({
      list: [
        { title: "无缝滚动第一行无缝滚动第一行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第二行无缝滚动第二行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第三行无缝滚动第三行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第四行无缝滚动第四行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第五行无缝滚动第五行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第六行无缝滚动第六行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第七行无缝滚动第七行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第八行无缝滚动第八行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第九行无缝滚动第九行", date: "2023-5-10 18:09:22" },
        { title: "无缝滚动第九行无缝滚动第十行", date: "2023-5-10 18:09:22" },
      ],
    });
    const index = ref(1);
    const setData = () => {
      if (index.value < 5) {
        index.value++;
        setTimeout(() => {
          state.list.push({
            title: "我是新增的一条数据",
            date: Date.now(),
          });
        }, 100);
      }
    };
    onMounted(() => {
      setData();
    });

    return {
      ...toRefs(state),
    };
  },
};
</script>

<style lang="scss" scoped>
.v-s-s {
  height: 200px;
  width: 500px;
  margin: 100px auto;
  overflow: hidden;
  font-size: 13px;

  .v-s-s_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3px 0;
  }
}
</style>
