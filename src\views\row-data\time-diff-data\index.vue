<template>
  <div class="con">
    <div class="top-bar-con">
      <div class="top-bar">
        <el-button-group>
          <el-button
            color="#0052D9"
            :plain="btnIndex === 0 ? false : true"
            @click="() => onBtnGroupClick(0)"
            >原始数据</el-button
          >
          <el-button
            color="#0052D9"
            :plain="btnIndex === 1 ? false : true"
            @click="() => onBtnGroupClick(1)"
            >优选数据</el-button
          >
        </el-button-group>
      </div>
    </div>
    <div>
      <raw v-if="btnIndex === 0" />
      <preferred v-else-if="btnIndex === 1" />
    </div>
  </div>
</template>

<script setup>
import { onUnmounted, ref } from "vue";
import preferred from "./preferred.vue";
import raw from "./raw.vue";

import { unsubscribeAll } from "@/api/ws";
onUnmounted(() => {
  unsubscribeAll();
});
const btnIndex = ref(0);

const onBtnGroupClick = (index) => {
  btnIndex.value = index;
};
</script>

<style lang="scss" scoped>
.con {
  .top-bar-con {
    position: relative;
  }

  .top-bar {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
  }
}
</style>
