<script setup lang="ts">
import { $t } from "@/locales";
import LogoIcon from "@/assets/imgs/logo.png";
import LogoTitleIcon from "@/assets/imgs/logo_title.png";
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
defineOptions({
  name: "GlobalLogo",
});

interface Props {
  /** Whether to show the title */
  showTitle?: boolean;
}

withDefaults(defineProps<Props>(), {
  showTitle: true,
});
</script>

<template>
  <RouterLink to="/" class="w-full flex-center nowrap-hidden">
    <div class="con">
      <div class="top-con">
        <img :src="LogoIcon" class="img"/>
        <img
          v-show="showTitle"
          class="title transition duration-300 ease-in-out"
          :src="LogoTitleIcon"
          alt="单台站性能评估系统"
        />
      </div>
      <div
        v-show="showTitle"
        class="bottom-text transition duration-300 ease-in-out"
      >
      {{ proxy.configure.stationAllLoginLong}}
      </div>
    </div>
  </RouterLink>
</template>

<style lang="scss" scoped>
.con {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .top-con {
    display: flex;
    flex-direction: row;
    align-items: center;

    .img {
      width: 32px;
      height: 32px;
      margin-right: 9px;
    }
    .title {
      height: 15px;
    }
  }
  .bottom-text {
    margin-top: 4px;
    font-weight: 400;
    font-size: 15px;
    color: rgba(0,0,0,0.65);
    line-height: 21px;
    letter-spacing: 7px;
    text-align: left;
    font-style: normal;
  }
}
</style>
