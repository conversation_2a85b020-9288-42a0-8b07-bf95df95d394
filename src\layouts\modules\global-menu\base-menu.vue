<script setup lang="ts">
import { computed, onMounted, ref, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import type { MentionOption, MenuOption, MenuProps } from "naive-ui";
import { SimpleScrollbar } from "@sa/materials";
import type { RouteKey } from "@elegant-router/types";
import { useAppStore } from "@/store/modules/app";
import { useThemeStore } from "@/store/modules/theme";
import { useRouteStore } from "@/store/modules/route";
import { useRouterPush } from "@/hooks/common/router";

defineOptions({
  name: "BaseMenu",
});

interface Props {
  darkTheme?: boolean;
  mode?: MenuProps["mode"];
  menus: App.Global.Menu[];
}

const props = withDefaults(defineProps<Props>(), {
  mode: "vertical",
});

const route = useRoute();
const appStore = useAppStore();
const themeStore = useThemeStore();
const routeStore = useRouteStore();
const { routerPushByKey } = useRouterPush();

const naiveMenus = computed(() => props.menus as unknown as MentionOption[]);

const isHorizontal = computed(() => props.mode === "horizontal");

const siderCollapse = computed(
  () => themeStore.layout.mode === "vertical" && appStore.siderCollapse
);

const headerHeight = computed(() => `${themeStore.header.height}px`);

const selectedKey = computed(() => {
  const { hideInMenu, activeMenu } = route.meta;
  const name = route.name as string;

  const routeName = (hideInMenu ? activeMenu : name) || name;

  return routeName;
});

const expandedKeys = ref<string[]>([]);

function updateExpandedKeys() {
  if (isHorizontal.value || siderCollapse.value || !selectedKey.value) {
    expandedKeys.value = [];
    return;
  }
  expandedKeys.value = routeStore.getSelectedMenuKeyPath(selectedKey.value);
}

function handleClickMenu(key: RouteKey) {
  const query = routeStore.getRouteQueryOfMetaByKey(key);

  routerPushByKey(key, { query });
}

watch(
  () => route.name,
  () => {
    updateExpandedKeys();
  },
  { immediate: true }
);

const renderMenuIcon = (option: MenuOption) => {
  if (
    option &&
    typeof option.key === "string" &&
    option.key.indexOf("_") === -1 &&
    option.icon
  )
    return option.icon();
  return null;
};

onMounted(() => {
  const menuDom: any = document.querySelector(".simplebar-content .n-menu");
  if (!menuDom) return;

  // nextTick(()=>{
  //   let iconify =document.querySelectorAll(".n-menu-item-content__icon")
  //   iconify.forEach((i)=>{
  //     i.innerHTML='<span>qq</span>'
  //   })
  // })
  menuDom.style.setProperty("--n-border-radius", "3px");
  menuDom.style.setProperty("--n-item-height", "36px");
  menuDom.style.setProperty("--n-item-color-active", "#1f87ff");
});
</script>

<template>
  <SimpleScrollbar>
    <NMenu
      v-model:expanded-keys="expandedKeys"
      :mode="mode"
      :value="selectedKey"
      :collapsed="siderCollapse"
      :collapsed-width="themeStore.sider.collapsedWidth"
      :collapsed-icon-size="22"
      :options="naiveMenus"
      :inverted="darkTheme"
      :indent="18"
      responsive
      accordion
      @update:value="handleClickMenu"
      :render-icon="renderMenuIcon"
    />
  </SimpleScrollbar>
</template>

<style scoped>
:deep(.n-menu--horizontal) {
  --n-item-height: v-bind(headerHeight) !important;
}
:deep(.n-menu-item-content--selected) .n-menu-item-content-header {
  color: #fff !important;
}
:deep(.n-menu-item-content--selected) .n-menu-item-content__icon {
  color: #fff !important;
}
</style>
<style>
.n-menu
  .n-menu-item-content:not(
    .n-menu-item-content--disabled
  ).n-menu-item-content--selected:hover::before {
  background: hsla(212, 100%, 50%, 0.692) !important;
}
.n-menu.n-menu--collapsed
  .n-menu-item-content.n-menu-item-content--selected::before {
  background: hsla(212, 100%, 50%, 0.692) !important;
}
</style>
