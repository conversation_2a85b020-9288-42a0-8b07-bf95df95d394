import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs, watch } from 'vue';
import { getToken } from '@/store/modules/auth/shared';
import { useParamsManage } from './modules/params-manage';
import { router } from '@/router';
import { ElMessage } from 'element-plus';

// timerGap时间内没有鼠标移动和键盘输入，则进入锁屏状态
export const useScreenLock = defineStore(
  SetupStoreId.ScreenLock,
  () => {
    // states
    const data = reactive({
      isLocked: false, // 是否在锁屏状态
      timerGap: 0, //锁屏倒计时ms
      inputPwd: '', //用户输入的锁屏密码
    });

    // getters

    // actions
    const onMouseDown = () => {
      restartTimer();
    }

    const onMouseMove = () => {
      restartTimer();
    }

    const onKeyDown = () => {
      restartTimer();
    }

    const onWheel = () => {
      restartTimer();
    }

    const addListeners = () => {
      removeListeners();

      document.addEventListener("mousedown", onMouseDown);
      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("wheel", onWheel);
      document.addEventListener("keydown", onKeyDown);
    }

    const removeListeners = () => {
      document.removeEventListener("mousedown", onMouseDown);
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("wheel", onWheel);
      document.removeEventListener("keydown", onKeyDown);
    }

    const init = () => {
      addListeners();
    }

    const dispose = () => {
      removeListeners();
      clearTimeout(window.screenLockTimer);
      window.screenLockTimer = null;
      data.inputPwd = '';
    }

    // 重新开始计时
    const restartTimer = () => {
      clearTimeout(window.screenLockTimer);

      let hasToken = getToken();
      if (!hasToken || data.isLocked || !data.timerGap) {
        return;
      }

      window.screenLockTimer = setTimeout(() => {
        // // 显示锁屏遮罩
        // data.isLocked = true;
        // // 跳转到首页
        // router.push('/home');
      }, data.timerGap);
    }

    const paramsManageStore = useParamsManage();
    watch(() => paramsManageStore.form3.sreenLockWaitTime, (val) => {
      if(paramsManageStore.form3.pwd) {
        data.timerGap = Number(val) * 60 * 1000;
        restartTimer();
      }
    });

    watch(() => paramsManageStore.form3.pwd, (val) => {
      if(val) {
        init();

        data.timerGap = Number(paramsManageStore.form3.sreenLockWaitTime) * 60 * 1000;
        restartTimer();
      } else {
        dispose();
      }
    }, {
      immediate: true
    });

    watch(() => data.isLocked, (val) => {
      if(val) {
        dispose();
      } else {
        init();
      }
    }, {
      immediate: true
    });

    const checkPwd = () => {
      if(data.inputPwd === '') {
        ElMessage.error('请输入密码.');
      } else if(data.inputPwd === paramsManageStore.form3.sreenLockPwd) {
        data.isLocked = false;
        ElMessage.success('欢迎回来.');
      } else {
        ElMessage.error('密码错误.');
      }
    };

    return {
      ...toRefs(data),
      init,
      dispose,
      restartTimer,
      checkPwd,
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: true,
  }
);
