<template>
  <div class="preprocessing">
    <div class="preprocessing_nav">
      <span class="title">时间选择</span>
      <div>
        <el-date-picker v-model="selects.value4" type="datetimerange" range-separator="至" start-placeholder="开始时间"
          end-placeholder="结束时间" :disabled-date="disabledDate" />
      </div>
      <el-select v-model="filterData.select" placeholder="请选择" class="select">
        <el-option key="CH1" label="CH1" value="CH1"></el-option>
        <el-option key="CH2" label="CH2" value="CH2"></el-option>
        <el-option key="CH3" label="CH3" value="CH3"></el-option>
        <el-option key="CH4" label="CH4" value="CH4"></el-option>
      </el-select>
      <div :class="filterData.select === 'CH1'
        ? 'select-label'
        : 'hide-select-label'
        ">
        BPL-6000M
      </div>
      <el-button type="primary" class="btn">计算</el-button>
      <el-button type="primary" class="btn">导出</el-button>
      <span class="state_mode">
        <div :class="{ hand: true, stateInfo: dataPrice.statemode == 'hand' }" @click="setEmode('hand')">
          手动模式
        </div>
        <div :class="{
          automatic: true,
          stateInfo: dataPrice.statemode == 'automatic',
        }" @click="setEmode('automatic')">
          自动模式
        </div>
      </span>
    </div>
    <el-card class="card" v-for="key in Object.keys(diagramData)" :key="key">
      <template #header>
        <div class="card-header">
          <span>{{ diagramData[key].name }}</span>
        </div>
      </template>
      <div class="data">
        <lineChart class="chartArea" :data="diagramData[key].data.ChartData" />
        <el-table :data="diagramData[key].data.tableData" class="tableHome">
          <el-table-column prop="date" label="时间" />
          <el-table-column prop="name" label="偏差" />
          <el-table-column prop="name" label="最大值" />
          <el-table-column prop="name" label="最小值" />
          <el-table-column prop="name" label="均值" />
          <el-table-column prop="name" label="标准偏差" />
          <el-table-column prop="name" label="RMS" />
          <el-table-column prop="name" label="95%分位数" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { diagramData } from "./data";
import lineChart from "@/components/lineChart/lineChart.vue";

const filterData = reactive({
  select: "CH1",
});
const dataPrice = reactive({
  statemode: "hand", //hand手动模式 automatic自动模式
});
//切换按钮
const setEmode = (data) => {
  dataPrice.statemode = data;
};
const selects = reactive({
  value4: [new Date(2022, 1, 1), new Date(2022, 1, 31)],
});
</script>

<style lang="scss" scoped>
.preprocessing {
  display: flex;
  flex-direction: column;

  .preprocessing_nav {
    padding: 24px;
    background-color: #fff;
    display: flex;
    align-items: center;

    .select {
      width: 162px;
      margin-left: 10px;
    }

    .select-label {
      margin-left: 6px;
      margin-right: 10px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }

    .hide-select-label {
      display: none;
    }

    .title {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
      margin-right: 6px;
    }

    .btn {
      margin-left: 8px;
    }

    .state_mode {
      display: flex;
      margin-left: 16px;
      border: 1px solid #0052d9;
      border-radius: 6px;
      overflow: hidden;

      .hand,
      .automatic {
        width: 88px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        color: #0052d9;
        cursor: pointer;
      }

      .stateInfo {
        background: #0052d9;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

.card {
  width: 100%;
  margin-top: 16px;
  background: #ffffff;
  border-radius: 2px;

  .card-header {
    font-weight: 600;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
  }

  .data {
    display: flex;
    padding-bottom: 20px;

    .chartArea {
      flex: 1;
      height: 300px;

    }

    .tableHome {
      flex: 1;
      margin-left: 16px;

      :deep(.el-table__row) {
        height: 60px;
      }
    }
  }
}

.tableHome {
  .view {
    font-size: 14px;
    color: #0052d9;
  }

  :deep(.is-leaf) {
    background: #fafafa;
  }

  :deep(.el-table__cell) {
    padding: 10px 0;
  }
}
</style>
<style>
.synthesize_tooltip {
  color: #266fe8 !important;
}
</style>
