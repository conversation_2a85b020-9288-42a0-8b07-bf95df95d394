import type { ElegantConstRoute, ElegantRoute } from '@elegant-router/types';
import { generatedRoutes } from '../elegant/routes';
import { layouts, views } from '../elegant/imports';
import { transformElegantRoutesToVueRoutes } from '../elegant/transform';

/**
 * custom routes
 *
 * @link https://github.com/soybeanjs/elegant-router?tab=readme-ov-file#custom-route
 */
const customRoutes: any[] = [
  // {
  //   name: 'exception',
  //   path: '/exception',
  //   component: 'layout.base',
  //   meta: {
  //     title: '报错页面',
  //     icon: 'ant-design:exception-outlined',
  //     order: 7
  //   },
  //   children: [
  //     {
  //       name: 'exception_403',
  //       path: '/exception/403',
  //       component: 'view.403',
  //       meta: {
  //         title: '报错403',
  //         icon: 'ic:baseline-block'
  //       }
  //     },
  //     {
  //       name: 'exception_404',
  //       path: '/exception/404',
  //       component: 'view.404',
  //       meta: {
  //         title: '404',
  //         icon: 'ic:baseline-web-asset-off'
  //       }
  //     },
  //     {
  //       name: '500',
  //       path: '/exception/500',
  //       component: 'view.500',
  //       meta: {
  //         title: '500',
  //         icon: 'ic:baseline-wifi-off'
  //       }
  //     }
  //   ]
  // }
];

/** create routes when the auth route mode is static */
export function createStaticRoutes() {
  const constantRoutes: ElegantRoute[] = [];

  const authRoutes: ElegantRoute[] = [];
  [...generatedRoutes, ...customRoutes].forEach(item => {
    if (item.meta?.constant) {
      constantRoutes.push(item);
    } else {
      authRoutes.push(item);
    }
  });

  return {
    constantRoutes,
    authRoutes
  };
}

/**
 * Get auth vue routes
 *
 * @param routes Elegant routes
 */
export function getAuthVueRoutes(routes: ElegantConstRoute[]) {
  return transformElegantRoutesToVueRoutes(routes, layouts, views);
}
