import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs, watch } from 'vue';
import apiAjax from "@/api/index";
import configure from '@/utils/configure'
import { initScocket, subscribe, unsubscribe } from "@/api/ws";
export const useTimeDiffData = defineStore(
  SetupStoreId.TimeDiffData,
  () => {
    // states
    const diffData = reactive({
      station: null,
      transmitterList: [],
      rawData: {
        navList: {
          fieldStrength: {
            name: "场强",
            key: "fieldstrength",
            unit: "dBμV/m",
          },
          snr: {
            name: "信噪比",
            key: "snr",
            unit: "dB"
          },
          packageTD: {
            name: "包周差",
            key: "packagetd",
            unit: "μs",
          },
          frequencyTD: {
            name: "频率偏差",
            key: "frequencytd",
            unit: "Hz",
          }
        },
        rawRef: null,
        chartData: [],
        tableData: [],
        activeName: "fieldStrength",
      },// 原始数据的图形的值
      receeiveData: {
        navList: {
          fieldStrength: {
            name: "场强",
            key: "fieldstrength",
            unit: "dBμV/m",
          },
          snr: {
            name: "信噪比",
            key: "snr",
            unit: "dB"
          },
          packageTD: {
            name: "包周差",
            key: "packagetd",
            unit: "μs",
          },
          frequencyTD: {
            name: "频率偏差",
            key: "frequencytd",
            unit: "Hz",
          }
        },
        rawRef: null,
        chartData: [],
        tableData: [],
        activeName: "fieldStrength",
      },// 优选数据的的图形的值
    });

    const init = async () => {
      if (diffData.station) {
        return
      }
      const data = await apiAjax.post("/api/jnx/dataSimplSc/findAllTransmitter");
      if (data.length > 0) {
        diffData.station = data[0];
        diffData.transmitterList = data.map(item => {
          return {
            value: item,
            label: configure.thoroughfare(item)
          }
        })
      }
    }


    // 原始数据初始化
    const initRawData = async (rawRef) => {
      await init();
      diffData.rawData.rawRef = rawRef;
      await getChartData(diffData.rawData.activeName);
    }


    // 原始数据点击tab
    const handleRawClick = async (val) => {
      console.log(val);
      await getChartData(val);
    }

    // 原始数据获取图形数据
    const getChartData = async (val) => {
      if (!diffData.station) {
        return
      }
      const value = diffData.rawData.navList[val].key;
      const data = await apiAjax.get(`/api/jnx/data/findHistory?startTime=${configure.getTimestampTwoMinutesAgo(1440)}&endTime=${configure.getUtcDate()}&dataType=fbjcd_real&value=${value}&offset=1&twSize=10&page=1&pageNum=300&transmitter=${diffData.station}`);
      console.log("data", data);

    }

    const selectChangeHandle = () => {
      console.log(diffData.station);
    }

    return {
      ...toRefs(diffData),
      init,
      selectChangeHandle,
      initRawData,
      handleRawClick
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  }
);
